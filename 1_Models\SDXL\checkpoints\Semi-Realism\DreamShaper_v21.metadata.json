{"file_name": "DreamShaper_v21", "model_name": "DreamShaper XL", "file_path": "D:/stable_diffusion/1_Models/SDXL/checkpoints/Semi-Realism\\DreamShaper_v21.safetensors", "size": 6939220250, "modified": 1749326064.5943644, "sha256": "4496b36d48bfd7cfe4e5dbce3485db567bcefa2bef7238d290dbd45612125083", "base_model": "SDXL 1.0", "preview_url": "D:/stable_diffusion/1_Models/SDXL/checkpoints/Semi-Realism\\DreamShaper_v21.webp", "preview_nsfw_level": 1, "notes": "", "from_civitai": true, "civitai": {"id": 351306, "modelId": 112902, "name": "v2.1 Turbo DPM++ SDE", "createdAt": "2024-02-19T17:38:14.056Z", "updatedAt": "2025-02-14T21:42:50.940Z", "status": "Published", "publishedAt": "2024-02-19T17:52:25.977Z", "trainedWords": [], "trainingStatus": null, "trainingDetails": null, "baseModel": "SDXL Turbo", "baseModelType": "Standard", "earlyAccessEndsAt": null, "earlyAccessConfig": {}, "description": "<p>Turbo version should be used at <u>CFG scale 2</u> and with around 4<u>-8 sampling steps</u>. Should work with <strong><u>DPM++ SDE Karras (or Normal)</u></strong>. Comparison of other samplers: <a target=\"_blank\" rel=\"ugc\" href=\"https://civitai.com/posts/951781\">https://civitai.com/posts/951781</a></p><p>Can be used for highres fix and tiled upscaling. Please check the examples.</p><p><strong>If you train on this, make sure to use <u>DPM++ SDE</u>.</strong><br /><br /><strong>What's new in 2.1</strong><br />Fixes a skin issue that sometimes occurred in v1 and v2. I used targeted adversarial learning for it. <br />The problem didn't affect SFW versions of the model, so I won't update that one. <br />It should have little to no effect on most images compared to v2, except other minor improvements on anime stuff apparently (which was totally collateral).</p>", "uploadType": "Created", "usageControl": "Download", "air": "urn:air:sdxl:checkpoint:civitai:112902@351306", "stats": {"downloadCount": 217887, "ratingCount": 331, "rating": 4.99, "thumbsUpCount": 5570}, "model": {"name": "DreamShaper XL", "type": "Checkpoint", "nsfw": false, "poi": false}, "files": [{"id": 279836, "sizeKB": 6776582.275390625, "name": "dreamshaperXL_v21TurboDPMSDE.safetensors", "type": "Model", "pickleScanResult": "Success", "pickleScanMessage": "No Pickle imports", "virusScanResult": "Success", "virusScanMessage": null, "scannedAt": "2024-02-19T17:58:47.792Z", "metadata": {"format": "SafeTensor", "size": "full", "fp": "fp16"}, "hashes": {"AutoV1": "488F085E", "AutoV2": "4496B36D48", "SHA256": "4496B36D48BFD7CFE4E5DBCE3485DB567BCEFA2BEF7238D290DBD45612125083", "CRC32": "3FFAE079", "BLAKE3": "39D4E86885924311D47475838B8E10EDB7A33C780934E9FC9D38B80C27CB658E", "AutoV3": "76D10920FDFF"}, "primary": true, "downloadUrl": "https://civitai.com/api/download/models/351306"}], "images": [{"url": "https://image.civitai.com/xG1nkqKTMzGDvpLrqFT7WA/fb00718b-227c-462c-ad03-1371f63e3213/width=1224/6840669.jpeg", "nsfwLevel": 1, "width": 1224, "height": 1944, "hash": "U9By]X%MNy-p~XWXtRRP.8RkIoe.%gxuM{Io", "type": "image", "metadata": {"hash": "U9By]X%MNy-p~XWXtRRP.8RkIoe.%gxuM{Io", "size": 3905958, "width": 1224, "height": 1944}, "minor": false, "poi": false, "meta": {"Size": "768x1216", "seed": 554464390, "Model": "DreamShaperXL_Turbo_v2_1_fix5", "steps": 8, "hashes": {"model": "4496b36d48", "lora:aesthetic_anime_v1s": "2b83812dfa"}, "prompt": "In <PERSON>'s evocative style, art of a beautiful young girl cyborg with long brown hair, futuristic, scifi, intricate, elegant, highly detailed, majestic, <PERSON><PERSON>'s brushwork infuses the painting with a unique combination of realism and abstraction, greg rutkowski, surreal gold filigree, broken glass, (masterpiece, sidelighting, finely detailed beautiful eyes: 1.2), hdr, realistic painting, natural skin, textured skin, closed mouth, crystal eyes, butterfly filigree, chest armor, eye makeup, robot joints, long hair moved by the wind, window facing to another world, <PERSON><PERSON>'s distinctive style captures the essence of the girl's enigmatic nature, inviting viewers to explore the depths of her soul, award winning art <lora:aesthetic_anime_v1s:1>", "Version": "v1.6.1", "sampler": "DPM++ SDE Karras", "cfgScale": 2, "resources": [{"name": "aesthetic_anime_v1s", "type": "lora", "weight": 1}, {"hash": "4496b36d48", "name": "DreamShaperXL_Turbo_v2_1_fix5", "type": "model"}], "Model hash": "4496b36d48", "Hires steps": "5", "Hires upscale": "1.6", "Hires upscaler": "8x_NMKD-Superscale_150000_G", "negativePrompt": "ugly, deformed, noisy, blurry, low contrast, text, 3d, cgi, render, anime, open mouth, big forehead, long neck", "ADetailer model": "mediapipe_face_mesh_eyes_only", "ADetailer steps": "5", "ADetailer prompt": "\"In <PERSON>'s evocative style, art of a beautiful young girl cyborg with long brown hair, futuristic, scifi, intricate, elegant, highly detailed, majestic, <PERSON><PERSON>'s brushwork infuses the painting with a unique combination of realism and abstraction, greg rutkowski, surreal gold filigree, broken glass, (masterpiece, sidelighting", "ADetailer version": "23.11.1", "Denoising strength": "0.45", "ADetailer CFG scale": "2.0", "ADetailer mask blur": "7", "\"aesthetic_anime_v1s": "96312f4032a9\"", "ADetailer confidence": "0.3", "ADetailer dilate erode": "4", "ADetailer inpaint padding": "32", "ADetailer negative prompt": "\"ugly, deformed, noisy, blurry, low contrast, text, BadDream, 3d, cgi, render, fake, anime, open mouth, big forehead, long neck\"", "ADetailer denoising strength": "0.2", "ADetailer use separate steps": "True", "ADetailer inpaint only masked": "True", "finely detailed beautiful eyes": "1.2), hdr, realistic painting, natural skin, textured skin, closed mouth, crystal eyes, butterfly filigree, chest armor, eye makeup, robot joints, long hair moved by the wind, window facing to another world, <PERSON><PERSON>'s distinctive style captures the essence of the girl's enigmatic nature, inviting viewers to explore the depths of her soul, award winning art\"", "ADetailer use separate CFG scale": "True", "ADetailer mask only top k largest": "1"}, "availability": "Public", "hasMeta": true, "hasPositivePrompt": true, "onSite": false, "remixOfId": null}, {"url": "https://image.civitai.com/xG1nkqKTMzGDvpLrqFT7WA/d2d6d082-85ae-4cd5-a31e-ae6075e7e612/width=1224/6840674.jpeg", "nsfwLevel": 1, "width": 1224, "height": 1632, "hash": "UUExkUIpV?-U~BR*xaoJ%fe.oLf+%LbHbIWB", "type": "image", "metadata": {"hash": "UUExkUIpV?-U~BR*xaoJ%fe.oLf+%LbHbIWB", "size": 2632926, "width": 1224, "height": 1632}, "minor": false, "poi": false, "meta": {"RNG": "CPU", "Size": "768x1024", "seed": 3346112079, "Model": "DreamShaperXL_Turbo_v2_1_fix5", "steps": 8, "hashes": {"model": "4496b36d48"}, "prompt": "cinematic film still, close up, photo of redheaded girl near grasses, fictional landscapes, (intense sunlight:1.4), realist detail, brooding mood, ue5, detailed character expressions, light amber and red, amazing quality, wallpaper, analog film grain, jacket", "Version": "v1.6.1", "sampler": "DPM++ SDE Karras", "cfgScale": 2, "clipSkip": 2, "resources": [{"hash": "4496b36d48", "name": "DreamShaperXL_Turbo_v2_1_fix5", "type": "model"}], "Model hash": "4496b36d48", "Hires steps": "5", "Hires upscale": "1.6", "Hires upscaler": "8x_NMKD-Superscale_150000_G", "negativePrompt": "(low quality, worst quality:1.4), cgi,  text, signature, watermark, extra limbs, cleavage", "Denoising strength": "0.45"}, "availability": "Public", "hasMeta": true, "hasPositivePrompt": true, "onSite": false, "remixOfId": null}, {"url": "https://image.civitai.com/xG1nkqKTMzGDvpLrqFT7WA/21dacc34-1463-4147-a192-0688984392ff/width=1696/6840675.jpeg", "nsfwLevel": 1, "width": 1696, "height": 1072, "hash": "U8Bf*7m*HXBC#i?aIAIr0J}SX-My~Wwv-pNG", "type": "image", "metadata": {"hash": "U8Bf*7m*HXBC#i?aIAIr0J}SX-My~Wwv-pNG", "size": 2205789, "width": 1696, "height": 1072}, "minor": false, "poi": false, "meta": {"RNG": "CPU", "Size": "1216x768", "seed": 1136711377, "Model": "DreamShaperXL_Turbo_v2_1_fix5", "steps": 8, "hashes": {"model": "4496b36d48", "lora:add-detail-xl": "0d9bd1b873", "lora:aesthetic_anime_v1s": "2b83812dfa"}, "prompt": "cinematic film still, <PERSON> Trooper, colored lights, amazing quality, wallpaper, analog film grain <lora:aesthetic_anime_v1s:0.5>  <lora:add-detail-xl:1.1>", "Version": "v1.6.1", "sampler": "DPM++ SDE Karras", "cfgScale": 2, "clipSkip": 2, "resources": [{"name": "aesthetic_anime_v1s", "type": "lora", "weight": 0.5}, {"hash": "4496b36d48", "name": "DreamShaperXL_Turbo_v2_1_fix5", "type": "model"}], "Model hash": "4496b36d48", "Hires steps": "5", "Hires upscale": "1.4", "add-detail-xl": "9c783c8ce46c\"", "Hires upscaler": "8x_NMKD-Superscale_150000_G", "negativePrompt": "(low quality, worst quality:1.4), cgi,  text, signature, watermark, extra limbs, ((nipples))", "Denoising strength": "0.45", "\"aesthetic_anime_v1s": "96312f4032a9"}, "availability": "Public", "hasMeta": true, "hasPositivePrompt": true, "onSite": false, "remixOfId": null}, {"url": "https://image.civitai.com/xG1nkqKTMzGDvpLrqFT7WA/ff42a320-b3ab-47bb-aa82-614b8c02496d/width=1696/6840678.jpeg", "nsfwLevel": 1, "width": 1696, "height": 1072, "hash": "U27Ly7Iy00YR-s$v00IA00nNrWlALgV[+?kC", "type": "image", "metadata": {"hash": "U27Ly7Iy00YR-s$v00IA00nNrWlALgV[+?kC", "size": 2824483, "width": 1696, "height": 1072}, "minor": false, "poi": false, "meta": {"RNG": "CPU", "Size": "1216x768", "seed": 1136711378, "Model": "DreamShaperXL_Turbo_v2_1_fix5", "steps": 8, "hashes": {"model": "4496b36d48", "embed:negativeXL_D": "fff5d51ab6", "lora:aesthetic_anime_v1s": "2b83812dfa"}, "prompt": "anime style, 1girl, blunt bangs, high ponytail, silver hair, pointy ears, in the depths of a bioluminescent alien jungle, [evangelion:cyberpunk edgerunners:0.5], reflective transparent iridescent opaque clothing, long sleeves, flowing dress, long skirt, very aesthetic, highres, 4k, 8k, intricate detail, cinematic lighting, amazing quality, amazing shading, detailed Illustration, official artwork, wallpaper, official art, extremely detailed eyes and face, beautiful detailed eyes, from below, full body, thigh gap, <lora:aesthetic_anime_v1s:1>", "Version": "v1.6.1", "sampler": "DPM++ SDE Karras", "cfgScale": 2, "clipSkip": 2, "resources": [{"name": "aesthetic_anime_v1s", "type": "lora", "weight": 1}, {"hash": "4496b36d48", "name": "DreamShaperXL_Turbo_v2_1_fix5", "type": "model"}], "Model hash": "4496b36d48", "Hires steps": "5", "negativeXL_D": "fff5d51ab655\"", "\"negativeXL_D": "fff5d51ab655", "Hires upscale": "1.4", "worst quality": "1.4), negativeXL_D, cgi, text, signature, watermark, extra limbs, holding dress\"", "Hires upscaler": "8x_NMKD-Superscale_150000_G", "negativePrompt": "(low quality, worst quality:1.4), negativeXL_D, cgi, text, signature, watermark, extra limbs, holding dress", "ADetailer model": "face_yolov8n.pt", "ADetailer steps": "5", "ADetailer prompt": "\"anime style, 1girl, blunt bangs, high ponytail, silver hair, pointy ears, in the depths of a bioluminescent alien jungle", "ADetailer version": "23.11.1", "Denoising strength": "0.45", "ADetailer CFG scale": "2.0", "ADetailer mask blur": "4", "aesthetic_anime_v1s": "1>\"", "\"aesthetic_anime_v1s": "96312f4032a9\"", "ADetailer confidence": "0.3", "cyberpunk edgerunners": "0.5], reflective transparent iridescent opaque clothing, long sleeves, flowing dress, long skirt, very aesthetic, highres, 4k, 8k, intricate detail, cinematic lighting, amazing quality, amazing shading, detailed Illustration, official artwork, wallpaper, official art, extremely detailed eyes and face, beautiful detailed eyes, from below, full body, thigh gap", "ADetailer dilate erode": "4", "ADetailer inpaint padding": "32", "ADetailer negative prompt": "\"(low quality", "ADetailer denoising strength": "0.33", "ADetailer use separate steps": "True", "ADetailer inpaint only masked": "True", "ADetailer use separate CFG scale": "True", "ADetailer mask only top k largest": "1"}, "availability": "Public", "hasMeta": true, "hasPositivePrompt": true, "onSite": false, "remixOfId": null}, {"url": "https://image.civitai.com/xG1nkqKTMzGDvpLrqFT7WA/210afd5c-157d-482c-9808-fe190167a9c0/width=1072/6927436.jpeg", "nsfwLevel": 1, "width": 1072, "height": 1432, "hash": "U5BWi4?u?tRO_NW,OE%200%MD*Io00-;S6af", "type": "image", "metadata": {"hash": "U5BWi4?u?tRO_NW,OE%200%MD*Io00-;S6af", "size": 2207868, "width": 1072, "height": 1432}, "minor": false, "poi": false, "meta": {"RNG": "CPU", "Size": "768x1024", "seed": 2067885438, "Model": "DreamShaperXL_Turbo_v2_1", "steps": 8, "hashes": {"model": "4496b36d48", "lora:aesthetic_anime_v1s": "2b83812dfa"}, "prompt": "(masterpiece, best quality, ultra-detailed, best shadow), cinematic film still, photo of a man wearing a high tech scifi armor, mecha armor, male focus, armor, solo, facial hair, cape, beard, looking at viewer, blue eyes, blurry background, power armor, knee protection, standing, brown hair, science fiction  <lora:aesthetic_anime_v1s:1.2>", "sampler": "DPM++ SDE Karras", "cfgScale": 2, "clipSkip": 2, "resources": [{"name": "aesthetic_anime_v1s", "type": "lora", "weight": 1.2}, {"hash": "4496b36d48", "name": "DreamShaperXL_Turbo_v2_1", "type": "model"}], "Model hash": "4496b36d48", "worst quality": "1.4), cgi,  text, signature, watermark, extra limbs\"", "negativePrompt": "(low quality, worst quality:1.4), cgi,  text, signature, watermark, extra limbs, ((nipples))", "ADetailer model": "mediapipe_face_mesh_eyes_only", "ADetailer steps": "5", "ADetailer prompt": "\"handsome face, perfect eyes\"", "Denoising strength": "0.52", "ADetailer CFG scale": "2.0", "ADetailer mask blur": "4", "ADetailer model 2nd": "hand_yolov8n.pt", "ADetailer confidence": "0.2", "ADetailer prompt 2nd": "hand", "ADetailer dilate erode": "4", "ADetailer inpaint padding": "32", "ADetailer negative prompt": "\"(low quality", "ADetailer denoising strength": "0.4", "ADetailer use separate steps": "True", "ADetailer inpaint only masked": "True", "ADetailer negative prompt 2nd": "\" (low quality", "ADetailer use separate CFG scale": "True", "ADetailer mask only top k largest": "1"}, "availability": "Public", "hasMeta": true, "hasPositivePrompt": true, "onSite": false, "remixOfId": null}, {"url": "https://image.civitai.com/xG1nkqKTMzGDvpLrqFT7WA/d691e954-92af-4e8f-af91-92d6c64167db/width=1072/6840681.jpeg", "nsfwLevel": 1, "width": 1072, "height": 1432, "hash": "U7B:sn~p$|?w=r%gtQRj4nt5x^9F9Zn#IAtR", "type": "image", "metadata": {"hash": "U7B:sn~p$|?w=r%gtQRj4nt5x^9F9Zn#IAtR", "size": 2152394, "width": 1072, "height": 1432}, "minor": false, "poi": false, "meta": {"RNG": "CPU", "Size": "768x1024", "seed": 2946499990, "Model": "DreamShaperXL_Turbo_v2_1_fix5", "steps": 8, "hashes": {"model": "4496b36d48", "lora:aesthetic_anime_v1s": "2b83812dfa"}, "prompt": "cinematic film still, close up, a robot woman stands tall, half-human half machine, amongst an ancient Greek gallery of paintings and marble, religious symbolism, quantum wavetracing, high fashion editorial, glsl shaders, semiconductors and electronic computer hardware, amazing quality, wallpaper, analog film grain, perfect face skin <lora:aesthetic_anime_v1s:1.1>", "sampler": "DPM++ SDE Karras", "cfgScale": 2, "clipSkip": 2, "resources": [{"name": "aesthetic_anime_v1s", "type": "lora", "weight": 1.1}, {"hash": "4496b36d48", "name": "DreamShaperXL_Turbo_v2_1_fix5", "type": "model"}], "Model hash": "4496b36d48", "worst quality": "1.4), cgi,  text, signature, watermark, extra limbs\"", "negativePrompt": "(low quality, worst quality:1.4), cgi,  text, signature, watermark, extra limbs, ((nipples))", "ADetailer model": "mediapipe_face_mesh_eyes_only", "ADetailer steps": "5", "ADetailer prompt": "\"cinematic film still, a woman stands tall, half-human half machine, amongst an ancient Greek gallery of paintings and marble, religious symbolism, quantum wavetracing, high fashion editorial, glsl shaders, semiconductors and electronic computer hardware, amazing quality", "Denoising strength": "0.52", "ADetailer CFG scale": "2.0", "ADetailer mask blur": "4", "ADetailer model 2nd": "hand_yolov8n.pt", "aesthetic_anime_v1s": "1.1>\"", "ADetailer confidence": "0.25", "ADetailer prompt 2nd": "hand", "ADetailer dilate erode": "4", "ADetailer inpaint padding": "32", "ADetailer negative prompt": "\"(low quality", "ADetailer denoising strength": "0.4", "ADetailer use separate steps": "True", "ADetailer inpaint only masked": "True", "ADetailer negative prompt 2nd": "\" (low quality", "ADetailer use separate CFG scale": "True", "ADetailer mask only top k largest": "1"}, "availability": "Public", "hasMeta": true, "hasPositivePrompt": true, "onSite": false, "remixOfId": null}, {"url": "https://image.civitai.com/xG1nkqKTMzGDvpLrqFT7WA/75218d56-c319-4038-9224-baccc79769a5/width=1536/6840683.jpeg", "nsfwLevel": 1, "width": 1536, "height": 1152, "hash": "U4A1VU17BYH;%7IoIoxn00^M%N$+00~q=?Io", "type": "image", "metadata": {"hash": "U4A1VU17BYH;%7IoIoxn00^M%N$+00~q=?Io", "size": 2428729, "width": 1536, "height": 1152}, "minor": false, "poi": false, "meta": {"RNG": "CPU", "Size": "1024x768", "seed": 2084045070, "Model": "DreamShaperXL_Turbo_v2_1_fix5", "steps": 8, "hashes": {"model": "4496b36d48"}, "prompt": "anime girl, night, blue light behind her,  ((Galaxy, Lens flare)), short hair, flower field, night sky, cinematic shot. Wallpaper. (Blue color schema), detailed background, a city in the distance", "Version": "v1.6.1", "sampler": "DPM++ SDE Karras", "cfgScale": 2, "clipSkip": 2, "resources": [{"hash": "4496b36d48", "name": "DreamShaperXL_Turbo_v2_1_fix5", "type": "model"}], "Model hash": "4496b36d48", "Hires steps": "5", "Hires upscale": "1.5", "Hires upscaler": "8x_NMKD-Superscale_150000_G", "negativePrompt": "cgi, 3d render, bad quality, worst quality, text, signature, watermark, extra limbs, badges", "ADetailer model": "face_yolov8n.pt", "ADetailer steps": "5", "ADetailer prompt": "\"anime girl, night, blue light behind her,  ((Galaxy, Lens flare)), short hair, flower field, night sky, cinematic shot. Wallpaper. (Blue color schema), detailed background, a city in the distance\"", "ADetailer version": "23.11.1", "Denoising strength": "0.45", "ADetailer CFG scale": "2.0", "ADetailer mask blur": "4", "ADetailer confidence": "0.2", "ADetailer dilate erode": "4", "ADetailer inpaint padding": "32", "ADetailer negative prompt": "\"cgi, 3d render, bad quality, worst quality, text, signature, watermark, extra limbs,\"", "ADetailer denoising strength": "0.37", "ADetailer use separate steps": "True", "ADetailer inpaint only masked": "True", "ADetailer use separate CFG scale": "True", "ADetailer mask only top k largest": "1"}, "availability": "Public", "hasMeta": true, "hasPositivePrompt": true, "onSite": false, "remixOfId": null}, {"url": "https://image.civitai.com/xG1nkqKTMzGDvpLrqFT7WA/2fe17de1-49d5-4285-911a-302538e6ad9c/width=1536/6840684.jpeg", "nsfwLevel": 1, "width": 1536, "height": 1536, "hash": "U6G+5W000Jo?0FDNT1?E0P#k_2MwG^?v~9wa", "type": "image", "metadata": {"hash": "U6G+5W000Jo?0FDNT1?E0P#k_2MwG^?v~9wa", "size": 3384070, "width": 1536, "height": 1536}, "minor": false, "poi": false, "meta": {"RNG": "CPU", "Size": "1024x1024", "seed": 2084045052, "Model": "DreamShaperXL_Turbo_v2_1_fix5", "steps": 7, "hashes": {"model": "4496b36d48", "embed:negativeXL_D": "fff5d51ab6", "embed:unaestheticXL_hk1": "ca29d24a64"}, "prompt": "80's anime screencap, girl wearing a cropped top and short shorts, artistic rendition with wide brush strokes, anime comic", "sampler": "DPM++ SDE Karras", "cfgScale": 2, "clipSkip": 2, "resources": [{"hash": "4496b36d48", "name": "DreamShaperXL_Turbo_v2_1_fix5", "type": "model"}], "Model hash": "4496b36d48", "Hires steps": "7", "negativeXL_D": "fff5d51ab655", "Hires upscale": "1.5", "Hires upscaler": "None", "negativePrompt": "cgi, render, bad quality, worst quality, text, signature, watermark, extra limbs, unaestheticXL_hk1, negativeXL_D", "ADetailer model": "face_yolov8n.pt", "ADetailer steps": "5", "ADetailer prompt": "\"80's anime screencap, girl wearing a cropped top and short shorts, artistic rendition with wide brush strokes, anime comic\"", "ADetailer version": "23.11.1", "unaestheticXL_hk1": "ca29d24a64c1", "\"unaestheticXL_hk1": "ca29d24a64c1", "Denoising strength": "0.55", "ADetailer CFG scale": "2.0", "ADetailer mask blur": "4", "ADetailer confidence": "0.2", "ADetailer dilate erode": "4", "ADetailer inpaint padding": "32", "ADetailer negative prompt": "\"cgi, render, bad quality, worst quality, text, signature, watermark, extra limbs, unaestheticXL_hk1, negativeXL_D\"", "ADetailer denoising strength": "0.37", "ADetailer use separate steps": "True", "ADetailer inpaint only masked": "True", "ADetailer use separate CFG scale": "True", "ADetailer mask only top k largest": "1"}, "availability": "Public", "hasMeta": true, "hasPositivePrompt": true, "onSite": false, "remixOfId": null}, {"url": "https://image.civitai.com/xG1nkqKTMzGDvpLrqFT7WA/b827a6f1-4e2f-4b15-8983-e58c8ca244df/width=1696/6840692.jpeg", "nsfwLevel": 1, "width": 1696, "height": 1072, "hash": "U8CP-D4qR2-nyYMyoHNGMxIUIuIp~SD*%NWV", "type": "image", "metadata": {"hash": "U8CP-D4qR2-nyYMyoHNGMxIUIuIp~SD*%NWV", "size": 2578885, "width": 1696, "height": 1072}, "minor": false, "poi": false, "meta": {"RNG": "CPU", "Size": "1216x768", "seed": 1592846095, "Model": "DreamShaperXL_Turbo_v2_1_fix5", "steps": 8, "hashes": {"model": "4496b36d48", "lora:add-detail-xl": "0d9bd1b873", "lora:aesthetic_anime_v1s": "2b83812dfa"}, "prompt": "cinematic film still, close up, photo of a cute Pokémon, in the style of hyper-realistic fantasy,, sony fe 12-24mm f/2.8 gm, close up, 32k uhd, light navy and light amber, kushan empirem alluring, perfect skin, seductive, amazing quality, wallpaper, analog film grain <lora:aesthetic_anime_v1s:0.5>  <lora:add-detail-xl:1.1>", "Version": "v1.6.1", "sampler": "DPM++ SDE Karras", "cfgScale": 2, "clipSkip": 2, "resources": [{"name": "aesthetic_anime_v1s", "type": "lora", "weight": 0.5}, {"hash": "4496b36d48", "name": "DreamShaperXL_Turbo_v2_1_fix5", "type": "model"}], "Model hash": "4496b36d48", "Hires steps": "5", "Hires upscale": "1.4", "add-detail-xl": "9c783c8ce46c\"", "Hires upscaler": "8x_NMKD-Superscale_150000_G", "negativePrompt": "Pikachu, (low quality, worst quality:1.4), cgi,  text, signature, watermark, extra limbs, ((nipples))", "Denoising strength": "0.52", "\"aesthetic_anime_v1s": "96312f4032a9"}, "availability": "Public", "hasMeta": true, "hasPositivePrompt": true, "onSite": false, "remixOfId": null}, {"url": "https://image.civitai.com/xG1nkqKTMzGDvpLrqFT7WA/08b71e78-50b0-4193-a3cb-d12fddf9cc85/width=1072/6840696.jpeg", "nsfwLevel": 1, "width": 1072, "height": 1432, "hash": "U4Bpzx00H;Rj~n9yDhM|MbE3ni_49DIWx^IA", "type": "image", "metadata": {"hash": "U4Bpzx00H;Rj~n9yDhM|MbE3ni_49DIWx^IA", "size": 2449763, "width": 1072, "height": 1432}, "minor": false, "poi": false, "meta": {"RNG": "CPU", "Size": "768x1024", "seed": 2067885437, "Model": "DreamShaperXL_Turbo_v2_1_fix5", "steps": 8, "hashes": {"model": "4496b36d48", "lora:aesthetic_anime_v1s": "2b83812dfa"}, "prompt": "masterpiece, best quality,\ncinematic film still, realistic, portrait, solo, white mecha robot, cape, science fiction, torn clothes, glowing, standing, robot joints, mecha, armor, cowboy shot, (floating cape), intense sunlight, silver dragonborn, outdoors, landscape, nature\nhighres, 4k, 8k, intricate detail, cinematic lighting, amazing quality, wallpaper <lora:aesthetic_anime_v1s:1.1>", "Version": "v1.6.1", "sampler": "DPM++ SDE Karras", "cfgScale": 2, "clipSkip": 2, "resources": [{"name": "aesthetic_anime_v1s", "type": "lora", "weight": 1.1}, {"hash": "4496b36d48", "name": "DreamShaperXL_Turbo_v2_1_fix5", "type": "model"}], "Model hash": "4496b36d48", "Hires steps": "5", "Hires upscale": "1.4", "Hires upscaler": "8x_NMKD-Superscale_150000_G", "negativePrompt": "nipples, (low quality, worst quality:1.4), cgi,  text, signature, watermark, extra limbs", "Denoising strength": "0.52", "\"aesthetic_anime_v1s": "96312f4032a9\""}, "availability": "Public", "hasMeta": true, "hasPositivePrompt": true, "onSite": false, "remixOfId": null}], "downloadUrl": "https://civitai.com/api/download/models/351306", "creator": {"username": "Lykon", "image": "https://image.civitai.com/xG1nkqKTMzGDvpLrqFT7WA/3b119431-445f-4b21-aba6-87a5ee9518ef/width=96/Lykon.jpeg"}}, "tags": ["anime", "art", "base model", "artstyle", "photography", "realistic", "dreamshaper", "turbo", "xl", "sdxl"], "modelDescription": "<h1 id=\"heading-4\">DreamShaper XL - Now Turbo!</h1><h3 id=\"heading-134\"><a target=\"_blank\" rel=\"ugc\" href=\"https://civitai.com/models/4384/dreamshaper\">Also check out the 1.5 DreamShaper page</a></h3><p><strong>Check the version description below (bottom right) for more info and add a ❤️ to receive future updates.</strong><br /><strong>Do you like what I do? Consider supporting me on </strong><a target=\"_blank\" rel=\"ugc\" href=\"https://www.patreon.com/Lykon275\"><strong>Patreon</strong></a><strong> 🅿️ to get exclusive tips and tutorials, or feel free to </strong><a target=\"_blank\" rel=\"ugc\" href=\"https://snipfeed.co/lykon\"><strong>buy me a coffee</strong></a><strong> ☕</strong></p><p></p><h3 id=\"heading-135\">Join my <a target=\"_blank\" rel=\"ugc\" href=\"https://discord.gg/uAhsmDq7GC\">Discord Server</a></h3><p></p><p><strong><span style=\"color:rgb(64, 192, 87)\">Alpha2 is a bit old now. I suggest you switch to the Turbo or Lightning version.</span></strong><br /><strong>DreamShaper</strong> is a<em> general purpose </em>SD model that aims at doing everything well, photos, art, anime, manga. It's designed to go against other general purpose models and pipelines like Midjourney and DALL-E.</p><p></p><h2 id=\"heading-634\">\"It's Turbotime\"</h2><p><strong>Turbo </strong>version should be used at <u><span style=\"color:rgb(253, 126, 20)\">CFG scale 2</span></u> and with around <u><span style=\"color:rgb(253, 126, 20)\">4-8 sampling steps</span></u>. This should work only with <strong><u><span style=\"color:rgb(253, 126, 20)\">DPM++ SDE Karras</span></u></strong> (NOT 2M). You can use this with LCM sampler, but don't do it unless you need speed vs quality. <br /><strong><span style=\"color:rgb(130, 201, 30)\">Sampler comparison at 8 steps:</span></strong> <a target=\"_blank\" rel=\"ugc\" href=\"https://civitai.com/posts/951781\">https://civitai.com/posts/951781</a><br /><strong><span style=\"color:rgb(250, 82, 82)\">UPDATE: </span><span style=\"color:rgb(18, 184, 134)\">Lightning </span></strong>version targets <u><span style=\"color:rgb(253, 126, 20)\">3-6 sampling steps</span></u> at <u><span style=\"color:rgb(253, 126, 20)\">CFG scale 2</span></u> and should also work only with <strong><u><span style=\"color:rgb(253, 126, 20)\">DPM++ SDE Karras</span></u></strong>. Avoid going too far above 1024 in either direction for the 1st step.</p><p>No need to use refiner and this model itself can be used for highres fix and tiled upscaling. <br />Examples have been generated using Auto1111, but you can achieve similar results with this ComfyUI Workflow: <a target=\"_blank\" rel=\"ugc\" href=\"https://pastebin.com/79XN01xs\">https://pastebin.com/79XN01xs</a></p><p><strong>Basic style comparison: </strong><a target=\"_blank\" rel=\"ugc\" href=\"https://civitai.com/images/4427452\">https://civitai.com/images/4427452</a></p><p><strong>If you train on this, make sure to use <u><span style=\"color:rgb(253, 126, 20)\">DPM++ SDE</span></u> sampler and appropriate steps/cfg.</strong></p><p><strong>Keep in mind <span style=\"color:rgb(250, 82, 82)\">Turbo currently cannot be used commercially unless you get permission from StabilityAI.</span> </strong>Get a membership here: <a target=\"_blank\" rel=\"ugc\" href=\"https://stability.ai/membership\">https://stability.ai/membership</a></p><p>You can use the Turbo version (<strong>not Lightning</strong>) as a non-Turbo model with DPM++ <strong><u>2M</u> </strong>SDE Karras / Euler at cfg 6 and 20-40 steps. Here is a comparison I made with some of the best non-Turbo XL models (with regular settings and turbo settings): <a target=\"_blank\" rel=\"ugc\" href=\"https://civitai.com/posts/1414848￼￼\">https://civitai.com/posts/1414848<br /></a>I have no idea why anyone would prefer 40 steps over 8, but you have the option.</p><p></p><div data-youtube-video><iframe width=\"640\" height=\"480\" allowfullscreen=\"true\" autoplay=\"false\" disablekbcontrols=\"false\" enableiframeapi=\"false\" endtime=\"0\" ivloadpolicy=\"0\" loop=\"false\" modestbranding=\"false\" origin playlist src=\"https://www.youtube.com/embed/l71ZCs7tUu8\" start=\"0\"></iframe></div><p></p><h2 id=\"heading-635\"><u>Old</u> description referring to <u>Alpha 2</u> and before</h2><p><strong>Finetuned over SDXL1.0.</strong><br />Even if this is still an alpha version, I think it's already much better compared to the first alpha based on xl0.9.<br />For the workflows you need Math plugins for comfy (or to reimplement some parts manually).<br />Basically I do the first gen with DreamShaperXL, then I upscale to 2x and finally a do a img2img steo with either DreamShaperXL itself, or a 1.5 model that i find suited, such as DreamShaper7 or AbsoluteReality.</p><p><strong>What does it do better than SDXL1.0?</strong></p><ul><li><p>No need for refiner. Just do highres fix (upscale+i2i)</p></li><li><p>Better looking people</p></li><li><p>Less blurry edges</p></li><li><p>75% better dragons 🐉</p></li><li><p>Better NSFW</p></li></ul><p></p><h3 id=\"heading-226\">Old DreamShaper XL 0.9 Alpha Description</h3><p>Finally got permission to share this. It's based on SDXL0.9, so it's just a training test. It definitely has room for improvement.</p><p>Workflow for this one is a bit more complicated than usual, as it's using AbsoluteReality or DreamShaper7 as \"refiner\" (meaning I'm generating with DreamShaperXL and then doing \"highres fix\" with AR or DS7). <br /><br />Results are quite nice for such an early stage. <br /><br />I might disable the comment section as I'm sure some people will judge this even if it's early stage. I also don't think this is on par with SD1.5 DreamShaper yet, but it's useless to pour resources into this as SDXL1.0 is about to be released. <br /><br />Have fun and make sure to add a <strong>❤️ </strong>to receive future updates.<br /><br /><s>Non commercial license is forced by Stability at the moment.</s></p>", "civitai_deleted": false, "favorite": false, "exclude": false, "model_type": "checkpoint"}