{"file_name": "w4r10ck_Detail_Tweaker_XL_Quality_SDXL_1.0", "model_name": "Detail Tweaker XL", "file_path": "D:/stable_diffusion/1_Models/SDXL/loras/Utility/Quality/Enhancement/w4r10ck_Detail_Tweaker_XL_Quality_SDXL_1.0.safetensors", "size": 228452344, "modified": 1749462514.9428937, "sha256": "0d9bd1b873a7863e128b4672e3e245838858f71469a3cec58123c16c06f83bd7", "base_model": "SDXL 1.0", "preview_url": "D:/stable_diffusion/1_Models/SDXL/loras/Utility/Quality/Enhancement/w4r10ck_Detail_Tweaker_XL_Quality_SDXL_1.0.webp", "preview_nsfw_level": 0, "notes": "", "from_civitai": true, "civitai": {"id": 135867, "modelId": 122359, "name": "v1.0", "createdAt": "2023-08-07T14:51:04.506Z", "updatedAt": "2025-02-14T21:43:00.929Z", "status": "Published", "publishedAt": "2023-08-07T14:55:02.627Z", "trainedWords": [], "trainingStatus": null, "trainingDetails": null, "baseModel": "SDXL 1.0", "baseModelType": "Standard", "earlyAccessEndsAt": null, "earlyAccessConfig": {}, "description": null, "uploadType": "Created", "usageControl": "Download", "air": "urn:air:sdxl:lora:civitai:122359@135867", "stats": {"downloadCount": 312149, "ratingCount": 4079, "rating": 4.98, "thumbsUpCount": 34464}, "model": {"name": "Detail Tweaker XL", "type": "LORA", "nsfw": false, "poi": false}, "files": [{"id": 99264, "sizeKB": 223097.9921875, "name": "add-detail-xl.safetensors", "type": "Model", "pickleScanResult": "Success", "pickleScanMessage": "No Pickle imports", "virusScanResult": "Success", "virusScanMessage": null, "scannedAt": "2023-08-07T14:55:53.517Z", "metadata": {"format": "SafeTensor", "size": null, "fp": null}, "hashes": {"AutoV1": "29A40D2E", "AutoV2": "0D9BD1B873", "SHA256": "0D9BD1B873A7863E128B4672E3E245838858F71469A3CEC58123C16C06F83BD7", "CRC32": "A94E124F", "BLAKE3": "595854A2079ABB9AF0FD84830DDF1142ABFFAA70DEBF51C25C2E73A248CB11CA", "AutoV3": "9C783C8CE46C"}, "primary": true, "downloadUrl": "https://civitai.com/api/download/models/135867"}], "images": [{"url": "https://image.civitai.com/xG1nkqKTMzGDvpLrqFT7WA/217179cb-87a0-4e96-8d77-e410f757aba0/width=1800/1917130.jpeg", "nsfwLevel": 1, "width": 2698, "height": 1158, "hash": "UUHK;5WYoJR*~qWCj[j[_Ns:a}of_3s:azof", "type": "image", "metadata": {"hash": "UUHK;5WYoJR*~qWCj[j[_Ns:a}of_3s:azof", "size": 4192036, "width": 2698, "height": 1158}, "minor": false, "poi": false, "meta": {"Size": "896x1024", "seed": 3308533307, "Model": "dreamshaperXL10_alpha2Xl10", "steps": 20, "Script": "X/Y/Z plot", "X Type": "Prompt S/R", "hashes": {"model": "82b5f664ae"}, "prompt": "photo, 8k portrait of beautiful cyborg with brown hair, intricate, elegant, highly detailed, majestic, digital photography, art by art<PERSON><PERSON> and ruan jia and greg r<PERSON><PERSON> surreal painting gold butterfly filigree, broken glass, (masterpiece, sidelighting, finely detailed beautiful eyes: 1.2), hdr, realistic, high definition, <lora:add-detail-xl:-1>", "Version": "v1.5.1", "sampler": "DPM++ SDE Karras", "cfgScale": 7, "resources": [{"hash": "82b5f664ae", "name": "dreamshaperXL10_alpha2Xl10", "type": "model"}], "Model hash": "82b5f664ae", "add-detail-xl": "1>\"", "\"add-detail-xl": "9c783c8ce46c\""}, "availability": "Public", "hasMeta": true, "hasPositivePrompt": true, "onSite": false, "remixOfId": null}, {"url": "https://image.civitai.com/xG1nkqKTMzGDvpLrqFT7WA/1132c8aa-053e-468b-9f80-32a071f73f57/width=1800/1917133.jpeg", "nsfwLevel": 1, "width": 2698, "height": 1158, "hash": "UWDb~Kofa|of?vj[j[kC~WbHj[kC~qoffkof", "type": "image", "metadata": {"hash": "UWDb~Kofa|of?vj[j[kC~WbHj[kC~qoffkof", "size": 3810435, "width": 2698, "height": 1158}, "minor": false, "poi": false, "meta": {"Size": "896x1024", "seed": 3407892333, "Model": "dreamshaperXL10_alpha2Xl10", "steps": 20, "Script": "X/Y/Z plot", "X Type": "Prompt S/R", "hashes": {"model": "82b5f664ae"}, "prompt": "city street, photo, night, lights, <lora:add-detail-xl:-1>", "Version": "v1.5.1", "sampler": "DPM++ SDE Karras", "cfgScale": 7, "resources": [{"hash": "82b5f664ae", "name": "dreamshaperXL10_alpha2Xl10", "type": "model"}], "Model hash": "82b5f664ae", "add-detail-xl": "1>\"", "\"add-detail-xl": "9c783c8ce46c\""}, "availability": "Public", "hasMeta": true, "hasPositivePrompt": true, "onSite": false, "remixOfId": null}, {"url": "https://image.civitai.com/xG1nkqKTMzGDvpLrqFT7WA/270b159f-d91b-424b-8ac6-14857726f1f1/width=1800/1917136.jpeg", "nsfwLevel": 1, "width": 2698, "height": 1158, "hash": "UjHBSroJays._4oJazs.?cWDayWXpJa}oLWX", "type": "image", "metadata": {"hash": "UjHBSroJays._4oJazs.?cWDayWXpJa}oLWX", "size": 3386323, "width": 2698, "height": 1158}, "minor": false, "poi": false, "meta": {"Size": "896x1024", "seed": 4138801743, "Model": "dreamshaperXL10_alpha2Xl10", "steps": 20, "Script": "X/Y/Z plot", "X Type": "Prompt S/R", "hashes": {"model": "82b5f664ae"}, "prompt": "anime, road, mountains, sunset, village, <lora:add-detail-xl:-1.5>", "Version": "v1.5.1", "sampler": "DPM++ SDE Karras", "cfgScale": 7, "resources": [{"hash": "82b5f664ae", "name": "dreamshaperXL10_alpha2Xl10", "type": "model"}], "Model hash": "82b5f664ae", "add-detail-xl": "1.5>\"", "\"add-detail-xl": "9c783c8ce46c\""}, "availability": "Public", "hasMeta": true, "hasPositivePrompt": true, "onSite": false, "remixOfId": null}, {"url": "https://image.civitai.com/xG1nkqKTMzGDvpLrqFT7WA/f7cef77a-eabb-4d89-9faa-399bef495e32/width=1800/1917137.jpeg", "nsfwLevel": 1, "width": 2698, "height": 1158, "hash": "UgF5]ot7ayt8_Mofjtof?cbHfQfk-=ayj[ay", "type": "image", "metadata": {"hash": "UgF5]ot7ayt8_Mofjtof?cbHfQfk-=ayj[ay", "size": 3989467, "width": 2698, "height": 1158}, "minor": false, "poi": false, "meta": {"Size": "896x1024", "seed": 2161439391, "Model": "dreamshaperXL10_alpha2Xl10", "steps": 20, "Script": "X/Y/Z plot", "X Type": "Prompt S/R", "hashes": {"model": "82b5f664ae"}, "prompt": "cyberpunk city, <lora:eduardo-xl:0.8>, <lora:add-detail-xl:-1>", "Version": "v1.5.1", "sampler": "DPM++ SDE Karras", "cfgScale": 7, "resources": [{"hash": "82b5f664ae", "name": "dreamshaperXL10_alpha2Xl10", "type": "model"}], "Model hash": "82b5f664ae", "\"eduardo-xl": "1be1874242ec", "add-detail-xl": "1>\""}, "availability": "Public", "hasMeta": true, "hasPositivePrompt": true, "onSite": false, "remixOfId": null}, {"url": "https://image.civitai.com/xG1nkqKTMzGDvpLrqFT7WA/4d6138b0-6e54-4331-8326-1b5291fed0d8/width=1800/1917142.jpeg", "nsfwLevel": 1, "width": 2698, "height": 1158, "hash": "UONAYXIUbIs;_Ns:ayof.Aayjsof~Vofofof", "type": "image", "metadata": {"hash": "UONAYXIUbIs;_Ns:ayof.Aayjsof~Vofofof", "size": 1777229, "width": 2698, "height": 1158}, "minor": false, "poi": false, "meta": {"Size": "896x1024", "seed": 1138549894, "Model": "dreamshaperXL10_alpha2Xl10", "steps": 20, "Script": "X/Y/Z plot", "X Type": "Prompt S/R", "hashes": {"model": "82b5f664ae"}, "prompt": "a very cute tiny mouse standing with a piece of cheese, <lora:add-detail-xl:-1>", "Version": "v1.5.1", "sampler": "DPM++ SDE Karras", "cfgScale": 7, "resources": [{"hash": "82b5f664ae", "name": "dreamshaperXL10_alpha2Xl10", "type": "model"}], "Model hash": "82b5f664ae", "add-detail-xl": "1>\"", "\"add-detail-xl": "9c783c8ce46c\""}, "availability": "Public", "hasMeta": true, "hasPositivePrompt": true, "onSite": false, "remixOfId": null}, {"url": "https://image.civitai.com/xG1nkqKTMzGDvpLrqFT7WA/fc49dbba-7f86-4d9d-a8cb-bfc8e483e3af/width=1800/1917146.jpeg", "nsfwLevel": 1, "width": 2698, "height": 1158, "hash": "UTE{h0t7e=of_Na#jubH~qofa{oM?vodj@of", "type": "image", "metadata": {"hash": "UTE{h0t7e=of_Na#jubH~qofa{oM?vodj@of", "size": 5359238, "width": 2698, "height": 1158}, "minor": false, "poi": false, "meta": {"Size": "896x1024", "seed": 4199339366, "Model": "dreamshaperXL10_alpha2Xl10", "steps": 20, "Script": "X/Y/Z plot", "X Type": "Prompt S/R", "hashes": {"model": "82b5f664ae"}, "prompt": "old man, long beard, smoking pipe, oil painting, sitting in garden, <lora:add-detail-xl:-1>", "Version": "v1.5.1", "sampler": "DPM++ SDE Karras", "cfgScale": 7, "resources": [{"hash": "82b5f664ae", "name": "dreamshaperXL10_alpha2Xl10", "type": "model"}], "Model hash": "82b5f664ae", "add-detail-xl": "1>\"", "\"add-detail-xl": "9c783c8ce46c\""}, "availability": "Public", "hasMeta": true, "hasPositivePrompt": true, "onSite": false, "remixOfId": null}, {"url": "https://image.civitai.com/xG1nkqKTMzGDvpLrqFT7WA/f7a46c72-8d6e-41fe-b6e1-3b093039b036/width=1800/1917151.jpeg", "nsfwLevel": 1, "width": 2698, "height": 1158, "hash": "UYF#,ho#ofoz~podj[oe_4WXfQbI?vofj[bH", "type": "image", "metadata": {"hash": "UYF#,ho#ofoz~podj[oe_4WXfQbI?vofj[bH", "size": 5450868, "width": 2698, "height": 1158}, "minor": false, "poi": false, "meta": {"Size": "896x1024", "seed": 3638304036, "Model": "dreamshaperXL10_alpha2Xl10", "steps": 20, "Script": "X/Y/Z plot", "X Type": "Prompt S/R", "hashes": {"model": "82b5f664ae"}, "prompt": "(masterpiece:1.1), (highest quality:1.1), (HDR:1.3), (top quality, best quality, official art, beautiful and aesthetic:1.2), woman, extremely detailed, (fractal art:1.1), (colorful:1.1), highest detailed, (zentangle:1.2), (dynamic), (abstract background:1.3), (shiny), (many colors:1.4), solo, coral background, yellow lightning, cinematic lighting, long hair, detailed black eyes, highest quality face, (sky aesthetic), <lora:add-detail-xl:-1>", "Version": "v1.5.1", "sampler": "DPM++ SDE Karras", "cfgScale": 7, "resources": [{"hash": "82b5f664ae", "name": "dreamshaperXL10_alpha2Xl10", "type": "model"}], "Model hash": "82b5f664ae", "add-detail-xl": "1>\"", "\"add-detail-xl": "9c783c8ce46c\""}, "availability": "Public", "hasMeta": true, "hasPositivePrompt": true, "onSite": false, "remixOfId": null}, {"url": "https://image.civitai.com/xG1nkqKTMzGDvpLrqFT7WA/41d4eb52-3728-4fd1-864b-73f23cfd969b/width=1800/1917152.jpeg", "nsfwLevel": 1, "width": 2698, "height": 1158, "hash": "UnNvrYt7WBt6?wV@j[bHpJkCj[j?%Ms.bIkC", "type": "image", "metadata": {"hash": "UnNvrYt7WBt6?wV@j[bHpJkCj[j?%Ms.bIkC", "size": 3356481, "width": 2698, "height": 1158}, "minor": false, "poi": false, "meta": {"Size": "896x1024", "seed": 1330402432, "Model": "dreamshaperXL10_alpha2Xl10", "steps": 20, "Script": "X/Y/Z plot", "X Type": "Prompt S/R", "hashes": {"model": "82b5f664ae"}, "prompt": "cat made of spaghetti, perfect composition, masterpiece, best quality, <lora:add-detail-xl:-1>", "Version": "v1.5.1", "sampler": "DPM++ SDE Karras", "cfgScale": 7, "resources": [{"hash": "82b5f664ae", "name": "dreamshaperXL10_alpha2Xl10", "type": "model"}], "Model hash": "82b5f664ae", "add-detail-xl": "1>\"", "\"add-detail-xl": "9c783c8ce46c\""}, "availability": "Public", "hasMeta": true, "hasPositivePrompt": true, "onSite": false, "remixOfId": null}, {"url": "https://image.civitai.com/xG1nkqKTMzGDvpLrqFT7WA/0e537b14-d13c-4053-90f8-4ccc081fdf74/width=1797/1917153.jpeg", "nsfwLevel": 1, "width": 1797, "height": 1158, "hash": "UFON2nRjRj={~qM{RjS$?c%2t7R-%gNGWVaK", "type": "image", "metadata": {"hash": "UFON2nRjRj={~qM{RjS$?c%2t7R-%gNGWVaK", "size": 1378156, "width": 1797, "height": 1158}, "minor": false, "poi": false, "meta": {"Size": "896x1024", "seed": 1603824148, "Model": "dreamshaperXL10_alpha2Xl10", "steps": 20, "Script": "X/Y/Z plot", "X Type": "Prompt S/R", "hashes": {"model": "82b5f664ae"}, "prompt": "dog, low poly, white background, <lora:add-detail-xl:-2>", "Version": "v1.5.1", "sampler": "DPM++ SDE Karras", "cfgScale": 7, "resources": [{"hash": "82b5f664ae", "name": "dreamshaperXL10_alpha2Xl10", "type": "model"}], "Model hash": "82b5f664ae", "add-detail-xl": "0>\"", "\"add-detail-xl": "9c783c8ce46c\""}, "availability": "Public", "hasMeta": true, "hasPositivePrompt": true, "onSite": false, "remixOfId": null}, {"url": "https://image.civitai.com/xG1nkqKTMzGDvpLrqFT7WA/08a14430-af56-40a1-971f-4180f187626d/width=1800/1917154.jpeg", "nsfwLevel": 1, "width": 2698, "height": 1158, "hash": "URIXv?RQkCV@?wRjt7kC~qkCbHWV_3t7R*t7", "type": "image", "metadata": {"hash": "URIXv?RQkCV@?wRjt7kC~qkCbHWV_3t7R*t7", "size": 2706285, "width": 2698, "height": 1158}, "minor": false, "poi": false, "meta": {"Size": "896x1024", "seed": 447634022, "Model": "dreamshaperXL10_alpha2Xl10", "steps": 20, "Script": "X/Y/Z plot", "X Type": "Prompt S/R", "hashes": {"model": "82b5f664ae"}, "prompt": "anime girl eating ramen, <lora:add-detail-xl:-2>", "Version": "v1.5.1", "sampler": "DPM++ SDE Karras", "cfgScale": 7, "resources": [{"hash": "82b5f664ae", "name": "dreamshaperXL10_alpha2Xl10", "type": "model"}], "Model hash": "82b5f664ae", "add-detail-xl": "2>\"", "\"add-detail-xl": "9c783c8ce46c\""}, "availability": "Public", "hasMeta": true, "hasPositivePrompt": true, "onSite": false, "remixOfId": null}], "downloadUrl": "https://civitai.com/api/download/models/135867", "creator": {"username": "w4r10ck", "image": "https://lh3.googleusercontent.com/a/AAcHTteNN1NjuPJVdxuJEu6YKAGW4_OLYzed3y0xbLe9=s96-c"}}, "tags": ["concept", "detailed", "detail", "enhancer", "undetailed"], "modelDescription": "<p>Detail tweaker for SDXL.</p><p>Works with weights [-3, 3]</p><p>Use positive weight to increase details and negative weight to reduce details.</p><p>Good weight depends on your prompt and number of sampling steps, I recommend starting at 1.5 and then adjusting it.</p>", "civitai_deleted": false, "favorite": false, "exclude": false, "usage_tips": "{}"}