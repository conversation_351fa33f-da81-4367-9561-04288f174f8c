# 🤖 LoRA Auto-Renamer

Automatically renames new LoRA models using intelligent naming convention with creator attribution.

## 📁 Files

- **`auto_rename_new_models.py`** - Main automation script
- **`rename_new_models.bat`** - Windows batch file for easy execution  
- **`rename_config.json`** - Configuration file for customization
- **`README.md`** - This documentation

## 🚀 Quick Start

### Method 1: Scan All New Models
Double-click `rename_new_models.bat` or run:
```bash
python automation/auto_rename_new_models.py
```

### Method 2: Rename Specific File
Drag & drop a `.safetensors` file onto `rename_new_models.bat` or run:
```bash
python automation/auto_rename_new_models.py "path/to/model.safetensors"
```

## ✨ Features

- **🔍 Auto-Detection** - Finds models that don't follow naming convention
- **📊 Metadata Analysis** - Extracts creator, character, base model info
- **🎯 Intelligent Naming** - Applies `{Creator}_{Subject}_{Category}_{BaseModel}` format
- **📁 Smart Categorization** - Suggests correct directory based on character
- **📝 Complete Renaming** - Handles .safetensors, .metadata.json, .webp, .mp4
- **🔧 Metadata Updates** - Fixes internal file paths

## 🎛️ Configuration

Edit `rename_config.json` to customize:

```json
{
  "character_mappings": {
    "lois": "Lois_Griffin",
    "jinx": "Jinx_DC",
    "nisha": "Nisha_BlueDingo"
  },
  "franchise_directories": {
    "Family_Guy": "Cartoon/Character/Family_Guy",
    "DC_Comics": "Cartoon/Character/DC_Comics"
  },
  "naming_convention": "{Creator}_{Subject}_{Category}_{BaseModel}",
  "auto_move_characters": true,
  "use_hyphens_for_artists": true
}
```

## 📋 Naming Convention

**Characters:** `JLFO_Lois_Griffin_Char_Illustrious`
**Artists:** `redtvpe_Range-Murata-Style_ArtStyle_Illustrious`  
**Styles:** `zura_janai_retro_artstyle_Style_Illustrious`
**Concepts:** `Arbusyss_Goblin_Concept_Illustrious`
**Quality:** `w4r10ck_Detail_Tweaker_XL_Quality_SDXL_1.0`

## 🔄 Workflow

1. Install new LoRA models (however you normally do)
2. Run the auto-renamer (double-click batch file)
3. Review suggestions and confirm
4. Done! Models properly renamed and organized

## 📂 Directory Structure

The script automatically suggests directories based on character franchises:

```
Cartoon/Character/
├── MLP/              # My Little Pony
├── Family_Guy/       # Family Guy characters  
├── Simpsons/         # Simpsons characters
├── Rick_and_Morty/   # Rick & Morty characters
├── DC_Comics/        # DC Comics characters
├── Other_Western/    # Other western/original characters
└── Anime/            # Anime characters
```

## 🛠️ Adding New Characters

To add support for new characters, edit `rename_config.json`:

```json
{
  "character_mappings": {
    "new_character": "Proper_Character_Name",
    "another_char": "Another_Character_Franchise"
  }
}
```

## 📝 Example Output

```
🤖 Automatic LoRA Model Renamer
==================================================
🔍 Scanning for models that need renaming...
📊 Found 2 models to process

📋 Rename Preview:
--------------------------------------------------
Current: some_lois_model_v2
New:     JLFO_Lois_Griffin_Char_Illustrious
Creator: JLFO
Subject: Lois_Griffin
Move to: Cartoon/Character/Family_Guy

Current: artist_style_model
New:     redtvpe_Range-Murata-Style_ArtStyle_Illustrious
Creator: redtvpe
Subject: Range-Murata-Style

Process 2 models? (y/N): y

🚀 Processing models...
✅ Successfully processed 2/2 models
```

## 🔧 Troubleshooting

**No metadata found:** Ensure .metadata.json files exist alongside .safetensors files
**Wrong categorization:** Update character mappings in config file
**Path issues:** Run from the main loras directory, not the automation folder

---

*This system learns from your existing organization and applies intelligent naming to new models automatically!* 🚀
