#!/usr/bin/env python3
"""
Checkpoint Model Analyzer and Intelligent Renamer
Analyzes all checkpoint .metadata.json files to extract creator information and generate intelligent renames
"""

import json
import os
import re
from pathlib import Path
from typing import Dict, List, Optional

def clean_filename(text: str) -> str:
    """Clean text for use in filenames"""
    # Remove or replace invalid filename characters
    text = re.sub(r'[<>:"/\\|?*]', '', text)
    text = re.sub(r'\s+', '_', text.strip())
    text = re.sub(r'[^\w\-_.]', '', text)
    return text

def extract_model_info_from_metadata(metadata: dict) -> Dict[str, str]:
    """Extract model name, version, and other info from metadata"""
    file_name = metadata.get('file_name', '')
    model_name = metadata.get('model_name', '')
    
    # Extract version information
    version = "v1.0"  # default
    
    # Look for version patterns in filename or model name
    version_patterns = [
        r'v(\d+\.?\d*)',
        r'V(\d+\.?\d*)', 
        r'_v(\d+)',
        r'_V(\d+)',
        r'(\d+\.?\d+)$'
    ]
    
    for pattern in version_patterns:
        match = re.search(pattern, file_name)
        if match:
            version = f"v{match.group(1)}"
            break
        match = re.search(pattern, model_name)
        if match:
            version = f"v{match.group(1)}"
            break
    
    # Extract clean model name (remove version and common suffixes)
    clean_model_name = model_name
    if clean_model_name:
        # Remove version info
        clean_model_name = re.sub(r'v\d+\.?\d*', '', clean_model_name, flags=re.IGNORECASE)
        clean_model_name = re.sub(r'V\d+\.?\d*', '', clean_model_name)
        # Remove common checkpoint suffixes
        clean_model_name = re.sub(r'(XL|SDXL|_SDXL)', '', clean_model_name, flags=re.IGNORECASE)
        clean_model_name = clean_model_name.strip('_- ')
    
    if not clean_model_name:
        clean_model_name = file_name.split('_')[0] if '_' in file_name else file_name
    
    return {
        'model_name': clean_filename(clean_model_name),
        'version': clean_filename(version),
        'original_name': model_name
    }

def determine_checkpoint_category(file_path: str, metadata: dict) -> str:
    """Determine category based on file path and metadata"""
    path_parts = Path(file_path).parts
    
    # Extract category from path
    if 'Cartoon' in path_parts:
        return 'Cartoon'
    elif 'Realistic' in path_parts:
        return 'Realistic'
    elif 'Semi-Realism' in path_parts:
        return 'SemiRealistic'
    elif 'All-Purpose' in path_parts:
        return 'AllPurpose'
    else:
        return 'General'

def analyze_checkpoint_metadata_file(metadata_path: str) -> Optional[Dict]:
    """Analyze a single checkpoint metadata file and extract renaming information"""
    try:
        with open(metadata_path, 'r', encoding='utf-8') as f:
            metadata = json.load(f)
        
        # Extract creator
        creator = "Unknown"
        if 'civitai' in metadata and 'creator' in metadata['civitai']:
            creator = metadata['civitai']['creator'].get('username', 'Unknown')
        
        # Extract base model info
        base_model = metadata.get('base_model', 'SDXL')
        if base_model == 'Unknown' and 'civitai' in metadata:
            base_model = metadata['civitai'].get('baseModel', 'SDXL')
        
        # Extract model information
        model_info = extract_model_info_from_metadata(metadata)
        
        # Determine category
        category = determine_checkpoint_category(metadata_path, metadata)
        
        # Get current filename without extension
        current_name = metadata.get('file_name', Path(metadata_path).stem.replace('.metadata', ''))
        
        # Generate new name: {ModelName}_{Version}
        new_name = f"{model_info['model_name']}_{model_info['version']}"
        
        return {
            'current_name': current_name,
            'new_name': new_name,
            'creator': creator,
            'model_name': model_info['model_name'],
            'original_model_name': model_info['original_name'],
            'version': model_info['version'],
            'category': category,
            'base_model': base_model,
            'metadata_path': metadata_path,
            'file_path': metadata.get('file_path', '')
        }
        
    except Exception as e:
        print(f"Error analyzing {metadata_path}: {e}")
        return None

def find_all_checkpoint_metadata_files(root_dir: str) -> List[str]:
    """Find all .metadata.json files in the checkpoints directory tree"""
    metadata_files = []
    for root, dirs, files in os.walk(root_dir):
        for file in files:
            if file.endswith('.metadata.json'):
                metadata_files.append(os.path.join(root, file))
    return metadata_files

def main():
    """Main analysis function for checkpoints"""
    root_dir = "../checkpoints"  # Checkpoints directory
    
    print("🔍 Analyzing Checkpoint metadata files...")
    metadata_files = find_all_checkpoint_metadata_files(root_dir)
    print(f"Found {len(metadata_files)} checkpoint metadata files")
    
    analysis_results = []
    
    for metadata_file in metadata_files:
        result = analyze_checkpoint_metadata_file(metadata_file)
        if result:
            analysis_results.append(result)
    
    print(f"\n📊 Checkpoint Analysis Results ({len(analysis_results)} models):")
    print("=" * 80)
    
    # Group by category
    by_category = {}
    for result in analysis_results:
        category = result['category']
        if category not in by_category:
            by_category[category] = []
        by_category[category].append(result)
    
    for category, results in by_category.items():
        print(f"\n🏷️  {category.upper()} ({len(results)} models):")
        print("-" * 40)
        
        for result in results:
            print(f"Current: {result['current_name']}")
            print(f"New:     {result['new_name']}")
            print(f"Creator: {result['creator']}")
            print(f"Model:   {result['model_name']} ({result['version']})")
            print(f"Base:    {result['base_model']}")
            print()
    
    # Save results to JSON for the renaming script
    with open('checkpoint_renaming_plan.json', 'w', encoding='utf-8') as f:
        json.dump(analysis_results, f, indent=2, ensure_ascii=False)
    
    print(f"💾 Checkpoint renaming plan saved to 'checkpoint_renaming_plan.json'")
    print(f"📝 Ready to proceed with renaming {len(analysis_results)} checkpoint models")

if __name__ == "__main__":
    main()
