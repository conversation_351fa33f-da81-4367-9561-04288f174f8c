#!/usr/bin/env python3
"""
LoRA Metadata Analyzer and Intelligent Renamer
Analyzes all .metadata.json files to extract creator information and generate intelligent renames
"""

import json
import os
import re
from pathlib import Path
from typing import Dict, List, Tuple, Optional

def clean_filename(text: str) -> str:
    """Clean text for use in filenames"""
    # Remove or replace invalid filename characters
    text = re.sub(r'[<>:"/\\|?*]', '', text)
    # For artist names with spaces, use hyphens; for others use underscores
    if 'Style' in text and (' ' in text or '_' in text):
        text = re.sub(r'[\s_]+', '-', text.strip())
    else:
        text = re.sub(r'\s+', '_', text.strip())
    text = re.sub(r'[^\w\-_.]', '', text)
    return text

def extract_subject_from_metadata(metadata: dict, metadata_path: str = '') -> str:
    """Extract the main subject/character from metadata"""
    # Special handling for specific characters
    file_name = metadata.get('file_name', '')
    model_name = metadata.get('model_name', '')

    # If file_name is None or empty, extract from path
    if not file_name and metadata_path:
        file_name = Path(metadata_path).stem.replace('.metadata', '')

    # Ensure we have strings to work with
    file_name = file_name or ''
    model_name = model_name or ''

    # Special cases for better character name extraction
    if 'lois' in file_name.lower() or 'lois' in model_name.lower():
        return "Lois_Griffin"
    elif 'meg' in file_name.lower() and ('griffin' in model_name.lower() or 'family' in model_name.lower()):
        return "Meg_Griffin"
    elif 'marge' in file_name.lower() or 'marge' in model_name.lower():
        return "Marge_Simpson"
    elif 'summer' in file_name.lower() and 'smith' in file_name.lower():
        return "Summer_Smith"
    elif 'tricia' in file_name.lower():
        return "Tricia_Lange"
    elif 'dexter' in file_name.lower() and 'mom' in file_name.lower():
        return "Dexters_Mom"
    elif 'gina' in file_name.lower() and 'jabowski' in file_name.lower():
        return "Gina_Jabowski"
    elif 'vicky' in file_name.lower():
        return "Vicky_FairlyOddParents"
    elif 'ember' in file_name.lower() and 'mclain' in file_name.lower():
        return "Ember_McLain"
    elif 'alcina' in file_name.lower() and 'dimitrescu' in file_name.lower():
        return "Alcina_Dimitrescu"
    elif 'jinx' in file_name.lower():
        return "Jinx_DC"  # Fixed: Jinx is from DC, not LoL
    elif 'nisha' in file_name.lower() or 'n1sh4' in file_name.lower():
        return "Nisha_BlueDingo"  # Fixed: Nisha by BlueDingo
    elif 'anubis' in file_name.lower() and 'bom39' in file_name.lower():
        return "Anubis_Bom39"  # Fixed: Anubis by Bom39
    elif 'daybreaker' in file_name.lower() or 'daybreaker' in model_name.lower():
        return "Daybreaker_MLP"
    elif 'celestia' in file_name.lower() or 'celestia' in model_name.lower():
        return "Princess_Celestia_MLP"
    elif 'yzma' in file_name.lower():
        return "Yzma_Disney"
    elif 'zira' in file_name.lower():
        return "Zira_LionKing"
    elif 'fourarms' in file_name.lower():
        return "Four_Arms_Ben10"
    elif 'range' in file_name.lower() and 'murata' in file_name.lower():
        return "Range-Murata-Style"  # Fixed: Use hyphens instead of underscores
    elif 'john' in file_name.lower() and 'harris' in file_name.lower():
        return "John-Harris-Style"  # Fixed: Use hyphens instead of underscores

    # Try trained words
    if 'civitai' in metadata and 'trainedWords' in metadata['civitai']:
        trained_words = metadata['civitai']['trainedWords']
        if trained_words and len(trained_words) > 0:
            # Take the first trained word as primary subject
            subject = trained_words[0].split(',')[0].strip()
            return clean_filename(subject)

    # Fallback to model name parsing
    if model_name:
        # Extract character/subject from model name
        # Common patterns: "Character Name (Series)", "Character Name - Series", etc.
        match = re.search(r'^([^(\-]+)', model_name)
        if match:
            return clean_filename(match.group(1).strip())

    return "Unknown"

def determine_category(file_path: str, metadata: dict) -> str:
    """Determine category based on file path and metadata"""
    path_parts = Path(file_path).parts

    # Extract category from path
    if 'Character' in path_parts:
        return 'Character'
    elif 'Artist' in path_parts:
        return 'ArtStyle'
    elif 'Style' in path_parts:
        return 'Style'
    elif 'Concept' in path_parts:
        return 'Concept'
    elif 'Quality' in path_parts:
        return 'Quality'
    else:
        return 'Misc'

def get_character_franchise(subject: str, file_path: str) -> str:
    """Determine which franchise/category a character belongs to"""
    # NSFW original characters should go to Other_Western
    if 'Nisha_BlueDingo' in subject or 'Anubis_Bom39' in subject:
        return 'Other_Western'

    # Check current path to maintain existing organization
    path_parts = Path(file_path).parts

    if 'MLP' in path_parts or 'Daybreaker' in subject or 'Princess_Celestia' in subject:
        return 'MLP'
    elif 'Family_Guy' in path_parts or 'Griffin' in subject:
        return 'Family_Guy'
    elif 'Simpsons' in path_parts or 'Simpson' in subject:
        return 'Simpsons'
    elif 'Rick_and_Morty' in path_parts or 'Smith' in subject or 'Lange' in subject:
        return 'Rick_and_Morty'
    elif 'Danny_Phantom' in path_parts or 'McLain' in subject:
        return 'Danny_Phantom'
    elif 'Disney' in path_parts or 'Disney' in subject or 'LionKing' in subject:
        return 'Disney'
    elif 'Ben_10' in path_parts or 'Ben10' in subject:
        return 'Ben_10'
    elif 'Resident_Evil' in path_parts or 'Dimitrescu' in subject:
        return 'Resident_Evil'
    elif 'League_of_Legends' in path_parts or ('Jinx' in subject and 'DC' not in subject):
        return 'League_of_Legends'
    elif 'DC' in subject:
        return 'DC_Comics'
    elif 'Anime' in path_parts:
        return 'Anime'
    else:
        return 'Other_Western'

def analyze_metadata_file(metadata_path: str) -> Optional[Dict]:
    """Analyze a single metadata file and extract renaming information"""
    try:
        with open(metadata_path, 'r', encoding='utf-8') as f:
            metadata = json.load(f)
        
        # Extract creator
        creator = "Unknown"
        if 'civitai' in metadata and 'creator' in metadata['civitai']:
            creator = metadata['civitai']['creator'].get('username', 'Unknown')
        
        # Extract base model
        base_model = metadata.get('base_model', 'Unknown')
        if base_model == 'Unknown' and 'civitai' in metadata:
            base_model = metadata['civitai'].get('baseModel', 'Unknown')
        
        # Extract subject
        subject = extract_subject_from_metadata(metadata, metadata_path)
        
        # Determine category
        category = determine_category(metadata_path, metadata)
        
        # Get current filename without extension
        current_name = metadata.get('file_name') or Path(metadata_path).stem.replace('.metadata', '')
        
        # For characters, determine franchise and include it
        if category == 'Character':
            franchise = get_character_franchise(subject, metadata_path)
            new_name = f"{clean_filename(creator)}_{clean_filename(subject)}_{category}_{clean_filename(base_model)}"
        else:
            new_name = f"{clean_filename(creator)}_{clean_filename(subject)}_{category}_{clean_filename(base_model)}"
        
        return {
            'current_name': current_name,
            'new_name': new_name,
            'creator': creator,
            'subject': subject,
            'category': category,
            'base_model': base_model,
            'metadata_path': metadata_path,
            'model_name': metadata.get('model_name', ''),
            'file_path': metadata.get('file_path', '')
        }
        
    except Exception as e:
        print(f"Error analyzing {metadata_path}: {e}")
        return None

def find_all_metadata_files(root_dir: str) -> List[str]:
    """Find all .metadata.json files in the directory tree"""
    metadata_files = []
    for root, dirs, files in os.walk(root_dir):
        for file in files:
            if file.endswith('.metadata.json'):
                metadata_files.append(os.path.join(root, file))
    return metadata_files

def main():
    """Main analysis function"""
    root_dir = "."  # Current directory
    
    print("🔍 Analyzing LoRA metadata files...")
    metadata_files = find_all_metadata_files(root_dir)
    print(f"Found {len(metadata_files)} metadata files")
    
    analysis_results = []
    
    for metadata_file in metadata_files:
        result = analyze_metadata_file(metadata_file)
        if result:
            analysis_results.append(result)
    
    print(f"\n📊 Analysis Results ({len(analysis_results)} models):")
    print("=" * 80)
    
    # Group by category
    by_category = {}
    for result in analysis_results:
        category = result['category']
        if category not in by_category:
            by_category[category] = []
        by_category[category].append(result)
    
    for category, results in by_category.items():
        print(f"\n🏷️  {category.upper()} ({len(results)} models):")
        print("-" * 40)
        
        for result in results:
            print(f"Current: {result['current_name']}")
            print(f"New:     {result['new_name']}")
            print(f"Creator: {result['creator']}")
            print(f"Subject: {result['subject']}")
            print(f"Base:    {result['base_model']}")
            print()
    
    # Save results to JSON for the renaming script
    with open('renaming_plan.json', 'w', encoding='utf-8') as f:
        json.dump(analysis_results, f, indent=2, ensure_ascii=False)
    
    print(f"💾 Renaming plan saved to 'renaming_plan.json'")
    print(f"📝 Ready to proceed with renaming {len(analysis_results)} models")

if __name__ == "__main__":
    main()
