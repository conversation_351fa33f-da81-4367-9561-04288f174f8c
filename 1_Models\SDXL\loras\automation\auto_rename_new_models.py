#!/usr/bin/env python3
"""
Automatic LoRA Model Renamer
Automatically renames new LoRA models using intelligent naming convention:
{Creator}_{Subject}_{Category}_{BaseModel}

Usage:
1. Run after installing new models: python auto_rename_new_models.py
2. Or run with specific file: python auto_rename_new_models.py "filename.safetensors"
"""

import json
import os
import sys
import shutil
import re
from pathlib import Path
from typing import Dict, List, Optional

# Load configuration
def load_config():
    """Load configuration from config file"""
    config_path = Path(__file__).parent / 'rename_config.json'
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"⚠️  Could not load config: {e}")
        return {
            'character_mappings': {
                'lois': '<PERSON>_<PERSON>',
                'meg': '<PERSON>_<PERSON>',
                'marge': '<PERSON><PERSON><PERSON><PERSON>',
                'summer': '<PERSON><PERSON><PERSON>',
                'tricia': '<PERSON><PERSON>_<PERSON>',
                'jinx': '<PERSON><PERSON>_<PERSON>',
                'nisha': '<PERSON><PERSON>_BlueDingo',
                'n1sh4': '<PERSON><PERSON>_BlueDingo',
                'anubis': 'Anubi<PERSON>_Bom39',
                'daybreaker': 'Daybreaker_MLP',
                'celestia': 'Princess_Celestia_MLP',
                'pearl': 'Pearl_StevenUniverse',
                'illupearl': 'Pearl_StevenUniverse',
                'ember': 'Ember_McLain',
                'alcina': 'Alcina_Dimitrescu',
                'yzma': 'Yzma_Disney',
                'zira': 'Zira_LionKing',
                'fourarms': 'Four_Arms_Ben10',
                'dexter': 'Dexters_Mom',
                'gina': 'Gina_Jabowski',
                'vicky': 'Vicky_FairlyOddParents'
            },
            'use_hyphens_for_artists': True
        }

# Global config
CONFIG = load_config()
CHARACTER_MAP = CONFIG.get('character_mappings', {})

def clean_filename(text: str) -> str:
    """Clean text for use in filenames"""
    text = re.sub(r'[<>:"/\\|?*]', '', text)
    # Use hyphens for artist names if configured, underscores for others
    if CONFIG.get('use_hyphens_for_artists', True) and 'style' in text.lower():
        text = re.sub(r'[\s_]+', '-', text.strip())
    else:
        text = re.sub(r'\s+', '_', text.strip())
    text = re.sub(r'[^\w\-_.]', '', text)
    return text

def extract_character_from_filename(filename: str, metadata: dict) -> str:
    """Extract character name from filename and metadata"""
    filename_lower = filename.lower()

    # Check character map first
    for key, value in CHARACTER_MAP.items():
        if key in filename_lower:
            return value

    # Try to extract from model name first (more descriptive than trigger words)
    if metadata and 'model_name' in metadata:
        model_name = metadata['model_name']
        # Extract main subject from model name, removing version info and brackets
        clean_model_name = re.sub(r'\s*[\[\(].*?[\]\)]', '', model_name)  # Remove bracketed info
        clean_model_name = re.sub(r'\s*-\s*.*$', '', clean_model_name)    # Remove everything after dash
        clean_model_name = clean_model_name.strip()
        if clean_model_name and len(clean_model_name) > 2:
            return clean_filename(clean_model_name)

    # Try civitai model name as backup
    if metadata and 'civitai' in metadata and 'model' in metadata['civitai'] and 'name' in metadata['civitai']['model']:
        civitai_name = metadata['civitai']['model']['name']
        clean_civitai_name = re.sub(r'\s*[\[\(].*?[\]\)]', '', civitai_name)
        clean_civitai_name = re.sub(r'\s*-\s*.*$', '', clean_civitai_name)
        clean_civitai_name = clean_civitai_name.strip()
        if clean_civitai_name and len(clean_civitai_name) > 2:
            return clean_filename(clean_civitai_name)

    # Only use trained words as last resort (often just trigger words, not descriptive)
    if metadata and 'civitai' in metadata and 'trainedWords' in metadata['civitai']:
        trained_words = metadata['civitai']['trainedWords']
        if trained_words and len(trained_words) > 0:
            subject = trained_words[0].split(',')[0].strip()
            # Skip if it's clearly just a trigger word (short, cryptic)
            if len(subject) > 4 and not re.match(r'^[a-z]+\d*$', subject):
                return clean_filename(subject)
    
    # Fallback to filename parsing
    # More conservative approach - only remove obvious version numbers and model suffixes at the end
    clean_name = re.sub(r'[-_]+(v\d+|V\d+|\d+)$', '', filename)  # Remove version numbers at end
    clean_name = re.sub(r'[-_]+(XL|SDXL|Illustrious|Pony)$', '', clean_name, flags=re.IGNORECASE)  # Remove model suffixes at end
    clean_name = re.sub(r'[-_]+', '_', clean_name).strip('_-')  # Normalize separators

    return clean_filename(clean_name) if clean_name else "Unknown"

def determine_category_from_path(file_path: str) -> str:
    """Determine category from file location"""
    path_parts = Path(file_path).parts
    
    if 'Character' in path_parts:
        return 'Char'
    elif 'Artist' in path_parts:
        return 'ArtStyle'
    elif 'Style' in path_parts:
        return 'Style'
    elif 'Concept' in path_parts:
        return 'Concept'
    elif 'Quality' in path_parts:
        return 'Quality'
    else:
        return 'Misc'

def suggest_target_directory(subject: str, category: str) -> str:
    """Suggest target directory based on subject and category"""
    if category != 'Char':
        return None  # Don't auto-move non-character models
    
    # Character franchise detection
    if any(x in subject for x in ['MLP', 'Daybreaker', 'Celestia']):
        return 'Cartoon/Character/MLP'
    elif 'Griffin' in subject:
        return 'Cartoon/Character/Family_Guy'
    elif 'Simpson' in subject:
        return 'Cartoon/Character/Simpsons'
    elif any(x in subject for x in ['Smith', 'Lange']):
        return 'Cartoon/Character/Rick_and_Morty'
    elif 'McLain' in subject:
        return 'Cartoon/Character/Danny_Phantom'
    elif any(x in subject for x in ['Disney', 'LionKing']):
        return 'Cartoon/Character/Disney'
    elif 'Ben10' in subject:
        return 'Cartoon/Character/Ben_10'
    elif 'Dimitrescu' in subject:
        return 'Cartoon/Character/Resident_Evil'
    elif 'DC' in subject:
        return 'Cartoon/Character/DC_Comics'
    elif any(x in subject for x in ['BlueDingo', 'Bom39', 'StevenUniverse', 'Dexters', 'Jabowski', 'FairlyOddParents']):
        return 'Cartoon/Character/Other_Western'
    else:
        return 'Cartoon/Character/Anime'

def analyze_model_file(file_path: str) -> Optional[Dict]:
    """Analyze a model file and generate rename suggestion"""
    base_path = Path(file_path).parent
    base_name = Path(file_path).stem
    
    # Look for metadata file
    metadata_path = base_path / f"{base_name}.metadata.json"
    if not metadata_path.exists():
        print(f"⚠️  No metadata found for {base_name}")
        return None
    
    try:
        with open(metadata_path, 'r', encoding='utf-8') as f:
            metadata = json.load(f)
    except Exception as e:
        print(f"❌ Error reading metadata for {base_name}: {e}")
        return None

    # Skip if metadata is None or empty
    if not metadata:
        print(f"⚠️  Empty metadata for {base_name}")
        return None
    
    # Extract information
    creator = "Unknown"
    if metadata and 'civitai' in metadata and 'creator' in metadata['civitai']:
        creator = metadata['civitai']['creator'].get('username', 'Unknown')
    
    base_model = "Unknown"
    if metadata:
        base_model = metadata.get('base_model', 'Unknown')
        if base_model == 'Unknown' and 'civitai' in metadata:
            base_model = metadata['civitai'].get('baseModel', 'Unknown')
    
    subject = extract_character_from_filename(base_name, metadata)
    category = determine_category_from_path(file_path)
    
    # Generate new name
    new_name = f"{clean_filename(creator)}_{clean_filename(subject)}_{category}_{clean_filename(base_model)}"
    
    # Suggest target directory
    target_dir = suggest_target_directory(subject, category)
    
    return {
        'current_path': file_path,
        'current_name': base_name,
        'new_name': new_name,
        'creator': creator,
        'subject': subject,
        'category': category,
        'base_model': base_model,
        'target_directory': target_dir,
        'metadata_path': str(metadata_path)
    }

def find_new_models() -> List[str]:
    """Find models that don't follow the naming convention"""
    new_models = []
    
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.safetensors'):
                file_path = os.path.join(root, file)
                base_name = Path(file).stem
                
                # Check if it follows our naming convention
                # Our convention: {Creator}_{Subject}_{Category}_{BaseModel}
                parts = base_name.split('_')
                if len(parts) < 4 or not any(cat in base_name for cat in ['Char', 'ArtStyle', 'Style', 'Concept', 'Quality']):
                    new_models.append(file_path)
    
    return new_models

def rename_model_files(analysis: Dict, dry_run: bool = True) -> bool:
    """Rename model files according to analysis"""
    current_path = analysis['current_path']
    current_name = analysis['current_name']
    new_name = analysis['new_name']
    target_dir = analysis['target_directory']
    
    base_dir = Path(current_path).parent
    
    # Determine target directory
    if target_dir and target_dir != str(base_dir):
        final_dir = Path(target_dir)
        if not dry_run:
            final_dir.mkdir(parents=True, exist_ok=True)
    else:
        final_dir = base_dir
    
    # Find all associated files
    extensions = ['.safetensors', '.metadata.json', '.webp', '.mp4']
    files_to_rename = []
    
    for ext in extensions:
        old_file = base_dir / f"{current_name}{ext}"
        if old_file.exists():
            new_file = final_dir / f"{new_name}{ext}"
            files_to_rename.append((str(old_file), str(new_file)))
    
    if not files_to_rename:
        print(f"⚠️  No files found for {current_name}")
        return False
    
    if dry_run:
        print(f"🔍 Would rename {current_name} → {new_name}")
        if target_dir and target_dir != str(base_dir):
            print(f"   Would move to: {target_dir}")
        for old_path, new_path in files_to_rename:
            print(f"   {Path(old_path).name} → {Path(new_path).name}")
        return True
    else:
        # Actually rename files
        try:
            for old_path, new_path in files_to_rename:
                shutil.move(old_path, new_path)
                print(f"✅ Moved: {Path(old_path).name} → {Path(new_path).name}")
            
            # Update metadata file
            metadata_file = final_dir / f"{new_name}.metadata.json"
            if metadata_file.exists():
                with open(metadata_file, 'r', encoding='utf-8') as f:
                    metadata = json.load(f)
                
                metadata['file_name'] = new_name
                metadata['file_path'] = str(final_dir / f"{new_name}.safetensors")
                if 'preview_url' in metadata:
                    metadata['preview_url'] = str(final_dir / f"{new_name}.webp")
                
                with open(metadata_file, 'w', encoding='utf-8') as f:
                    json.dump(metadata, f, indent=2, ensure_ascii=False)
                
                print(f"📝 Updated metadata for {new_name}")
            
            return True
            
        except Exception as e:
            print(f"❌ Error renaming {current_name}: {e}")
            return False

def main():
    """Main function"""
    print("🤖 Automatic LoRA Model Renamer")
    print("=" * 50)
    
    # Check if specific file provided
    if len(sys.argv) > 1:
        target_file = sys.argv[1]
        if not os.path.exists(target_file):
            print(f"❌ File not found: {target_file}")
            return
        models_to_process = [target_file]
        print(f"📁 Processing specific file: {target_file}")
    else:
        # Find all new models
        print("🔍 Scanning for models that need renaming...")
        models_to_process = find_new_models()
        print(f"📊 Found {len(models_to_process)} models to process")
    
    if not models_to_process:
        print("✅ No models need renaming!")
        return
    
    # Analyze all models
    analyses = []
    for model_path in models_to_process:
        analysis = analyze_model_file(model_path)
        if analysis:
            analyses.append(analysis)
    
    if not analyses:
        print("❌ No valid models to process")
        return
    
    # Show preview
    print(f"\n📋 Rename Preview:")
    print("-" * 50)
    for analysis in analyses:
        print(f"Current: {analysis['current_name']}")
        print(f"New:     {analysis['new_name']}")
        print(f"Creator: {analysis['creator']}")
        print(f"Subject: {analysis['subject']}")
        if analysis['target_directory']:
            print(f"Move to: {analysis['target_directory']}")
        print()
    
    # Ask for confirmation
    choice = input(f"Process {len(analyses)} models? (y/N): ").strip().lower()
    if choice == 'y':
        print("\n🚀 Processing models...")
        success_count = 0
        for analysis in analyses:
            if rename_model_files(analysis, dry_run=False):
                success_count += 1
        
        print(f"\n✅ Successfully processed {success_count}/{len(analyses)} models")
    else:
        print("❌ Cancelled")

if __name__ == "__main__":
    main()
