[{"current_name": "albedobaseXL_v31Large", "new_name": "AlbedoBase_v31", "creator": "albedobond", "model_name": "AlbedoBase", "original_model_name": "AlbedoBase XL", "version": "v31", "category": "AllPurpose", "base_model": "SDXL 1.0", "metadata_path": "../checkpoints\\All-Purpose\\albedobaseXL_v31Large.metadata.json", "file_path": "D:/stable_diffusion/1_Models/SDXL/checkpoints/All-Purpose/albedobaseXL_v31Large.safetensors"}, {"current_name": "novaFurryXL_illustriousV8b", "new_name": "Nova_Furry_v8", "creator": "<PERSON><PERSON><PERSON>", "model_name": "Nova_Furry", "original_model_name": "Nova Furry XL", "version": "v8", "category": "Cartoon", "base_model": "Illustrious", "metadata_path": "../checkpoints\\Cartoon\\novaFurryXL_illustriousV8b.metadata.json", "file_path": "D:/stable_diffusion/1_Models/SDXL/checkpoints/Cartoon/novaFurryXL_illustriousV8b.safetensors"}, {"current_name": "obliviousMix_v10", "new_name": "Oblivious_Mix_illustrious_v10", "creator": "Goofy_Ai", "model_name": "Oblivious_Mix_illustrious", "original_model_name": "Oblivious Mix illustrious", "version": "v10", "category": "Cartoon", "base_model": "Illustrious", "metadata_path": "../checkpoints\\Cartoon\\obliviousMix_v10.metadata.json", "file_path": "D:/stable_diffusion/1_Models/SDXL/checkpoints/Cartoon/obliviousMix_v10.safetensors"}, {"current_name": "prefectIllustriousXL_v10", "new_name": "Prefect_illustrious_v10", "creator": "Goofy_Ai", "model_name": "Prefect_illustrious", "original_model_name": "Prefect illustrious <PERSON><PERSON>", "version": "v10", "category": "Cartoon", "base_model": "Illustrious", "metadata_path": "../checkpoints\\Cartoon\\prefectIllustriousXL_v10.metadata.json", "file_path": "D:/stable_diffusion/1_Models/SDXL/checkpoints/Cartoon/prefectIllustriousXL_v10.safetensors"}, {"current_name": "vixonsNoobIllust_v14", "new_name": "Vixons_Noob_Illust_Merge_v14", "creator": "freckled<PERSON>xon", "model_name": "Vix<PERSON>_<PERSON>ob_Illust_Merge", "original_model_name": "Vixon's <PERSON><PERSON>", "version": "v14", "category": "Cartoon", "base_model": "Illustrious", "metadata_path": "../checkpoints\\Cartoon\\vixonsNoobIllust_v14.metadata.json", "file_path": "D:/stable_diffusion/1_Models/SDXL/checkpoints/Cartoon/vixonsNoobIllust_v14.safetensors"}, {"current_name": "waiNSFWIllustrious_v140", "new_name": "WAI-NSFW-illustrious_v140", "creator": "WAI0731", "model_name": "WAI-NSFW-illustrious", "original_model_name": "WAI-NSFW-illustrious-SDXL", "version": "v140", "category": "Cartoon", "base_model": "Illustrious", "metadata_path": "../checkpoints\\Cartoon\\waiNSFWIllustrious_v140.metadata.json", "file_path": "D:/stable_diffusion/1_Models/SDXL/checkpoints/Cartoon/waiNSFWIllustrious_v140.safetensors"}, {"current_name": "epicrealismXL_vxviLastfameRealism", "new_name": "epiCRealism_v1.0", "creator": "epinikion", "model_name": "epiCRealism", "original_model_name": "epiCRealism XL", "version": "v1.0", "category": "Realistic", "base_model": "SDXL 1.0", "metadata_path": "../checkpoints\\Realistic\\epicrealismXL_vxviLastfameRealism.metadata.json", "file_path": "D:/stable_diffusion/1_Models/SDXL/checkpoints/Realistic/epicrealismXL_vxviLastfameRealism.safetensors"}, {"current_name": "juggernautXL_ragnarokBy", "new_name": "Juggernaut_v1.0", "creator": "KandooAI", "model_name": "Juggernaut", "original_model_name": "Juggernaut XL", "version": "v1.0", "category": "Realistic", "base_model": "SDXL 1.0", "metadata_path": "../checkpoints\\Realistic\\juggernautXL_ragnarokBy.metadata.json", "file_path": "D:/stable_diffusion/1_Models/SDXL/checkpoints/Realistic/juggernautXL_ragnarokBy.safetensors"}, {"current_name": "dreamshaperXL_v21TurboDPMSDE", "new_name": "DreamShaper_v21", "creator": "Lykon", "model_name": "DreamShaper", "original_model_name": "DreamShaper XL", "version": "v21", "category": "SemiRealistic", "base_model": "SDXL 1.0", "metadata_path": "../checkpoints\\Semi-Realism\\dreamshaperXL_v21TurboDPMSDE.metadata.json", "file_path": "D:/stable_diffusion/1_Models/SDXL/checkpoints/Semi-Realism/dreamshaperXL_v21TurboDPMSDE.safetensors"}, {"current_name": "MythWeaver_SDXL_V1.0_DA", "new_name": "𝐌𝐘𝐓𝐇𝐖𝐄𝐀𝐕𝐄𝐑_𝐒𝐃𝐗𝐋_𝐯𝟏.𝟎_v1.0", "creator": "DarkAgent", "model_name": "𝐌𝐘𝐓𝐇𝐖𝐄𝐀𝐕𝐄𝐑_𝐒𝐃𝐗𝐋_𝐯𝟏.𝟎", "original_model_name": "✨𝐌𝐘𝐓𝐇𝐖𝐄𝐀𝐕𝐄𝐑 𝐒𝐃𝐗𝐋 𝐯𝟏.𝟎✨", "version": "v1.0", "category": "SemiRealistic", "base_model": "SDXL 1.0", "metadata_path": "../checkpoints\\Semi-Realism\\MythWeaver_SDXL_V1.0_DA.metadata.json", "file_path": "D:/stable_diffusion/1_Models/SDXL/checkpoints/Semi-Realism/MythWeaver_SDXL_V1.0_DA.safetensors"}]