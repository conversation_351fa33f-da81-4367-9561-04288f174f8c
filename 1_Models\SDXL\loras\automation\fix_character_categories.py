#!/usr/bin/env python3
"""
Fix character categorization for <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and Jinx
"""

import os
import shutil
from pathlib import Path

def move_files_with_pattern(pattern: str, source_dir: str, target_dir: str):
    """Move all files matching a pattern from source to target directory"""
    try:
        # Create target directory if it doesn't exist
        os.makedirs(target_dir, exist_ok=True)
        
        # Find all files matching the pattern
        moved_files = []
        for file in os.listdir(source_dir):
            if pattern.lower() in file.lower():
                source_path = os.path.join(source_dir, file)
                target_path = os.path.join(target_dir, file)
                
                if os.path.exists(target_path):
                    print(f"⚠️  Target exists: {target_path}")
                    continue
                
                shutil.move(source_path, target_path)
                moved_files.append(file)
                print(f"✅ Moved: {file}")
        
        return moved_files
        
    except Exception as e:
        print(f"❌ Error moving {pattern}: {e}")
        return []

def main():
    """Fix character categorizations"""
    print("🔧 Fixing character categorizations...")
    
    # Move Nisha (BlueDingo) from Anime to Other_Western
    print("\n📁 Moving Nisha (BlueDingo) to Other_Western...")
    nisha_files = move_files_with_pattern(
        "nisha", 
        "Cartoon/Character/Anime", 
        "Cartoon/Character/Other_Western"
    )
    
    # Move Anubis (Bom39) from Anime to Other_Western  
    print("\n📁 Moving Anubis (Bom39) to Other_Western...")
    anubis_files = move_files_with_pattern(
        "anubis", 
        "Cartoon/Character/Anime", 
        "Cartoon/Character/Other_Western"
    )
    
    # Move Jinx from League_of_Legends to new DC_Comics directory
    print("\n📁 Creating DC_Comics directory and moving Jinx...")
    os.makedirs("Cartoon/Character/DC_Comics", exist_ok=True)
    
    jinx_files = move_files_with_pattern(
        "jinx", 
        "Cartoon/Character/League_of_Legends", 
        "Cartoon/Character/DC_Comics"
    )
    
    print(f"\n✅ Character categorization fixes complete!")
    print(f"📊 Files moved:")
    print(f"   Nisha: {len(nisha_files)} files")
    print(f"   Anubis: {len(anubis_files)} files") 
    print(f"   Jinx: {len(jinx_files)} files")

if __name__ == "__main__":
    main()
