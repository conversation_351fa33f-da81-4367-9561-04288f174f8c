#!/usr/bin/env python3
"""
Fix specific LoRA naming issues based on user feedback
"""

import os
import shutil
from pathlib import Path

def rename_files_with_pattern(old_pattern: str, new_pattern: str, directory: str):
    """Rename all files matching old_pattern to new_pattern in directory"""
    try:
        renamed_files = []
        for file in os.listdir(directory):
            if old_pattern in file:
                old_path = os.path.join(directory, file)
                new_file = file.replace(old_pattern, new_pattern)
                new_path = os.path.join(directory, new_file)
                
                if os.path.exists(new_path):
                    print(f"⚠️  Target exists: {new_path}")
                    continue
                
                shutil.move(old_path, new_path)
                renamed_files.append((file, new_file))
                print(f"✅ Renamed: {file} → {new_file}")
        
        return renamed_files
        
    except Exception as e:
        print(f"❌ Error renaming {old_pattern}: {e}")
        return []

def main():
    """Fix specific LoRA naming issues"""
    print("🔧 Fixing LoRA naming issues...")
    
    # Fix Jinx: LoL → DC
    print("\n📁 Fixing Jinx (DC Comics, not LoL)...")
    jinx_fixes = []
    jinx_fixes.extend(rename_files_with_pattern(
        "Jinx_LoL", "Jinx_DC", "Cartoon/Character/DC_Comics"
    ))
    
    # Fix Nisha: n1sh4 → Nisha_BlueDingo
    print("\n📁 Fixing Nisha (BlueDingo character)...")
    nisha_fixes = []
    nisha_fixes.extend(rename_files_with_pattern(
        "n1sh4", "Nisha_BlueDingo", "Cartoon/Character/Other_Western"
    ))
    
    # Fix Anubis: 3d → Anubis_Bom39
    print("\n📁 Fixing Anubis (Bom39 character)...")
    anubis_fixes = []
    anubis_fixes.extend(rename_files_with_pattern(
        "3d", "Anubis_Bom39", "Cartoon/Character/Other_Western"
    ))
    
    # Fix artist names: Replace underscores with hyphens in style names
    print("\n📁 Fixing artist name formatting (underscores → hyphens)...")
    
    # Range Murata
    range_murata_fixes = []
    range_murata_fixes.extend(rename_files_with_pattern(
        "Range_Murata_Style", "Range-Murata-Style", "Cartoon/Artist/Contemporary"
    ))
    
    # John Harris
    john_harris_fixes = []
    john_harris_fixes.extend(rename_files_with_pattern(
        "John_Harris_Style", "John-Harris-Style", "Miscellaneous/Artist/Contemporary"
    ))
    
    print(f"\n✅ LoRA naming fixes complete!")
    print(f"📊 Files renamed:")
    print(f"   Jinx (DC): {len(jinx_fixes)} files")
    print(f"   Nisha (BlueDingo): {len(nisha_fixes)} files")
    print(f"   Anubis (Bom39): {len(anubis_fixes)} files")
    print(f"   Range Murata: {len(range_murata_fixes)} files")
    print(f"   John Harris: {len(john_harris_fixes)} files")

if __name__ == "__main__":
    main()
