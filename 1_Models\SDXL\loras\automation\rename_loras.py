#!/usr/bin/env python3
"""
LoRA Intelligent Renamer
Renames .safetensors, .metadata.json, and .webp files based on creator analysis
"""

import json
import os
import shutil
from pathlib import Path
from typing import Dict, List

def find_associated_files(base_name: str, directory: str) -> Dict[str, str]:
    """Find all files associated with a base name (.safetensors, .metadata.json, .webp)"""
    files = {}
    
    # Look for files with the base name
    for ext in ['.safetensors', '.metadata.json', '.webp']:
        potential_file = os.path.join(directory, base_name + ext)
        if os.path.exists(potential_file):
            files[ext] = potential_file
    
    return files

def safe_rename(old_path: str, new_path: str) -> bool:
    """Safely rename a file, avoiding conflicts"""
    try:
        # Check if target already exists
        if os.path.exists(new_path):
            print(f"⚠️  Target exists: {new_path}")
            return False
        
        # Create target directory if it doesn't exist
        os.makedirs(os.path.dirname(new_path), exist_ok=True)
        
        # Rename the file
        shutil.move(old_path, new_path)
        print(f"✅ Renamed: {os.path.basename(old_path)} → {os.path.basename(new_path)}")
        return True
        
    except Exception as e:
        print(f"❌ Error renaming {old_path}: {e}")
        return False

def update_metadata_file_paths(metadata_path: str, new_base_name: str):
    """Update file paths inside metadata.json to reflect new names"""
    try:
        with open(metadata_path, 'r', encoding='utf-8') as f:
            metadata = json.load(f)
        
        # Update file_name
        metadata['file_name'] = new_base_name
        
        # Update file_path if it exists
        if 'file_path' in metadata:
            old_path = metadata['file_path']
            directory = os.path.dirname(old_path)
            metadata['file_path'] = os.path.join(directory, new_base_name + '.safetensors')
        
        # Update preview_url if it exists
        if 'preview_url' in metadata:
            old_path = metadata['preview_url']
            directory = os.path.dirname(old_path)
            metadata['preview_url'] = os.path.join(directory, new_base_name + '.webp')
        
        # Save updated metadata
        with open(metadata_path, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, indent=2, ensure_ascii=False)
        
        print(f"📝 Updated metadata paths in {os.path.basename(metadata_path)}")
        
    except Exception as e:
        print(f"❌ Error updating metadata {metadata_path}: {e}")

def rename_model_files(renaming_plan: List[Dict], dry_run: bool = True):
    """Rename all model files according to the plan"""
    
    print(f"🚀 Starting {'DRY RUN' if dry_run else 'ACTUAL'} renaming process...")
    print(f"📊 Processing {len(renaming_plan)} models")
    print("=" * 80)
    
    success_count = 0
    error_count = 0
    
    for i, item in enumerate(renaming_plan, 1):
        current_name = item['current_name']
        new_name = item['new_name']
        metadata_path = item['metadata_path']
        
        print(f"\n[{i}/{len(renaming_plan)}] Processing: {current_name}")
        print(f"Creator: {item['creator']} | Subject: {item['subject']} | Base: {item['base_model']}")
        
        # Get directory from metadata path
        directory = os.path.dirname(metadata_path)
        
        # Find all associated files
        associated_files = find_associated_files(current_name, directory)
        
        if not associated_files:
            print(f"⚠️  No files found for {current_name}")
            error_count += 1
            continue
        
        print(f"📁 Found files: {list(associated_files.keys())}")
        
        if dry_run:
            print(f"🔍 Would rename to: {new_name}")
            for ext, old_path in associated_files.items():
                new_path = os.path.join(directory, new_name + ext)
                print(f"   {ext}: {os.path.basename(old_path)} → {os.path.basename(new_path)}")
            success_count += 1
        else:
            # Actually rename files
            all_success = True
            renamed_files = {}
            
            # Rename all files
            for ext, old_path in associated_files.items():
                new_path = os.path.join(directory, new_name + ext)
                if safe_rename(old_path, new_path):
                    renamed_files[ext] = new_path
                else:
                    all_success = False
                    break
            
            if all_success:
                # Update metadata file paths
                if '.metadata.json' in renamed_files:
                    update_metadata_file_paths(renamed_files['.metadata.json'], new_name)
                success_count += 1
            else:
                error_count += 1
    
    print("\n" + "=" * 80)
    print(f"📊 Renaming Summary:")
    print(f"✅ Successful: {success_count}")
    print(f"❌ Errors: {error_count}")
    print(f"📁 Total: {len(renaming_plan)}")

def main():
    """Main renaming function"""
    
    # Load renaming plan
    if not os.path.exists('renaming_plan.json'):
        print("❌ renaming_plan.json not found. Run analyze_metadata.py first.")
        return
    
    with open('renaming_plan.json', 'r', encoding='utf-8') as f:
        renaming_plan = json.load(f)
    
    print("🎯 LoRA Intelligent Renamer")
    print(f"📋 Loaded plan for {len(renaming_plan)} models")
    
    # Show preview
    print("\n🔍 Preview of changes:")
    print("-" * 40)
    for item in renaming_plan[:5]:  # Show first 5
        print(f"{item['current_name']} → {item['new_name']}")
    if len(renaming_plan) > 5:
        print(f"... and {len(renaming_plan) - 5} more")
    
    # Ask for confirmation
    print(f"\n❓ This will rename {len(renaming_plan)} models and their associated files.")
    print("   Each model typically has 3 files: .safetensors, .metadata.json, .webp")
    
    choice = input("\nChoose action:\n1. Dry run (preview only)\n2. Execute renaming\n3. Cancel\nEnter choice (1-3): ").strip()
    
    if choice == '1':
        rename_model_files(renaming_plan, dry_run=True)
    elif choice == '2':
        print("\n⚠️  FINAL WARNING: This will permanently rename your files!")
        confirm = input("Type 'YES' to proceed: ").strip()
        if confirm == 'YES':
            rename_model_files(renaming_plan, dry_run=False)
        else:
            print("❌ Cancelled.")
    else:
        print("❌ Cancelled.")

if __name__ == "__main__":
    main()
