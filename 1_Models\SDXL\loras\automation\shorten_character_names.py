#!/usr/bin/env python3
"""
Replace "Character" with "Char" in all character model filenames
"""

import os
import shutil
from pathlib import Path

def rename_files_in_directory(directory: str):
    """Rename all files in directory replacing 'Character' with 'Char'"""
    try:
        renamed_files = []
        if not os.path.exists(directory):
            print(f"⚠️  Directory not found: {directory}")
            return renamed_files
            
        for file in os.listdir(directory):
            if "Character" in file:
                old_path = os.path.join(directory, file)
                new_file = file.replace("Character", "Char")
                new_path = os.path.join(directory, new_file)
                
                if os.path.exists(new_path):
                    print(f"⚠️  Target exists: {new_path}")
                    continue
                
                shutil.move(old_path, new_path)
                renamed_files.append((file, new_file))
                print(f"✅ {os.path.basename(directory)}: {os.path.basename(file)} → {os.path.basename(new_file)}")
        
        return renamed_files
        
    except Exception as e:
        print(f"❌ Error in {directory}: {e}")
        return []

def main():
    """Replace 'Character' with 'Char' in all character model names"""
    print("🔧 Shortening character model names (Character → Char)...")
    
    # List of all character directories
    character_dirs = [
        "Cartoon/Character/MLP",
        "Cartoon/Character/Family_Guy", 
        "Cartoon/Character/Simpsons",
        "Cartoon/Character/Rick_and_Morty",
        "Cartoon/Character/Danny_Phantom",
        "Cartoon/Character/Disney",
        "Cartoon/Character/Ben_10",
        "Cartoon/Character/Resident_Evil",
        "Cartoon/Character/DC_Comics",
        "Cartoon/Character/Other_Western",
        "Cartoon/Character/Anime"
    ]
    
    total_renamed = 0
    
    for directory in character_dirs:
        print(f"\n📁 Processing {directory}...")
        renamed_files = rename_files_in_directory(directory)
        total_renamed += len(renamed_files)
        
        if renamed_files:
            print(f"   Renamed {len(renamed_files)} files")
        else:
            print(f"   No files to rename")
    
    print(f"\n✅ Character name shortening complete!")
    print(f"📊 Total files renamed: {total_renamed}")
    print(f"🎯 All character models now use 'Char' instead of 'Character'")

if __name__ == "__main__":
    main()
