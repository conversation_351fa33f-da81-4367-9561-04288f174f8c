`ImageBatch`ノードは、2つの画像を1つのバッチに結合するために設計されています。画像の寸法が一致しない場合、2番目の画像を自動的にリスケールして最初の画像の寸法に合わせてから結合します。

## 入力

| パラメータ | データ型 | 説明 |
|-----------|-------------|-------------|
| `image1`  | `IMAGE`     | バッチに結合される最初の画像です。必要に応じて、2番目の画像が調整される寸法の基準として機能します。 |
| `image2`  | `IMAGE`     | バッチに結合される2番目の画像です。寸法が異なる場合、最初の画像の寸法に自動的にリスケールされます。 |

## 出力

| パラメータ | データ型 | 説明 |
|-----------|-------------|-------------|
| `image`   | `IMAGE`     | 2番目の画像が必要に応じて最初の画像の寸法にリスケールされた、結合された画像のバッチです。 |
