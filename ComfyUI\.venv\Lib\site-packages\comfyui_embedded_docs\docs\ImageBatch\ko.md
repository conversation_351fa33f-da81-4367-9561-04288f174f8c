`ImageBatch` 노드는 두 개의 이미지를 하나의 배치로 결합하도록 설계되었습니다. 이미지의 크기가 일치하지 않으면, 결합하기 전에 두 번째 이미지를 첫 번째 이미지의 크기에 맞게 자동으로 조정합니다.

## 입력

| 매개변수 | 데이터 유형 | 설명                                                                                                   |
| -------- | ----------- | ------------------------------------------------------------------------------------------------------ |
| `image1` | `IMAGE`     | 배치에 결합될 첫 번째 이미지입니다. 필요할 경우 두 번째 이미지가 조정될 크기의 기준이 됩니다.          |
| `image2` | `IMAGE`     | 배치에 결합될 두 번째 이미지입니다. 크기가 다를 경우 첫 번째 이미지의 크기에 맞게 자동으로 조정됩니다. |

## 출력

| 매개변수 | 데이터 유형 | 설명                                                                            |
| -------- | ----------- | ------------------------------------------------------------------------------- |
| `image`  | `IMAGE`     | 두 번째 이미지가 첫 번째 이미지의 크기에 맞게 조정된, 결합된 이미지 배치입니다. |
