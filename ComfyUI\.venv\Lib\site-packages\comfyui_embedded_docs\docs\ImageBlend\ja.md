
`ImageBlend`ノードは、指定されたブレンドモードとブレンドファクターに基づいて、2つの画像をブレンドするために設計されています。通常、乗算、スクリーン、オーバーレイ、ソフトライト、差分など、さまざまなブレンドモードをサポートしており、多様な画像操作と合成技術を可能にします。このノードは、2つの画像レイヤー間の視覚的な相互作用を調整することで、合成画像を作成するために不可欠です。

## 入力

| フィールド         | Data Type | 説明                                                                       |
|---------------|-------------|-----------------------------------------------------------------------------------|
| `image1`      | `IMAGE`     | ブレンドされる最初の画像。ブレンド操作の基礎レイヤーとして機能します。 |
| `image2`      | `IMAGE`     | ブレンドされる2番目の画像。ブレンドモードに応じて、最初の画像の外観を変更します。 |
| `blend_factor`| `FLOAT`     | ブレンドにおける2番目の画像の重みを決定します。ブレンドファクターが高いほど、結果のブレンドで2番目の画像がより目立ちます。 |
| `blend_mode`  | COMBO[STRING] | 2つの画像をブレンドする方法を指定します。通常、乗算、スクリーン、オーバーレイ、ソフトライト、差分などのモードをサポートし、それぞれが独自の視覚効果を生み出します。 |

## 出力

| フィールド | Data Type | 説明                                                              |
|-------|-------------|--------------------------------------------------------------------------|
| `image`| `IMAGE`     | 指定されたブレンドモードとファクターに従って、2つの入力画像をブレンドした結果の画像です。 |
