
`ImageBlend` 노드는 지정된 블렌딩 모드와 블렌드 팩터를 기반으로 두 이미지를 혼합하도록 설계되었습니다. 일반, 곱하기, 스크린, 오버레이, 소프트 라이트, 차이와 같은 다양한 블렌딩 모드를 지원하여 다재다능한 이미지 조작 및 합성 기술을 제공합니다. 이 노드는 두 이미지 레이어 간의 시각적 상호작용을 조정하여 합성 이미지를 만드는 데 필수적입니다.

## 입력

| 필드           | 데이터 유형   | 설명                                                                                                                                                     |
| -------------- | ------------- | -------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `image1`       | `IMAGE`       | 혼합될 첫 번째 이미지입니다. 블렌딩 작업의 기본 레이어로 사용됩니다.                                                                                     |
| `image2`       | `IMAGE`       | 혼합될 두 번째 이미지입니다. 블렌드 모드에 따라 첫 번째 이미지의 외관을 수정합니다.                                                                      |
| `blend_factor` | `FLOAT`       | 혼합에서 두 번째 이미지의 가중치를 결정합니다. 높은 블렌드 팩터는 결과 혼합에서 두 번째 이미지에 더 많은 중요성을 부여합니다.                            |
| `blend_mode`   | COMBO[STRING] | 두 이미지를 혼합하는 방법을 지정합니다. 일반, 곱하기, 스크린, 오버레이, 소프트 라이트, 차이와 같은 모드를 지원하며 각각 고유한 시각적 효과를 생성합니다. |

## 출력

| 필드    | 데이터 유형 | 설명                                                                        |
| ------- | ----------- | --------------------------------------------------------------------------- |
| `image` | `IMAGE`     | 지정된 블렌드 모드와 팩터에 따라 두 입력 이미지를 혼합한 결과 이미지입니다. |
