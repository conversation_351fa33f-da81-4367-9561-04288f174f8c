
此节点设计用于根据指定的混合模式和混合因子将两张图像混合在一起。它支持各种混合模式，如正常、乘法、屏幕、叠加、柔光和差异，允许进行多功能的图像操作和组合技术。此节点对于通过调整两个图像层之间的视觉交互来创建复合图像至关重要。

## 输入

| 参数名称 | 数据类型 | 作用 |
| --- | --- | --- |
| `image1` | `IMAGE` | 第一张要混合的图像。它作为混合操作的基础层。 |
| `image2` | `IMAGE` | 第二张要混合的图像。根据混合模式，它修改第一张图像的外观。 |
| `blend_factor` | `FLOAT` | 确定第二张图像在混合中的权重。更高的混合因子会使第二张图像在结果混合中更加突出。 |
| `blend_mode` | COMBO[STRING] | 指定两种图像混合的方法。支持正常、乘法、屏幕、叠加、柔光和差异等模式，每种模式都产生独特的视觉效果。 |

## 输出

| 参数名称 | 数据类型 | 作用 |
| --- | --- | --- |
| `image` | `IMAGE` | 根据指定的混合模式和因子混合两张输入图像后得到的图像。 |
