该节点会检测位于 `ComfyUI/models/checkpoints` 文件夹下的模型，同时也会读取你在 extra_model_paths.yaml 文件中配置的额外路径的模型，有时你可能需要 **刷新 ComfyUI 界面** 才能让它读取到对应文件夹下的模型文件

此节点专门用于加载视频生成工作流中基于图像的模型的检查点。它有效地检索和配置来自给定检查点的所需组件，专注于模型的图像相关方面。

## 输入

| 参数名称 | 数据类型 | 作用 |
| --- | --- | --- |
| `ckpt_name` | COMBO[STRING] | 指定要加载的检查点的名称。此参数对于从预定义的可用检查点列表中识别和检索正确的检查点文件至关重要。 |

## 输出

| 参数名称 | 数据类型 | 作用 |
| --- | --- | --- |
| `model` | MODEL | 返回从检查点加载的并配置用于视频生成上下文中的图像处理的主要模型。 |
| `clip_vision` | CLIP_VISION | 提供从检查点提取的CLIP视觉组件，专为图像理解和特征提取而设计。 |
| `vae` | VAE | 提供变分自编码器（VAE）组件，对图像操作和生成任务至关重要。 |
