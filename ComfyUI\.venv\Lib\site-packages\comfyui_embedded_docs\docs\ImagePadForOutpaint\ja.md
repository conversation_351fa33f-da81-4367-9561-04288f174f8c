このノードは、アウトペインティングプロセスのために画像の周囲にパディングを追加して準備するために設計されています。画像の寸法を調整してアウトペインティングアルゴリズムとの互換性を確保し、元の境界を超えた拡張画像領域の生成を促進します。

## 入力

| パラメータ | データ型 | 説明 |
|-----------|-------------|-------------|
| `image`   | `IMAGE`     | `image`入力は、アウトペインティングのために準備される主要な画像であり、パディング操作の基礎として機能します。 |
| `left`    | `INT`       | 画像の左側に追加するパディングの量を指定し、アウトペインティングのための拡張領域に影響を与えます。 |
| `top`     | `INT`       | 画像の上部に追加するパディングの量を決定し、アウトペインティングのための垂直方向の拡張に影響を与えます。 |
| `right`   | `INT`       | 画像の右側に追加するパディングの量を定義し、アウトペインティングのための水平方向の拡張に影響を与えます。 |
| `bottom`  | `INT`       | 画像の下部に追加するパディングの量を示し、アウトペインティングのための垂直方向の拡張に寄与します。 |
| `feathering` | `INT` | 元の画像と追加されたパディングの間の移行の滑らかさを制御し、アウトペインティングの視覚的統合を向上させます。 |

## 出力

| パラメータ | データ型 | 説明 |
|-----------|-------------|-------------|
| `image`   | `IMAGE`     | 出力`image`は、アウトペインティングプロセスの準備が整ったパディングされた画像を表します。 |
| `mask`    | `MASK`      | 出力`mask`は、元の画像と追加されたパディングの領域を示し、アウトペインティングアルゴリズムを導くのに役立ちます。 |
