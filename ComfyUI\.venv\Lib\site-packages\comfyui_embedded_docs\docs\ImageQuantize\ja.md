ImageQuantizeノードは、画像の色数を指定された数に減らすために設計されており、視覚的な品質を維持するためにディザリング技術を適用することもできます。このプロセスは、パレットベースの画像を作成したり、特定のアプリケーションのために色の複雑さを減らすのに役立ちます。

## 入力

| フィールド   | Data Type | 説明                                                                       |
|-------------|-------------|-----------------------------------------------------------------------------------|
| `image`     | `IMAGE`     | 量子化される入力画像テンソルです。これは、色の削減が行われる主要なデータとしてノードの実行に影響を与えます。 |
| `colors`    | `INT`       | 画像を減らす色数を指定します。これは、カラーパレットのサイズを決定することで量子化プロセスに直接影響を与えます。 |
| `dither`    | COMBO[STRING] | 量子化中に適用されるディザリング技術を決定し、出力画像の視覚的な品質と外観に影響を与えます。 |

## 出力

| フィールド | Data Type | 説明                                                                   |
|-----------|-------------|-------------------------------------------------------------------------------|
| `image`   | `IMAGE`     | 入力画像の量子化バージョンで、色の複雑さが減少し、視覚的な品質を維持するためにオプションでディザリングされています。 |
