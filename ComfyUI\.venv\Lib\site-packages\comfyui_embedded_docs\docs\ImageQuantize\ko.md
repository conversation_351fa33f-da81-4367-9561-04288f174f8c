ImageQuantize 노드는 이미지의 색상을 지정된 수로 줄이고, 시각적 품질을 유지하기 위해 디더링 기법을 선택적으로 적용하도록 설계되었습니다. 이 과정은 팔레트 기반 이미지를 생성하거나 특정 응용 프로그램을 위해 색상 복잡성을 줄이는 데 유용합니다.

## 입력

| 필드     | 데이터 유형   | 설명                                                                                                           |
| -------- | ------------- | -------------------------------------------------------------------------------------------------------------- |
| `image`  | `IMAGE`       | 양자화할 입력 이미지 텐서입니다. 이는 색상 감소가 수행되는 주요 데이터로 노드의 실행에 영향을 미칩니다.        |
| `colors` | `INT`         | 이미지의 색상을 줄일 수를 지정합니다. 이는 색상 팔레트 크기를 결정하여 양자화 과정에 직접적인 영향을 미칩니다. |
| `dither` | COMBO[STRING] | 양자화 중 적용할 디더링 기법을 결정하여 출력 이미지의 시각적 품질과 외관에 영향을 미칩니다.                    |

## 출력

| 필드    | 데이터 유형 | 설명                                                                                                         |
| ------- | ----------- | ------------------------------------------------------------------------------------------------------------ |
| `image` | `IMAGE`     | 입력 이미지의 양자화된 버전으로, 색상 복잡성이 줄어들고 시각적 품질을 유지하기 위해 선택적으로 디더링됩니다. |
