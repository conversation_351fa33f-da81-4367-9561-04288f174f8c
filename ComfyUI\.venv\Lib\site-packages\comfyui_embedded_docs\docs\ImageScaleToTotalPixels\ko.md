ImageScaleToTotalPixels 노드는 이미지의 종횡비를 유지하면서 지정된 총 픽셀 수로 이미지를 조정하도록 설계되었습니다. 원하는 픽셀 수를 달성하기 위해 다양한 업스케일링 방법을 제공합니다.

## 입력

| 매개변수         | 데이터 유형   | 설명                                                                                                      |
| ---------------- | ------------- | --------------------------------------------------------------------------------------------------------- |
| `image`          | `IMAGE`       | 지정된 총 픽셀 수로 업스케일링할 입력 이미지입니다.                                                       |
| `upscale_method` | COMBO[STRING] | 이미지를 업스케일링하는 데 사용되는 방법입니다. 이는 업스케일링된 이미지의 품질과 특성에 영향을 미칩니다. |
| `megapixels`     | `FLOAT`       | 이미지의 목표 크기를 메가픽셀 단위로 나타냅니다. 이는 업스케일링된 이미지의 총 픽셀 수를 결정합니다.      |

## 출력

| 매개변수 | 데이터 유형 | 설명                                                                 |
| -------- | ----------- | -------------------------------------------------------------------- |
| `image`  | `IMAGE`     | 지정된 총 픽셀 수로 업스케일링된 이미지로, 원본 종횡비를 유지합니다. |
