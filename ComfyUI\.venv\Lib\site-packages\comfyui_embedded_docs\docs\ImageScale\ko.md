ImageScale 노드는 이미지를 특정 크기로 조정하기 위해 설계되었으며, 다양한 업스케일 방법과 리사이즈된 이미지의 크롭 기능을 제공합니다. 이미지 업스케일링과 크롭의 복잡성을 추상화하여 사용자 정의 매개변수에 따라 이미지 크기를 수정할 수 있는 간단한 인터페이스를 제공합니다.

## 입력

| 매개변수         | 데이터 유형   | 설명                                                                                                                                                                                                     |
| ---------------- | ------------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `image`          | `IMAGE`       | 업스케일링할 입력 이미지입니다. 이 매개변수는 노드의 작동에 중심이 되며, 리사이즈 변환이 적용되는 기본 데이터로 사용됩니다. 출력 이미지의 품질과 크기는 원본 이미지의 속성에 직접적으로 영향을 받습니다. |
| `upscale_method` | COMBO[STRING] | 이미지를 업스케일링하는 데 사용되는 방법을 지정합니다. 방법의 선택은 업스케일된 이미지의 품질과 특성에 영향을 미치며, 리사이즈된 출력에서 시각적 충실도와 잠재적 아티팩트에 영향을 줄 수 있습니다.       |
| `width`          | `INT`         | 업스케일된 이미지의 목표 너비입니다. 이 매개변수는 출력 이미지의 크기에 직접적으로 영향을 미치며, 리사이즈 작업의 수평 스케일을 결정합니다.                                                              |
| `height`         | `INT`         | 업스케일된 이미지의 목표 높이입니다. 이 매개변수는 출력 이미지의 크기에 직접적으로 영향을 미치며, 리사이즈 작업의 수직 스케일을 결정합니다.                                                              |
| `crop`           | COMBO[STRING] | 업스케일된 이미지를 크롭할지 여부와 방법을 결정하며, 크롭 비활성화 또는 중앙 크롭 옵션을 제공합니다. 이는 지정된 크기에 맞추기 위해 가장자리를 제거하여 이미지의 최종 구성을 영향을 미칩니다.            |

## 출력

| 매개변수 | 데이터 유형 | 설명                                                                           |
| -------- | ----------- | ------------------------------------------------------------------------------ |
| `image`  | `IMAGE`     | 업스케일되고 선택적으로 크롭된 이미지로, 추가 처리나 시각화를 위해 준비됩니다. |
