此节点设计用于将图像调整到特定的尺寸，提供了一系列放大方法以及裁剪调整大小后的图像的能力。它抽象了图像放大和裁剪的复杂性，提供了一个简单的接口，用于根据用户定义的参数修改图像尺寸。

## 输入

| 参数名称 | 数据类型 | 作用 |
| --- | --- | --- |
| `图像` | `IMAGE` | 要放大的输入图像。此参数是节点操作的核心，作为应用尺寸转换的主要数据。输出图像的质量和尺寸直接受原始图像属性的影响。 |
| `缩放方法` | COMBO[STRING] | 指定用于放大图像的方法。方法的选择可以影响放大图像的质量和特性，影响调整大小后的输出的视觉保真度和潜在的伪影。 |
| `width` | `INT` | 放大图像的目标宽度。此参数直接影响输出图像的尺寸，决定了调整大小操作的水平缩放。 |
| `height` | `INT` | 放大图像的目标高度。此参数直接影响输出图像的尺寸，决定了调整大小操作的垂直缩放。 |
| `crop` | COMBO[STRING] | 确定是否以及如何裁剪放大后的图像，提供禁用裁剪或中心裁剪的选项。通过潜在地去除边缘以适应指定的尺寸，这影响图像的最终构图。 |

## 输出

| 参数名称 | 数据类型 | 作用 |
| --- | --- | --- |
| `image` | `IMAGE` | 放大（和可选裁剪）的图像，准备好进行进一步处理或可视化。 |

---
