ImageSharpenノードは、画像のエッジとディテールを強調することで、画像の鮮明さを向上させます。画像にシャープ化フィルターを適用し、その強度と半径を調整することで、より明確で鮮明な画像を作成します。

## 入力

| フィールド          | Data Type | 説明                                                                                   |
|----------------|-------------|-----------------------------------------------------------------------------------------------|
| `image`        | `IMAGE`     | シャープ化される入力画像。このパラメータは、シャープ化効果が適用される基礎画像を決定するために重要です。 |
| `sharpen_radius`| `INT`       | シャープ化効果の半径を定義します。半径が大きいほど、エッジ周辺のより多くのピクセルが影響を受け、より顕著なシャープ化効果が得られます。 |
| `sigma`        | `FLOAT`     | シャープ化効果の広がりを制御します。シグマ値が高いほど、エッジでのスムーズな遷移が得られ、シグマが低いとシャープ化がより局所的になります。 |
| `alpha`        | `FLOAT`     | シャープ化効果の強度を調整します。アルファ値が高いほど、より強いシャープ化効果が得られます。 |

## 出力

| フィールド | Data Type | 説明                                                              |
|-------|-------------|--------------------------------------------------------------------------|
| `image`| `IMAGE`     | エッジとディテールが強調されたシャープ化画像で、さらなる処理や表示の準備が整っています。 |
