ImageSharpen 노드는 이미지의 가장자리와 세부 사항을 강조하여 선명도를 향상시킵니다. 이미지에 샤프닝 필터를 적용하며, 강도와 반경을 조절할 수 있어 이미지를 더욱 선명하고 뚜렷하게 만듭니다.

## 입력

| 필드             | 데이터 유형 | 설명                                                                                                                                   |
| ---------------- | ----------- | -------------------------------------------------------------------------------------------------------------------------------------- |
| `image`          | `IMAGE`     | 샤프닝할 입력 이미지입니다. 이 매개변수는 샤프닝 효과가 적용될 기본 이미지를 결정하는 데 중요합니다.                                   |
| `sharpen_radius` | `INT`       | 샤프닝 효과의 반경을 정의합니다. 더 큰 반경은 가장자리 주변의 더 많은 픽셀에 영향을 미쳐 더 뚜렷한 샤프닝 효과를 생성합니다.           |
| `sigma`          | `FLOAT`     | 샤프닝 효과의 확산을 제어합니다. 더 높은 시그마 값은 가장자리에서 더 부드러운 전환을 제공하며, 낮은 시그마는 샤프닝을 더 국소화합니다. |
| `alpha`          | `FLOAT`     | 샤프닝 효과의 강도를 조절합니다. 더 높은 알파 값은 더 강한 샤프닝 효과를 제공합니다.                                                   |

## 출력

| 필드    | 데이터 유형 | 설명                                                                                       |
| ------- | ----------- | ------------------------------------------------------------------------------------------ |
| `image` | `IMAGE`     | 가장자리와 세부 사항이 향상된 샤프닝된 이미지로, 추가 처리나 디스플레이를 위해 준비됩니다. |
