此节点通过强调图像的边缘和细节来增强图像的清晰度。它对图像应用锐化滤镜，可以调整强度和半径，使图像看起来更清晰和鲜明。

## 输入

| 参数名称 | 数据类型 | 作用 |
| --- | --- | --- |
| `image` | `IMAGE` | 要锐化的输入图像。此参数至关重要，因为它决定了将应用锐化效果的基础图像。 |
| `sharpen_radius` | `INT` | 定义锐化效果的半径。较大的半径意味着边缘周围更多的像素将受到影响，导致更明显的锐化效果。 |
| `sigma` | `FLOAT` | 控制锐化效果的扩散。较高的sigma值会在边缘产生更平滑的过渡，而较低的sigma使锐化更局部化。 |
| `alpha` | `FLOAT` | 调整锐化效果的强度。较高的alpha值会导致更强的锐化效果。 |

## 输出

| 参数名称 | 数据类型 | 作用 |
| --- | --- | --- |
| `image` | `IMAGE` | 锐化的图像，边缘和细节得到增强，准备好进行进一步处理或显示。 |
