Этот узел позволяет объединить два изображения в указанном направлении (вверх, вниз, влево, вправо), с поддержкой подгонки размера и настройки интервала между изображениями.

## Входы

| Имя Параметра | Тип Данных | Тип Ввода | Значение по Умолчанию | Диапазон | Описание |
|---------------|------------|------------|----------------------|-----------|-----------|
| `image1` | IMAGE | Обязательный | - | - | Первое изображение для объединения |
| `image2` | IMAGE | Необязательный | None | - | Второе изображение для объединения, если не указано, возвращается только первое изображение |
| `direction` | STRING | Обязательный | right | right/down/left/up | Направление для объединения второго изображения: right (вправо), down (вниз), left (влево), up (вверх) |
| `match_image_size` | BOOLEAN | Обязательный | True | True/False | Нужно ли изменить размер второго изображения, чтобы оно соответствовало размерам первого |
| `spacing_width` | INT | Обязательный | 0 | 0-1024 | Ширина интервала между изображениями, должна быть четным числом |
| `spacing_color` | STRING | Обязательный | white | white/black/red/green/blue | Цвет интервала между объединенными изображениями |

> Для параметра `spacing_color` при использовании цветов, отличных от "white/black", если `match_image_size` установлен как `false`, область заполнения будет черной

## Выходы

| Имя Выхода | Тип Данных | Описание |
|------------|------------|-----------|
| `IMAGE` | IMAGE | Объединенное изображение |

## Пример Рабочего Процесса

В приведенном ниже рабочем процессе мы используем 3 входных изображения разных размеров в качестве примеров:

- image1: 500x300
- image2: 400x250
- image3: 300x300

![workflow](./asset/workflow.webp)

**Первый Узел Image Stitch**

- `match_image_size`: false, изображения будут объединены в их исходных размерах
- `direction`: up, `image2` будет размещено над `image1`
- `spacing_width`: 20
- `spacing_color`: black

Выходное изображение 1:

![output1](./asset/output-1.webp)

**Второй Узел Image Stitch**

- `match_image_size`: true, второе изображение будет масштабировано, чтобы соответствовать высоте или ширине первого изображения
- `direction`: right, `image3` появится справа
- `spacing_width`: 20
- `spacing_color`: white

Выходное изображение 2:

![output2](./asset/output-2.webp)
