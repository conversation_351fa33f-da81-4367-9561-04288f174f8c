ImageToMaskノードは、指定されたカラーチャンネルに基づいて画像をマスクに変換するために設計されています。これにより、画像の赤、緑、青、またはアルファチャンネルに対応するマスクレイヤーを抽出し、チャンネル特定のマスキングや処理を必要とする操作を容易にします。

## 入力

| パラメータ   | Data Type | 説明                                                                                                          |
|-------------|-------------|----------------------------------------------------------------------------------------------------------------------|
| `image`     | `IMAGE`     | 'image'パラメータは、指定されたカラーチャンネルに基づいてマスクが生成される入力画像を表します。これは、結果として得られるマスクの内容と特性を決定する上で重要な役割を果たします。 |
| `channel`   | COMBO[STRING] | 'channel'パラメータは、入力画像のどのカラーチャンネル（赤、緑、青、またはアルファ）がマスク生成に使用されるべきかを指定します。この選択は、マスクの外観や画像のどの部分が強調またはマスクされるかに直接影響を与えます。 |

## 出力

| パラメータ | データ型 | 説明 |
|-----------|-------------|-------------|
| `mask`    | `MASK`      | 出力される'mask'は、入力画像の指定されたカラーチャンネルのバイナリまたはグレースケール表現であり、さらなる画像処理やマスキング操作に役立ちます。 |
