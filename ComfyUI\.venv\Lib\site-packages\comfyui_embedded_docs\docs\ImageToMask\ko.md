
ImageToMask 노드는 지정된 색상 채널을 기반으로 이미지를 마스크로 변환하도록 설계되었습니다. 이 노드는 이미지의 빨강, 초록, 파랑 또는 알파 채널에 해당하는 마스크 레이어를 추출하여 채널별 마스킹이나 처리가 필요한 작업을 용이하게 합니다.

## 입력

| 매개변수  | 데이터 유형   | 설명                                                                                                                                                                                                                        |
| --------- | ------------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `image`   | `IMAGE`       | 'image' 매개변수는 지정된 색상 채널을 기반으로 마스크가 생성될 입력 이미지를 나타냅니다. 이는 결과 마스크의 내용과 특성을 결정하는 데 중요한 역할을 합니다.                                                                 |
| `channel` | COMBO[STRING] | 'channel' 매개변수는 입력 이미지의 어느 색상 채널(빨강, 초록, 파랑 또는 알파)을 사용하여 마스크를 생성할지를 지정합니다. 이 선택은 마스크의 외관과 이미지의 어느 부분이 강조되거나 마스킹될지를 직접적으로 영향을 미칩니다. |

## 출력

| 매개변수 | 데이터 유형 | 설명                                                                                                                         |
| -------- | ----------- | ---------------------------------------------------------------------------------------------------------------------------- |
| `mask`   | `MASK`      | 출력 'mask'는 입력 이미지의 지정된 색상 채널의 이진 또는 그레이스케일 표현으로, 추가 이미지 처리나 마스킹 작업에 유용합니다. |
