ImageToMask 节点旨在根据指定的颜色通道将图像转换为遮罩。它允许提取与图像的红色、绿色、蓝色或 alpha 通道相对应的遮罩层，从而便于进行需要特定通道遮罩或处理的操作。

## 输入

| 参数名称   | 数据类型 | 作用                                                         |
|------------|----------|--------------------------------------------------------------|
| `image`    | `IMAGE`  | 'image' 参数代表将根据指定颜色通道生成遮罩的输入图像。它在确定生成的遮罩的内容和特性方面起着关键作用。 |
| `channel`  | COMBO[STRING] | 'channel' 参数指定应该使用输入图像的哪种颜色通道（红色、绿色、蓝色或 alpha）来生成遮罩。这个选择直接影响遮罩的外观以及图像的哪些部分被突出显示或遮蔽。 |

## 输出

| 参数名称 | 数据类型 | 作用                                                         |
|----------|----------|--------------------------------------------------------------|
| `mask`   | `MASK`   | 输出的 'mask' 是输入图像中指定颜色通道的二进制或灰度表示，适用于进一步的图像处理或遮罩操作。 |
