
このノードは、指定されたアップスケールモデルを使用して画像を拡大するために設計されています。画像を適切なデバイスに調整し、メモリを効率的に管理し、タイル状にアップスケールモデルを適用することで、メモリ不足のエラーを回避します。

## 入力

| パラメータ         | Comfy dtype       | 説明                                                                 |
|-------------------|-------------------|----------------------------------------------------------------------------|
| `upscale_model`   | `UPSCALE_MODEL`   | 画像を拡大するために使用されるアップスケールモデルです。アップスケールアルゴリズムとそのパラメータを定義するために重要です。 |
| `image`           | `IMAGE`           | 拡大される画像。この入力は、アップスケールプロセスを受けるソースコンテンツを決定するために不可欠です。 |

## 出力

| パラメータ | Data Type | 説明                                        |
|-----------|-------------|----------------------------------------------------|
| `image`   | `IMAGE`     | アップスケールモデルによって処理された拡大画像。この出力は、解像度や品質が向上したアップスケール操作の結果を示します。 |
