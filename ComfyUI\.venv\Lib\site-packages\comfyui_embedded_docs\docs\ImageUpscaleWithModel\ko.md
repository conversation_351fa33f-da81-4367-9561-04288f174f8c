
이 노드는 지정된 업스케일 모델을 사용하여 이미지를 업스케일링하도록 설계되었습니다. 적절한 장치로 이미지를 조정하고 메모리를 효율적으로 관리하며, 잠재적인 메모리 부족 오류를 방지하기 위해 타일 방식으로 업스케일 모델을 적용합니다.

## 입력

| 매개변수        | Comfy dtype     | 설명                                                                                                                    |
| --------------- | --------------- | ----------------------------------------------------------------------------------------------------------------------- |
| `upscale_model` | `UPSCALE_MODEL` | 이미지를 업스케일링하는 데 사용될 업스케일 모델입니다. 이는 업스케일링 알고리즘과 그 매개변수를 정의하는 데 중요합니다. |
| `image`         | `IMAGE`         | 업스케일링할 이미지입니다. 이 입력은 업스케일링 과정을 거칠 소스 콘텐츠를 결정하는 데 필수적입니다.                     |

## 출력

| 매개변수 | 데이터 유형 | 설명                                                                                                                        |
| -------- | ----------- | --------------------------------------------------------------------------------------------------------------------------- |
| `image`  | `IMAGE`     | 업스케일 모델에 의해 처리된 업스케일된 이미지입니다. 이 출력은 업스케일링 작업의 결과로, 향상된 해상도나 품질을 보여줍니다. |
