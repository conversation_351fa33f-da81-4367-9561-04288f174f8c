
InpaintModelConditioningノードは、インペイントモデルの条件付けプロセスを容易にするために設計されており、さまざまな条件付け入力を統合および操作して、インペイント出力を調整することができます。特定のモデルチェックポイントのロードやスタイルまたはコントロールネットモデルの適用、条件付け要素のエンコードと結合など、幅広い機能を備えており、インペイントタスクをカスタマイズするための包括的なツールとして機能します。

## 入力

| パラメータ | Comfy dtype        | 説明 |
|-----------|--------------------|-------------|
| `positive`| `CONDITIONING`     | インペイントモデルに適用されるポジティブな条件付け情報またはパラメータを表します。この入力は、インペイント操作が実行されるコンテキストまたは制約を定義するために重要であり、最終出力に大きく影響します。 |
| `negative`| `CONDITIONING`     | インペイントモデルに適用されるネガティブな条件付け情報またはパラメータを表します。この入力は、インペイントプロセス中に避けるべき条件またはコンテキストを指定するために重要であり、最終出力に影響を与えます。 |
| `vae`     | `VAE`              | 条件付けプロセスで使用されるVAEモデルを指定します。この入力は、使用されるVAEモデルの特定のアーキテクチャとパラメータを決定するために重要です。 |
| `pixels`  | `IMAGE`            | インペイントされる画像のピクセルデータを表します。この入力は、インペイントタスクに必要な視覚的コンテキストを提供するために重要です。 |
| `mask`    | `MASK`             | 画像に適用されるマスクを指定し、インペイントされる領域を示します。この入力は、画像内でインペイントが必要な特定の領域を定義するために重要です。 |

## 出力

| パラメータ | データ型 | 説明 |
|-----------|--------------|-------------|
| `positive`| `CONDITIONING` | 処理後の修正されたポジティブな条件付け情報で、インペイントモデルに適用する準備ができています。この出力は、指定されたポジティブな条件に従ってインペイントプロセスを導くために重要です。 |
| `negative`| `CONDITIONING` | 処理後の修正されたネガティブな条件付け情報で、インペイントモデルに適用する準備ができています。この出力は、指定されたネガティブな条件に従ってインペイントプロセスを導くために重要です。 |
| `latent`  | `LATENT`     | 条件付けプロセスから得られる潜在表現です。この出力は、インペイントされる画像の基礎となる特徴や特性を理解するために重要です。 |
