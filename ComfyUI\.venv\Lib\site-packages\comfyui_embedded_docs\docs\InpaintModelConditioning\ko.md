
InpaintModelConditioning 노드는 인페인팅 모델을 위한 컨디셔닝 과정을 용이하게 하도록 설계되었습니다. 다양한 컨디셔닝 입력을 통합하고 조작하여 인페인팅 출력을 맞춤화할 수 있게 합니다. 특정 모델 체크포인트를 로드하고 스타일 또는 컨트롤 넷 모델을 적용하며, 컨디셔닝 요소를 인코딩하고 결합하는 등 광범위한 기능을 포함하여 인페인팅 작업을 맞춤화하는 포괄적인 도구로 작용합니다.

## 입력

| 매개변수   | Comfy dtype    | 설명                                                                                                                                                                            |
| ---------- | -------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `positive` | `CONDITIONING` | 인페인팅 모델에 적용될 긍정적 컨디셔닝 정보나 매개변수를 나타냅니다. 이 입력은 인페인팅 작업이 수행될 맥락이나 제약을 정의하는 데 중요하며, 최종 출력에 큰 영향을 미칩니다.     |
| `negative` | `CONDITIONING` | 인페인팅 모델에 적용될 부정적 컨디셔닝 정보나 매개변수를 나타냅니다. 이 입력은 인페인팅 과정에서 피해야 할 조건이나 맥락을 지정하는 데 필수적이며, 최종 출력에 영향을 미칩니다. |
| `vae`      | `VAE`          | 컨디셔닝 과정에서 사용할 VAE 모델을 지정합니다. 이 입력은 사용될 VAE 모델의 특정 아키텍처와 매개변수를 결정하는 데 중요합니다.                                                  |
| `pixels`   | `IMAGE`        | 인페인팅할 이미지의 픽셀 데이터를 나타냅니다. 이 입력은 인페인팅 작업에 필요한 시각적 맥락을 제공하는 데 필수적입니다.                                                          |
| `mask`     | `MASK`         | 이미지에 적용될 마스크를 지정하여 인페인팅할 영역을 나타냅니다. 이 입력은 이미지 내에서 인페인팅이 필요한 특정 영역을 정의하는 데 중요합니다.                                   |

## 출력

| 매개변수   | 데이터 유형    | 설명                                                                                                                                                      |
| ---------- | -------------- | --------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `positive` | `CONDITIONING` | 처리 후 인페인팅 모델에 적용할 준비가 된 수정된 긍정적 컨디셔닝 정보입니다. 이 출력은 지정된 긍정적 조건에 따라 인페인팅 과정을 안내하는 데 필수적입니다. |
| `negative` | `CONDITIONING` | 처리 후 인페인팅 모델에 적용할 준비가 된 수정된 부정적 컨디셔닝 정보입니다. 이 출력은 지정된 부정적 조건에 따라 인페인팅 과정을 안내하는 데 필수적입니다. |
| `latent`   | `LATENT`       | 컨디셔닝 과정에서 파생된 잠재 표현입니다. 이 출력은 인페인팅되는 이미지의 기본 특징과 특성을 이해하는 데 중요합니다.                                      |
