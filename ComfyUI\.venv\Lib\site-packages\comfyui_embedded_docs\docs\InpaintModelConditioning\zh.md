
修复模型条件节点旨在简化修复模型的条件处理过程，允许集成和操作各种条件输入以定制修复输出。它包含了一系列功能，从加载特定的模型检查点和应用风格或控制网络模型，到对条件元素进行编码和组合，因此作为定制修复任务的全面工具。

## 输入

| 参数名称  | 数据类型 | 作用 |
|-----------|----------|------|
| `positive`| `CONDITIONING` | 表示应用于修复模型的正面条件信息或参数。此输入对于定义修复操作应执行的上下文或约束至关重要，对最终输出有显著影响。 |
| `negative`| `CONDITIONING` | 表示应用于修复模型的负面条件信息或参数。此输入对于指定修复过程中要避免的条件或上下文至关重要，因此影响最终输出。 |
| `vae`     | VAE       | 指定在条件处理过程中使用的VAE模型。此输入对于确定将使用的VAE模型的具体架构和参数至关重要。 |
| `pixels`  | `IMAGE`    | 表示要进行修复的图像的像素数据。此输入对于提供修复任务所需的视觉上下文至关重要。 |
| `mask`    | `MASK`     | 指定要应用于图像的遮罩，指示需要进行修复的区域。此输入对于定义图像中需要修复的特定区域至关重要。 |

## 输出

| 参数名称 | 数据类型 | 作用 |
|-----------|----------|------|
| `positive`| `CONDITIONING` | 处理后的修改正面条件信息，准备应用于修复模型。此输出对于根据指定的正面条件指导修复过程至关重要。 |
| `negative`| `CONDITIONING` | 处理后的修改负面条件信息，准备应用于修复模型。此输出对于根据指定的负面条件指导修复过程至关重要。 |
| `latent`  | `LATENT`   | 从条件处理过程派生的潜在表示。此输出对于理解正在修复的图像的底层特征和特性至关重要。 |
