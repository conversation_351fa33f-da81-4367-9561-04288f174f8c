
InvertMaskノードは、指定されたマスクの値を反転させ、マスクされた領域とマスクされていない領域を効果的に入れ替えます。この操作は、前景と背景の関心の焦点を切り替える必要がある画像処理タスクにおいて基本的なものです。

## 入力

| パラメータ | データ型 | 説明 |
|-----------|--------------|-------------|
| `mask`    | MASK         | `mask`パラメータは、反転される入力マスクを表します。これは、反転プロセスで入れ替えられる領域を決定するために重要です。 |

## 出力

| パラメータ | データ型 | 説明 |
|-----------|--------------|-------------|
| `mask`    | MASK         | 出力は入力マスクの反転バージョンであり、以前にマスクされていた領域がマスクされていない領域になり、その逆も同様です。 |
