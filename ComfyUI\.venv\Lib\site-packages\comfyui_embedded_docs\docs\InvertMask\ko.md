
InvertMask 노드는 주어진 마스크의 값을 반전시켜, 마스크된 영역과 비마스크된 영역을 효과적으로 뒤집도록 설계되었습니다. 이 작업은 관심 초점이 전경과 배경 사이에서 전환되어야 하는 이미지 처리 작업에서 기본적입니다.

## 입력

| 매개변수 | 데이터 유형 | 설명                                                                                                        |
| -------- | ----------- | ----------------------------------------------------------------------------------------------------------- |
| `mask`   | MASK        | 'mask' 매개변수는 반전될 입력 마스크를 나타냅니다. 이는 반전 과정에서 뒤집힐 영역을 결정하는 데 중요합니다. |

## 출력

| 매개변수 | 데이터 유형 | 설명                                                                                        |
| -------- | ----------- | ------------------------------------------------------------------------------------------- |
| `mask`   | MASK        | 출력은 입력 마스크의 반전된 버전으로, 이전에 마스크된 영역이 비마스크되고 그 반대가 됩니다. |
