
このノードは合成操作用に設計されており、特に画像とその対応するアルファマスクを結合して単一の出力画像を生成します。視覚コンテンツと透明情報を効果的に組み合わせ、特定の領域が透明または半透明である画像を作成することができます。

## 入力

| パラメータ | データ型 | 説明 |
|-----------|-------------|-------------|
| `image`   | `IMAGE`     | アルファマスクと組み合わせる主要な視覚コンテンツです。透明情報を含まない画像を表します。 |
| `alpha`   | `MASK`      | 対応する画像の透明度を定義するアルファマスクです。画像のどの部分が透明または半透明であるべきかを決定するために使用されます。 |

## 出力

| パラメータ | データ型 | 説明 |
|-----------|-------------|-------------|
| `image`   | `IMAGE`     | 入力画像とアルファマスクを組み合わせ、視覚コンテンツに透明情報を組み込んだ単一の画像が出力されます。 |
