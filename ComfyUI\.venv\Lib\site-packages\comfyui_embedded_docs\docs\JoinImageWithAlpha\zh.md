此节点专为合成操作设计，特别是用于将图像与其对应的 Alpha 遮罩结合，产生单个输出图像。它有效地将视觉内容与透明度信息结合起来，使得可以创建某些区域是透明或半透明的图像。

## 输入

| 参数名称 | 数据类型 | 作用                                                         |
|----------|----------|--------------------------------------------------------------|
| `image`  | `IMAGE`  | 要与 Alpha 遮罩结合的主要视觉内容。它代表没有透明度信息的图像。 |
| `alpha`  | `MASK`   | 定义相应图像透明度的 Alpha 遮罩。它用于确定图像的哪些部分应该是透明的或半透明的。 |

## 输出

| 参数名称 | 数据类型 | 作用                                                         |
|----------|----------|--------------------------------------------------------------|
| `image`  | `IMAGE`  | 输出是将输入图像与 Alpha 遮罩结合的单个图像，将透明度信息整合到视觉内容中。 |
