The KSamplerAdvanced node is designed to enhance the sampling process by providing advanced configurations and techniques. It aims to offer more sophisticated options for generating samples from a model, improving upon the basic KSampler functionalities.

## Inputs

| Parameter             | Data Type | Description                                                                                                                                                                                                                                                                                                                                                     |
|----------------------|-------------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| `model`              | MODEL       | Specifies the model from which samples are to be generated, playing a crucial role in the sampling process.                                                                                                                                                                                                                      |
| `add_noise`          | COMBO[STRING] | Determines whether noise should be added to the sampling process, affecting the diversity and quality of the generated samples.                                                                                                                                                                                                             |
| `noise_seed`         | INT         | Sets the seed for noise generation, ensuring reproducibility in the sampling process.                                                                                                                                                                                                                                     |
| `steps`              | INT         | Defines the number of steps to be taken in the sampling process, impacting the detail and quality of the output.                                                                                                                                                                                                                   |
| `cfg`                | FLOAT       | Controls the conditioning factor, influencing the direction and space of the sampling process.                                                                                                                                                                                                                                  |
| `sampler_name`       | COMBO[STRING] | Selects the specific sampler to be used, allowing for customization of the sampling technique.                                                                                                                                                                                                                                  |
| `scheduler`          | COMBO[STRING] | Chooses the scheduler for controlling the sampling process, affecting the progression and quality of samples.                                                                                                                                                                                                                   |
| `positive`           | CONDITIONING | Specifies the positive conditioning to guide the sampling towards desired attributes.                                                                                                                                                                                                                                     |
| `negative`           | CONDITIONING | Specifies the negative conditioning to steer the sampling away from certain attributes.                                                                                                                                                                                                                                     |
| `latent_image`       | LATENT      | Provides the initial latent image to be used in the sampling process, serving as a starting point.                                                                                                                                                                                                                               |
| `start_at_step`      | INT         | Determines the starting step of the sampling process, allowing for control over the sampling progression.                                                                                                                                                                                                                               |
| `end_at_step`        | INT         | Sets the ending step of the sampling process, defining the scope of the sampling.                                                                                                                                                                                                                                         |
| `return_with_leftover_noise` | COMBO[STRING] | Indicates whether to return the sample with leftover noise, affecting the final output's appearance.                                                                                                                                                                                                                               |

## Outputs

| Parameter   | Data Type | Description                                                                                                               |
|-------------|-------------|------------------------------------------------------------------------------------------------------------------------------|
| `latent`    | LATENT      | The output represents the latent image generated from the model, reflecting the applied configurations and techniques. |
