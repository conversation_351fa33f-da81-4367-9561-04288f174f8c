El nodo KSamplerAdvanced está diseñado para mejorar el proceso de muestreo proporcionando configuraciones y técnicas avanzadas. Su objetivo es ofrecer opciones más sofisticadas para generar muestras a partir de un modelo, mejorando las funcionalidades básicas de KSampler.

## Entradas

| Parámetro             | Data Type | Descripción                                                                                                                                                                                                                                                                                                                                                     |
|----------------------|-------------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| `model`              | MODEL       | Especifica el modelo del cual se generarán las muestras, desempeñando un papel crucial en el proceso de muestreo.                                                                                                                                                                                                                      |
| `add_noise`          | COMBO[STRING] | Determina si se debe añadir ruido al proceso de muestreo, afectando la diversidad y calidad de las muestras generadas.                                                                                                                                                                                                             |
| `noise_seed`         | INT         | Establece la semilla para la generación de ruido, asegurando la reproducibilidad en el proceso de muestreo.                                                                                                                                                                                                                                     |
| `steps`              | INT         | Define el número de pasos a realizar en el proceso de muestreo, impactando el detalle y calidad del resultado.                                                                                                                                                                                                                   |
| `cfg`                | FLOAT       | Controla el factor de condicionamiento, influyendo en la dirección y espacio del proceso de muestreo.                                                                                                                                                                                                                                  |
| `sampler_name`       | COMBO[STRING] | Selecciona el muestreador específico a utilizar, permitiendo la personalización de la técnica de muestreo.                                                                                                                                                                                                                                  |
| `scheduler`          | COMBO[STRING] | Elige el programador para controlar el proceso de muestreo, afectando la progresión y calidad de las muestras.                                                                                                                                                                                                                   |
| `positive`           | CONDITIONING | Especifica el condicionamiento positivo para guiar el muestreo hacia los atributos deseados.                                                                                                                                                                                                                                     |
| `negative`           | CONDITIONING | Especifica el condicionamiento negativo para desviar el muestreo de ciertos atributos.                                                                                                                                                                                                                                     |
| `latent_image`       | LATENT      | Proporciona la imagen latente inicial a utilizar en el proceso de muestreo, sirviendo como punto de partida.                                                                                                                                                                                                                               |
| `start_at_step`      | INT         | Determina el paso inicial del proceso de muestreo, permitiendo el control sobre la progresión del muestreo.                                                                                                                                                                                                                               |
| `end_at_step`        | INT         | Establece el paso final del proceso de muestreo, definiendo el alcance del muestreo.                                                                                                                                                                                                                                         |
| `return_with_leftover_noise` | COMBO[STRING] | Indica si se debe devolver la muestra con el ruido sobrante, afectando la apariencia final del resultado.                                                                                                                                                                                                                               |

## Salidas

| Parámetro   | Data Type | Descripción                                                                                                               |
|-------------|-------------|------------------------------------------------------------------------------------------------------------------------------|
| `latent`    | LATENT      | La salida representa la imagen latente generada a partir del modelo, reflejando las configuraciones y técnicas aplicadas. |
