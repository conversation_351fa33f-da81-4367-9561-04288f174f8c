
Le nœud KSamplerAdvanced est conçu pour améliorer le processus d'échantillonnage en offrant des configurations et techniques avancées. Il vise à proposer des options plus sophistiquées pour générer des échantillons à partir d'un modèle, améliorant ainsi les fonctionnalités de base du KSampler.

## Entrées

| Paramètre             | Data Type | Description                                                                                                                                                                                                                                                                                                                                                     |
|----------------------|-------------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| `model`              | MODEL       | Spécifie le modèle à partir duquel les échantillons doivent être générés, jouant un rôle crucial dans le processus d'échantillonnage.                                                                                                                                                                                                                      |
| `add_noise`          | COMBO[STRING] | Détermine si du bruit doit être ajouté au processus d'échantillonnage, affectant la diversité et la qualité des échantillons générés.                                                                                                                                                                                                             |
| `noise_seed`         | INT         | Définit la graine pour la génération de bruit, assurant la reproductibilité du processus d'échantillonnage.                                                                                                                                                                                                                                     |
| `steps`              | INT         | Définit le nombre d'étapes à suivre dans le processus d'échantillonnage, impactant le détail et la qualité de la sortie.                                                                                                                                                                                                                   |
| `cfg`                | FLOAT       | Contrôle le facteur de conditionnement, influençant la direction et l'espace du processus d'échantillonnage.                                                                                                                                                                                                                                  |
| `sampler_name`       | COMBO[STRING] | Sélectionne l'échantillonneur spécifique à utiliser, permettant la personnalisation de la technique d'échantillonnage.                                                                                                                                                                                                                                  |
| `scheduler`          | COMBO[STRING] | Choisit le planificateur pour contrôler le processus d'échantillonnage, affectant la progression et la qualité des échantillons.                                                                                                                                                                                                                   |
| `positive`           | CONDITIONING | Spécifie le conditionnement positif pour guider l'échantillonnage vers les attributs souhaités.                                                                                                                                                                                                                                     |
| `negative`           | CONDITIONING | Spécifie le conditionnement négatif pour orienter l'échantillonnage loin de certains attributs.                                                                                                                                                                                                                                     |
| `latent_image`       | LATENT      | Fournit l'image latente initiale à utiliser dans le processus d'échantillonnage, servant de point de départ.                                                                                                                                                                                                                               |
| `start_at_step`      | INT         | Détermine l'étape de départ du processus d'échantillonnage, permettant de contrôler la progression de l'échantillonnage.                                                                                                                                                                                                                               |
| `end_at_step`        | INT         | Définit l'étape de fin du processus d'échantillonnage, définissant la portée de l'échantillonnage.                                                                                                                                                                                                                                         |
| `return_with_leftover_noise` | COMBO[STRING] | Indique si l'échantillon doit être retourné avec le bruit restant, affectant l'apparence finale de la sortie.                                                                                                                                                                                                                               |

## Sorties

| Paramètre   | Data Type | Description                                                                                                               |
|-------------|-------------|------------------------------------------------------------------------------------------------------------------------------|
| `latent`    | LATENT      | La sortie représente l'image latente générée à partir du modèle, reflétant les configurations et techniques appliquées. |
