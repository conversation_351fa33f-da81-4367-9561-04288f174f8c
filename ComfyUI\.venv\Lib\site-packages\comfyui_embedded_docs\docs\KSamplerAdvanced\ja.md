
KSamplerAdvancedノードは、サンプリングプロセスを強化するために高度な設定と技術を提供するように設計されています。これは、モデルからサンプルを生成するためのより洗練されたオプションを提供し、基本的なKSampler機能を向上させることを目的としています。

## 入力

| パラメータ             | Data Type | 説明                                                                                                                                                                                                                                                                                                                                                     |
|----------------------|-------------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| `model`              | MODEL       | サンプルを生成するモデルを指定し、サンプリングプロセスにおいて重要な役割を果たします。                                                                                                                                                                                                                      |
| `add_noise`          | COMBO[STRING] | サンプリングプロセスにノイズを追加するかどうかを決定し、生成されるサンプルの多様性と品質に影響を与えます。                                                                                                                                                                                                             |
| `noise_seed`         | INT         | ノイズ生成のためのシードを設定し、サンプリングプロセスの再現性を確保します。                                                                                                                                                                                                                                     |
| `steps`              | INT         | サンプリングプロセスで行うステップ数を定義し、出力の詳細と品質に影響を与えます。                                                                                                                                                                                                                   |
| `cfg`                | FLOAT       | コンディショニングファクターを制御し、サンプリングプロセスの方向と空間に影響を与えます。                                                                                                                                                                                                                                  |
| `sampler_name`       | COMBO[STRING] | 使用する特定のサンプラーを選択し、サンプリング技術のカスタマイズを可能にします。                                                                                                                                                                                                                                  |
| `scheduler`          | COMBO[STRING] | サンプリングプロセスを制御するスケジューラーを選択し、サンプルの進行と品質に影響を与えます。                                                                                                                                                                                                                   |
| `positive`           | CONDITIONING | 望ましい属性に向けてサンプリングを導くためのポジティブなコンディショニングを指定します。                                                                                                                                                                                                                                     |
| `negative`           | CONDITIONING | 特定の属性からサンプリングを遠ざけるためのネガティブなコンディショニングを指定します。                                                                                                                                                                                                                                     |
| `latent_image`       | LATENT      | サンプリングプロセスで使用する初期の潜在画像を提供し、出発点として機能します。                                                                                                                                                                                                                               |
| `start_at_step`      | INT         | サンプリングプロセスの開始ステップを決定し、サンプリングの進行を制御します。                                                                                                                                                                                                                               |
| `end_at_step`        | INT         | サンプリングプロセスの終了ステップを設定し、サンプリングの範囲を定義します。                                                                                                                                                                                                                                         |
| `return_with_leftover_noise` | COMBO[STRING] | 残りのノイズを伴ってサンプルを返すかどうかを示し、最終出力の外観に影響を与えます。                                                                                                                                                                                                                               |

## 出力

| パラメータ   | Data Type | 説明                                                                                                               |
|-------------|-------------|------------------------------------------------------------------------------------------------------------------------------|
| `latent`    | LATENT      | 出力は、モデルから生成された潜在画像を表し、適用された設定と技術を反映しています。 |
