
KSamplerAdvanced 노드는 샘플링 프로세스를 향상시키기 위해 고급 설정과 기술을 제공합니다. 이는 모델로부터 샘플을 생성하는 데 있어 더 정교한 옵션을 제공하여 기본 KSampler 기능을 개선하는 것을 목표로 합니다.

## 입력

| 매개변수                     | 데이터 유형   | 설명                                                                                            |
| ---------------------------- | ------------- | ----------------------------------------------------------------------------------------------- |
| `model`                      | MODEL         | 샘플을 생성할 모델을 지정하며, 샘플링 과정에서 중요한 역할을 합니다.                            |
| `add_noise`                  | COMBO[STRING] | 샘플링 과정에 노이즈를 추가할지 여부를 결정하며, 생성된 샘플의 다양성과 품질에 영향을 미칩니다. |
| `noise_seed`                 | INT           | 노이즈 생성에 대한 시드를 설정하여 샘플링 과정의 재현성을 보장합니다.                           |
| `steps`                      | INT           | 샘플링 과정에서 수행할 단계 수를 정의하며, 출력의 세부 사항과 품질에 영향을 미칩니다.           |
| `cfg`                        | FLOAT         | 조건 인자를 제어하여 샘플링 과정의 방향과 공간에 영향을 미칩니다.                               |
| `sampler_name`               | COMBO[STRING] | 사용할 특정 샘플러를 선택하여 샘플링 기술을 맞춤화할 수 있습니다.                               |
| `scheduler`                  | COMBO[STRING] | 샘플링 과정을 제어할 스케줄러를 선택하여 샘플의 진행과 품질에 영향을 미칩니다.                  |
| `positive`                   | CONDITIONING  | 샘플링을 원하는 속성으로 안내하기 위한 긍정적 조건을 지정합니다.                                |
| `negative`                   | CONDITIONING  | 특정 속성에서 벗어나도록 샘플링을 유도하기 위한 부정적 조건을 지정합니다.                       |
| `latent_image`               | LATENT        | 샘플링 과정에서 사용할 초기 잠재 이미지를 제공하여 시작점으로 작용합니다.                       |
| `start_at_step`              | INT           | 샘플링 과정의 시작 단계를 결정하여 샘플링 진행을 제어할 수 있습니다.                            |
| `end_at_step`                | INT           | 샘플링 과정의 종료 단계를 설정하여 샘플링의 범위를 정의합니다.                                  |
| `return_with_leftover_noise` | COMBO[STRING] | 남은 노이즈와 함께 샘플을 반환할지 여부를 나타내며, 최종 출력의 외관에 영향을 미칩니다.         |

## 출력

| 매개변수 | 데이터 유형 | 설명                                                                            |
| -------- | ----------- | ------------------------------------------------------------------------------- |
| `latent` | LATENT      | 출력은 모델에서 생성된 잠재 이미지를 나타내며, 적용된 설정과 기술을 반영합니다. |
