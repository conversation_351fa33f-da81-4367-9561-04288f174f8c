
KSamplerSelect 노드는 제공된 샘플러 이름에 따라 특정 샘플러를 선택하도록 설계되었습니다. 이는 샘플러 선택의 복잡성을 추상화하여 사용자가 다양한 샘플링 전략을 쉽게 전환할 수 있도록 합니다.

## 입력

| 매개변수       | 데이터 유형   | 설명                                                                                                                       |
| -------------- | ------------- | -------------------------------------------------------------------------------------------------------------------------- |
| `sampler_name` | COMBO[STRING] | 선택할 샘플러의 이름을 지정합니다. 이 매개변수는 사용할 샘플링 전략을 결정하며, 전체 샘플링 동작과 결과에 영향을 미칩니다. |

## 출력

| 매개변수  | 데이터 유형 | 설명                                                                      |
| --------- | ----------- | ------------------------------------------------------------------------- |
| `sampler` | `SAMPLER`   | 선택된 샘플러 객체를 반환하며, 샘플링 작업에 사용할 준비가 되어 있습니다. |
