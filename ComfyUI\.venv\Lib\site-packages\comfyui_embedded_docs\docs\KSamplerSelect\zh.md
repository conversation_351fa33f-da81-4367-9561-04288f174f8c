
`KSamplerSelect` 类用于根据提供的采样器名称选择特定的采样器。它简化了采样器选择的复杂性，允许用户轻松地在不同采样策略之间切换，以适应他们的任务需求。

## 输入

| 参数名称       | 数据类型 | 作用描述                                     |
| -------------- | -------- | -------------------------------------------- |
| `sampler_name` | COMBO[STRING] | 指定要被选择的采样器名称。此参数决定了将使用哪种采样策略，影响整体采样行为和结果。 |

## 输出

| 参数名称 | 数据类型 | 作用描述                                     |
| -------- | -------- | -------------------------------------------- |
| `sampler` | `SAMPLER` | 返回被选择的采样器对象，准备用于采样任务。 |
