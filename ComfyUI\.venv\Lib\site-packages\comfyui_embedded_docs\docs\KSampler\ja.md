
KSamplerノードは、生成モデル内での高度なサンプリング操作を目的として設計されており、さまざまなパラメータを通じてサンプリングプロセスをカスタマイズすることができます。潜在空間の表現を操作し、コンディショニングを活用し、ノイズレベルを調整することで、新しいデータサンプルの生成を促進します。

## 入力

| パラメータ       | Data Type | 説明                                                                                                               |
|-----------------|-------------|---------------------------------------------------------------------------------------------------------------------------|
| `model`         | `MODEL`     | サンプリングに使用される生成モデルを指定し、生成されるサンプルの特性を決定する上で重要な役割を果たします。 |
| `seed`          | `INT`       | サンプリングプロセスのランダム性を制御し、特定の値に設定することで結果の再現性を確保します。                         |
| `steps`         | `INT`       | サンプリングプロセスで行うステップ数を決定し、生成されるサンプルの詳細と品質に影響を与えます。           |
| `cfg`           | `FLOAT`     | コンディショニングファクターを調整し、サンプリング中に適用されるコンディショニングの方向と強度に影響を与えます。                     |
| `sampler_name`  | COMBO[STRING] | 使用する特定のサンプリングアルゴリズムを選択し、サンプリングプロセスの動作と結果に影響を与えます。                     |
| `scheduler`     | COMBO[STRING] | サンプリングプロセスを制御するスケジューリングアルゴリズムを選択し、サンプリングの進行と動態に影響を与えます。           |
| `positive`      | `CONDITIONING` | 望ましい属性や特徴に向けてサンプリングを導くためのポジティブなコンディショニングを定義します。                                         |
| `negative`      | `CONDITIONING` | 特定の属性や特徴からサンプリングを遠ざけるためのネガティブなコンディショニングを指定します。                                     |
| `latent_image`  | `LATENT`    | サンプリングプロセスの開始点または参照として使用される潜在空間の表現を提供します。                            |
| `denoise`       | `FLOAT`     | サンプルに適用されるデノイズのレベルを制御し、生成される画像の明瞭さと鮮明さに影響を与えます。                   |

## 出力

| パラメータ   | Data Type | 説明 |
|-------------|-------------|-------------|
| `latent`    | `LATENT`    | サンプリングプロセスの潜在空間出力を表し、生成されたサンプルをカプセル化しています。 |
