
KSampler 노드는 생성 모델 내에서 고급 샘플링 작업을 위해 설계되었으며, 다양한 매개변수를 통해 샘플링 프로세스를 맞춤화할 수 있습니다. 이는 잠재 공간 표현을 조작하고, 조건을 활용하며, 노이즈 수준을 조정하여 새로운 데이터 샘플을 생성하는 데 도움을 줍니다.

## 입력

| 매개변수       | 데이터 유형    | 설명                                                                                         |
| -------------- | -------------- | -------------------------------------------------------------------------------------------- |
| `model`        | `MODEL`        | 샘플링에 사용할 생성 모델을 지정하며, 생성된 샘플의 특성을 결정하는 데 중요한 역할을 합니다. |
| `seed`         | `INT`          | 샘플링 과정의 무작위성을 제어하여 특정 값으로 설정할 때 결과의 재현성을 보장합니다.          |
| `steps`        | `INT`          | 샘플링 과정에서 수행할 단계 수를 결정하여 생성된 샘플의 세부 사항과 품질에 영향을 미칩니다.  |
| `cfg`          | `FLOAT`        | 조건 인자를 조정하여 샘플링 중 적용되는 조건의 방향과 강도를 조절합니다.                     |
| `sampler_name` | COMBO[STRING]  | 사용할 특정 샘플링 알고리즘을 선택하여 샘플링 과정의 동작과 결과에 영향을 미칩니다.          |
| `scheduler`    | COMBO[STRING]  | 샘플링 과정을 제어할 스케줄링 알고리즘을 선택하여 샘플링의 진행과 동적에 영향을 미칩니다.    |
| `positive`     | `CONDITIONING` | 샘플링을 원하는 속성이나 특징으로 안내하기 위한 긍정적 조건을 정의합니다.                    |
| `negative`     | `CONDITIONING` | 특정 속성이나 특징에서 벗어나도록 샘플링을 유도하기 위한 부정적 조건을 지정합니다.           |
| `latent_image` | `LATENT`       | 샘플링 과정의 시작점이나 참조로 사용할 잠재 공간 표현을 제공합니다.                          |
| `denoise`      | `FLOAT`        | 샘플에 적용되는 디노이즈 수준을 제어하여 생성된 이미지의 선명도와 명확성에 영향을 미칩니다.  |

## 출력

| 매개변수 | 데이터 유형 | 설명                                                               |
| -------- | ----------- | ------------------------------------------------------------------ |
| `latent` | `LATENT`    | 샘플링 과정의 잠재 공간 출력을 나타내며, 생성된 샘플을 포함합니다. |
