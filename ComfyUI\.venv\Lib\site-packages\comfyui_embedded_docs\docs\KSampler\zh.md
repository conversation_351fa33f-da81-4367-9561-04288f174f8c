KSampler这个采样器，是这样工作的：它会根据提供的特定的模型和正、负两种条件，来改造提供的原始潜在图像信息。
首先，它会根据设定好的**seed随机种子**和**denoise降噪强度**，给原始图像数据加入一些噪声，然后输入预设的**Model模型**结合**positive正向**和**negative负向**的引导条件，去生成图像

## Input 输入

| 参数名称               | 数据类型         | 必填 | 默认值 | 取值范围/选项            | 说明                                                                         |
| ---------------------- | ---------------- | ---- | ------ | ------------------------ | ---------------------------------------------------------------------------- |
| Model模型              | checkpoint模型   | 是   | 无     | -                        | 输入用于降噪过程的模型                                                       |
| seed随机种子           | Int整数          | 是   | 0      | 0 ~ 18446744073709551615 | 用于生成随机噪声，使用同样的“种子”可以生成相同的画面                         |
| steps步数              | Int整数          | 是   | 20     | 1 ~ 10000                | 去噪过程中要使用的步骤数，步数越多，结果越准确                               |
| cfg                    | float浮点数      | 是   | 8.0    | 0.0 ~ 100.0              | 控制生成的图像与输入条件的贴合程度，通常建议6-8                              |
| sampler_name采样器     | 界面选项         | 是   | 无     | 多种采样算法             | 选择用来降噪的采样器，不同采样器影响生成速度和风格                           |
| scheduler调度器        | 界面选项         | 是   | 无     | 多种调度器               | 控制噪声去除的方式，不同调度器会影响生成过程                                 |
| Positive正向条件       | conditioning条件 | 是   | 无     | -                        | 用于引导降噪的正向条件，可理解为想要在画面中出现的内容                       |
| Negative负向条件       | conditioning条件 | 是   | 无     | -                        | 用于引导降噪的负向条件，可理解为不想要在画面中出现的内容                     |
| Latent_Image           | Latent           | 是   | 无     | -                        | 用于降噪的潜像                                                               |
| denoise降噪            | float浮点数      | 否   | 1.0    | 0.0 ~ 1.0                | 决定去除多少比例的噪声，值越小生成图像与输入图像关联越小，值越大越像输入图像 |
| control_after_generate | 界面选项         | 否   | 无     | 随机/增量/减量/保持      | 提供在每次提示后更改种子数的能力，节点可以随机、增量、减量或保持种子数不变   |

## Output 输出

| 参数名称 | 作用                       |
| -------- | -------------------------- |
| Latent   | 输出经过采样器降噪后的潜像 |
