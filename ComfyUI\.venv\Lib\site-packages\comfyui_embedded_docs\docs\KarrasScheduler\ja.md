
KarrasSchedulerノードは、<PERSON><PERSON><PERSON> et al. (2022)のノイズスケジュールに基づいてノイズレベル（シグマ）のシーケンスを生成するように設計されています。このスケジューラーは、生成モデルにおける拡散プロセスを制御するのに役立ち、生成プロセスの各ステップで適用されるノイズレベルを微調整することができます。

## 入力

| パラメータ   | Data Type | 説明                                                                                      |
|-------------|-------------|------------------------------------------------------------------------------------------------|
| `steps`     | INT         | ノイズスケジュールのステップ数を指定し、生成されるシグマシーケンスの粒度に影響を与えます。 |
| `sigma_max` | FLOAT       | ノイズスケジュールにおける最大シグマ値で、ノイズレベルの上限を設定します。                    |
| `sigma_min` | FLOAT       | ノイズスケジュールにおける最小シグマ値で、ノイズレベルの下限を設定します。                    |
| `rho`       | FLOAT       | ノイズスケジュール曲線の形状を制御するパラメータで、sigma_minからsigma_maxへのノイズレベルの進行に影響を与えます。 |

## 出力

| パラメータ | Data Type | 説明                                                                 |
|-----------|-------------|-----------------------------------------------------------------------------|
| `sigmas`  | SIGMAS      | Karras et al. (2022)のノイズスケジュールに従ったノイズレベル（シグマ）の生成されたシーケンス。 |
