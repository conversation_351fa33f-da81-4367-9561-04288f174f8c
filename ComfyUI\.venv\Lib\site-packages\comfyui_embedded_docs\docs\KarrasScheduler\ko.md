
KarrasScheduler 노드는 <PERSON><PERSON><PERSON> et al. (2022) 노이즈 스케줄을 기반으로 노이즈 레벨(시그마)의 시퀀스를 생성하도록 설계되었습니다. 이 스케줄러는 생성 모델에서 확산 과정을 제어하는 데 유용하며, 생성 과정의 각 단계에서 적용되는 노이즈 레벨을 세밀하게 조정할 수 있게 합니다.

## 입력

| 매개변수    | 데이터 유형 | 설명                                                                                                           |
| ----------- | ----------- | -------------------------------------------------------------------------------------------------------------- |
| `steps`     | INT         | 노이즈 스케줄의 단계 수를 지정하여 생성된 시그마 시퀀스의 세분화에 영향을 미칩니다.                            |
| `sigma_max` | FLOAT       | 노이즈 스케줄의 최대 시그마 값을 설정하여 노이즈 레벨의 상한선을 정합니다.                                     |
| `sigma_min` | FLOAT       | 노이즈 스케줄의 최소 시그마 값을 설정하여 노이즈 레벨의 하한선을 정합니다.                                     |
| `rho`       | FLOAT       | 노이즈 스케줄 곡선의 형태를 제어하는 매개변수로, sigma_min에서 sigma_max로의 노이즈 레벨 진행에 영향을 줍니다. |

## 출력

| 매개변수 | 데이터 유형 | 설명                                                                                   |
| -------- | ----------- | -------------------------------------------------------------------------------------- |
| `sigmas` | SIGMAS      | Karras et al. (2022) 노이즈 스케줄을 따르는 생성된 노이즈 레벨(시그마)의 시퀀스입니다. |
