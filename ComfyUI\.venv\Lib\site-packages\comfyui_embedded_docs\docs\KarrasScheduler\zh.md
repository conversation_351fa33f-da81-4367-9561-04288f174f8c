`KarrasScheduler` 节点旨在根据 Karras 等人（2022 年）的噪声时间表生成一系列噪声水平（sigmas）。这个调度器对于控制生成模型中的扩散过程非常有用，允许对生成过程中每一步应用的噪声水平进行微调。

## 输入

| 参数名称     | 数据类型       | 作用                                                         |
|--------------|----------------|--------------------------------------------------------------|
| `steps`      | INT            | 指定噪声时间表中的步骤数，影响生成的 sigmas 序列的粒度。   |
| `sigma_max`  | FLOAT          | 噪声时间表中的最大 sigma 值，设置噪声水平的上限。         |
| `sigma_min`  | FLOAT          | 噪声时间表中的最小 sigma 值，设置噪声水平的下限。         |
| `rho`        | FLOAT          | 控制噪声时间表曲线形状的参数，影响噪声水平从 sigma_min 到 sigma_max 的变化过程。 |

## 输出

| 参数名称   | 数据类型 | 作用                                                         |
|------------|----------|--------------------------------------------------------------|
| `sigmas`   | SIGMAS   | 根据 Karras 等人（2022 年）噪声时间表生成的噪声水平（sigmas）序列。 |

---
