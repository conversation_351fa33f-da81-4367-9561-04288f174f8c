
LatentAddノードは、2つの潜在表現の加算を目的として設計されています。これにより、これらの表現にエンコードされた特徴や特性を要素ごとに加算することで組み合わせることができます。

## 入力

| パラメータ    | Data Type | 説明 |
|--------------|-------------|-------------|
| `samples1`   | `LATENT`    | 加算される最初の潜在サンプルセットです。他の潜在サンプルセットと特徴を組み合わせる入力の一つを表します。 |
| `samples2`   | `LATENT`    | 加算される第二の潜在サンプルセットです。要素ごとの加算を通じて、最初の潜在サンプルセットと特徴を組み合わせるもう一つの入力として機能します。 |

## 出力

| パラメータ | データ型 | 説明 |
|-----------|-------------|-------------|
| `latent`  | `LATENT`    | 2つの潜在サンプルの要素ごとの加算の結果であり、両方の入力の特徴を組み合わせた新しい潜在サンプルセットを表します。 |
