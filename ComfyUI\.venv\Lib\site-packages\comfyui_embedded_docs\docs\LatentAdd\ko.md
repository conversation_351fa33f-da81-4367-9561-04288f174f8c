
LatentAdd 노드는 두 개의 잠재적 표현을 더하기 위해 설계되었습니다. 요소별 덧셈을 수행하여 이러한 표현에 인코딩된 특징이나 특성의 결합을 용이하게 합니다.

## 입력

| 매개변수   | 데이터 유형 | 설명                                                                                                                       |
| ---------- | ----------- | -------------------------------------------------------------------------------------------------------------------------- |
| `samples1` | `LATENT`    | 더해질 첫 번째 잠재적 샘플 세트입니다. 이는 다른 잠재적 샘플 세트와 결합될 입력 중 하나를 나타냅니다.                      |
| `samples2` | `LATENT`    | 더해질 두 번째 잠재적 샘플 세트입니다. 이는 첫 번째 잠재적 샘플 세트와 요소별 덧셈을 통해 결합되는 다른 입력을 제공합니다. |

## 출력

| 매개변수 | 데이터 유형 | 설명                                                                                               |
| -------- | ----------- | -------------------------------------------------------------------------------------------------- |
| `latent` | `LATENT`    | 두 잠재적 샘플의 요소별 덧셈 결과로, 두 입력의 특징을 결합한 새로운 잠재적 샘플 세트를 나타냅니다. |
