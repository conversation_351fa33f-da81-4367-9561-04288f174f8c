
LatentBatchSeedBehaviorノードは、潜在サンプルのバッチのシード挙動を変更するために設計されています。バッチ全体のシードをランダム化または固定することができ、生成プロセスにおいて、生成された出力の変動性を導入するか、一貫性を維持するかに影響を与えます。

## 入力

| パラメータ       | Data Type | 説明 |
|-----------------|--------------|-------------|
| `samples`       | `LATENT`     | `samples`パラメータは、処理される潜在サンプルのバッチを表します。選択されたシード挙動に応じてその変更が行われ、生成された出力の一貫性または変動性に影響を与えます。 |
| `seed_behavior`  | COMBO[STRING] | `seed_behavior`パラメータは、潜在サンプルのバッチのシードをランダム化するか固定するかを決定します。この選択は、バッチ全体の生成プロセスにおいて、変動性を導入するか、一貫性を確保するかに大きな影響を与えます。 |

## 出力

| パラメータ | データ型 | 説明 |
|-----------|-------------|-------------|
| `latent`  | `LATENT`    | 出力は、指定されたシード挙動に基づいて調整された入力潜在サンプルの修正版です。選択されたシード挙動を反映するために、バッチインデックスを維持または変更します。 |
