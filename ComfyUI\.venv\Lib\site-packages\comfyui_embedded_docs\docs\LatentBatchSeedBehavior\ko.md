
LatentBatchSeedBehavior 노드는 잠재 샘플 배치의 시드 동작을 수정하도록 설계되었습니다. 배치 전체에 걸쳐 시드를 무작위화하거나 고정할 수 있어, 생성 과정에서 변동성을 도입하거나 생성된 출력의 일관성을 유지할 수 있습니다.

## 입력

| 매개변수        | 데이터 유형   | 설명                                                                                                                                                                                |
| --------------- | ------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `samples`       | `LATENT`      | 'samples' 매개변수는 처리될 잠재 샘플 배치를 나타냅니다. 선택한 시드 동작에 따라 수정되며, 생성된 출력의 일관성 또는 변동성에 영향을 미칩니다.                                      |
| `seed_behavior` | COMBO[STRING] | 'seed_behavior' 매개변수는 잠재 샘플 배치의 시드를 무작위화할지 고정할지를 결정합니다. 이 선택은 생성 과정에 큰 영향을 미치며, 배치 전체의 변동성을 도입하거나 일관성을 보장합니다. |

## 출력

| 매개변수 | 데이터 유형 | 설명                                                                                                                                          |
| -------- | ----------- | --------------------------------------------------------------------------------------------------------------------------------------------- |
| `latent` | `LATENT`    | 출력은 입력 잠재 샘플의 수정된 버전으로, 지정된 시드 동작에 따라 조정됩니다. 선택한 시드 동작을 반영하여 배치 인덱스를 유지하거나 변경합니다. |
