
潜在批量种子行为节点旨在修改一批潜在样本的种子行为。它允许对种子进行随机化或固定，从而通过引入变化性或保持生成输出的一致性来影响生成过程。

## 输入

| 参数名称         | 数据类型     | 作用                                                         |
|------------------|--------------|--------------------------------------------------------------|
| `samples`        | `LATENT`     | `samples` 参数代表要处理的潜在样本批次。其修改取决于所选择的种子行为，影响生成输出的一致性或变化性。 |
| `seed_behavior`  | COMBO[STRING] | `seed_behavior` 参数决定一批潜在样本的种子应该是随机化的还是固定的。这个选择通过引入变化性或确保批次的一致性，显著影响生成过程。 |

## 输出

| 参数名称 | 数据类型 | 作用                                       |
|----------|----------|--------------------------------------------|
| `latent` | `LATENT` | 输出是基于指定的种子行为调整后的输入潜在样本的修改版本。它保持或改变批次索引以反映所选择的种子行为。 |
