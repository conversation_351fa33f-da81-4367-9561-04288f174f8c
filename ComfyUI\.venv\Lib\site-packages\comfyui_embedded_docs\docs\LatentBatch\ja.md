
LatentBatchノードは、2つの潜在サンプルセットを1つのバッチに統合するように設計されています。統合前に、必要に応じて一方のセットのサイズを他方に合わせて調整することができます。この操作により、異なる潜在表現を組み合わせて、さらなる処理や生成タスクを行うことが可能になります。

## 入力

| パラメータ    | Data Type | 説明 |
|--------------|-------------|-------------|
| `samples1`   | `LATENT`    | 統合される最初の潜在サンプルセットです。統合されたバッチの最終的な形状を決定する上で重要な役割を果たします。 |
| `samples2`   | `LATENT`    | 統合される第二の潜在サンプルセットです。最初のセットと寸法が異なる場合、統合前に互換性を確保するためにサイズが調整されます。 |

## 出力

| パラメータ | データ型 | 説明 |
|-----------|-------------|-------------|
| `latent`  | `LATENT`    | 統合された潜在サンプルセットであり、さらなる処理のために1つのバッチに結合されています。 |
