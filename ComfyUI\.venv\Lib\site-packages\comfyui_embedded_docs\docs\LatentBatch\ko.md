
LatentBatch 노드는 두 세트의 잠재 샘플을 하나의 배치로 병합하도록 설계되었습니다. 병합 전에 한 세트를 다른 세트의 크기에 맞게 조정할 수 있습니다. 이 작업은 다양한 잠재 표현을 결합하여 추가 처리나 생성 작업을 용이하게 합니다.

## 입력

| 매개변수   | 데이터 유형 | 설명                                                                                                                      |
| ---------- | ----------- | ------------------------------------------------------------------------------------------------------------------------- |
| `samples1` | `LATENT`    | 병합될 첫 번째 잠재 샘플 세트입니다. 병합된 배치의 최종 형태를 결정하는 데 중요한 역할을 합니다.                          |
| `samples2` | `LATENT`    | 병합될 두 번째 잠재 샘플 세트입니다. 첫 번째 세트와 크기가 다를 경우, 병합 전에 호환성을 보장하기 위해 크기가 조정됩니다. |

## 출력

| 매개변수 | 데이터 유형 | 설명                                                                         |
| -------- | ----------- | ---------------------------------------------------------------------------- |
| `latent` | `LATENT`    | 병합된 잠재 샘플 세트로, 이제 추가 처리를 위해 하나의 배치로 결합되었습니다. |
