
LatentCompositeMaskedノードは、指定された座標で2つの潜在表現を合成するように設計されており、オプションでマスクを使用してより制御された合成を行うことができます。このノードを使用すると、1つの画像の一部を別の画像に重ね合わせることで、複雑な潜在画像を作成でき、ソース画像を完璧にフィットさせるためにリサイズすることも可能です。

## 入力

| パラメータ | データ型 | 説明 |
|-----------|-------------|-------------|
| `destination` | `LATENT`    | 他の潜在表現が合成される潜在表現です。合成操作のベースレイヤーとして機能します。 |
| `source` | `LATENT`    | `destination`に合成される潜在表現です。このソースレイヤーは、指定されたパラメータに従ってリサイズおよび配置できます。 |
| `x` | `INT`       | ソースが配置される`destination`潜在表現のx座標です。ソースレイヤーの正確な位置決めを可能にします。 |
| `y` | `INT`       | ソースが配置される`destination`潜在表現のy座標で、正確なオーバーレイ位置を可能にします。 |
| `resize_source` | `BOOLEAN` | 合成前にソース潜在表現を`destination`の寸法に合わせてリサイズするかどうかを示すブールフラグです。 |
| `mask` | `MASK`     | ソースを`destination`にブレンドする際に使用できるオプションのマスクです。マスクは、最終的な合成でソースのどの部分が表示されるかを定義します。 |

## 出力

| パラメータ | データ型 | 説明 |
|-----------|-------------|-------------|
| `latent`  | `LATENT`    | ソースを`destination`に合成した後の潜在表現で、選択的なブレンドのためにマスクを使用することもあります。 |
