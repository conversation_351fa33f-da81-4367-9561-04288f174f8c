
此节点设计用于在指定坐标处将两个潜在表示混合在一起，可选择使用遮罩进行更受控的组合。该节点允许通过将一个图像的部分覆盖在另一个图像上来创建复杂的潜在图像，并能够调整源图像的大小以实现完美贴合。

## 输入

| 参数名称 | 数据类型 | 作用 |
| --- | --- | --- |
| `destination` | `LATENT` | 另一个潜在表示将被组合到其上的潜在表示。作为组合操作的基础层。 |
| `source` | `LATENT` | 要组合到目标上的潜在表示。这个源层可以根据指定的参数调整大小和位置。 |
| `x` | `INT` | 在目标潜在表示中放置源的x坐标。允许精确定位源层。 |
| `y` | `INT` | 在目标潜在表示中放置源的y坐标，实现准确的叠加定位。 |
| `resize_source` | `BOOLEAN` | 一个布尔标志，指示在组合之前是否应将源潜在表示调整大小以匹配目标的尺寸。 |
| `mask` | `MASK` | 一个可选的遮罩，用于控制源与目标的混合。遮罩定义了源的哪些部分将在最终组合中可见。 |

## 输出

| 参数名称 | 数据类型 | 作用 |
| --- | --- | --- |
| `latent` | `LATENT` | 在将源组合到目标上后得到的潜在表示，可能使用遮罩进行选择性混合。 |
