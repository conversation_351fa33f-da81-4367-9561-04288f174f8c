
LatentInterpolateノードは、指定された比率に基づいて2つの潜在サンプルセット間で補間を行い、両方のセットの特徴をブレンドして新しい中間の潜在サンプルセットを生成するように設計されています。

## 入力

| パラメータ    | Data Type | 説明 |
|--------------|-------------|-------------|
| `samples1`   | `LATENT`    | 補間される最初の潜在サンプルセットです。補間プロセスの開始点として機能します。 |
| `samples2`   | `LATENT`    | 補間される第二の潜在サンプルセットです。補間プロセスの終点として機能します。 |
| `ratio`      | `FLOAT`     | 補間された出力における各サンプルセットの重みを決定する浮動小数点値です。比率が0の場合、最初のセットのコピーを生成し、比率が1の場合、第二のセットのコピーを生成します。 |

## 出力

| パラメータ | データ型 | 説明 |
|-----------|-------------|-------------|
| `latent`  | `LATENT`    | 出力は、指定された比率に基づいて2つの入力セット間の補間状態を表す新しい潜在サンプルセットです。 |
