
LatentInterpolate 노드는 지정된 비율에 따라 두 세트의 잠재 샘플 간의 보간을 수행하도록 설계되었으며, 두 세트의 특성을 혼합하여 새로운 중간 잠재 샘플 세트를 생성합니다.

## 입력

| 매개변수   | 데이터 유형 | 설명                                                                                                                                                                   |
| ---------- | ----------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `samples1` | `LATENT`    | 보간될 첫 번째 잠재 샘플 세트입니다. 보간 과정의 시작점을 나타냅니다.                                                                                                  |
| `samples2` | `LATENT`    | 보간될 두 번째 잠재 샘플 세트입니다. 보간 과정의 끝점을 나타냅니다.                                                                                                    |
| `ratio`    | `FLOAT`     | 보간된 출력에서 각 샘플 세트의 가중치를 결정하는 부동 소수점 값입니다. 비율이 0이면 첫 번째 세트의 복사본을 생성하고, 비율이 1이면 두 번째 세트의 복사본을 생성합니다. |

## 출력

| 매개변수 | 데이터 유형 | 설명                                                                                            |
| -------- | ----------- | ----------------------------------------------------------------------------------------------- |
| `latent` | `LATENT`    | 출력은 지정된 비율에 따라 두 입력 세트 사이의 보간 상태를 나타내는 새로운 잠재 샘플 세트입니다. |
