
Latent插值节点旨在基于指定的比率对两组潜在样本执行插值，混合这两组的特性以产生一组新的、中间的潜在样本。

## 输入

| 参数名称 | 数据类型 | 作用 |
| --- | --- | --- |
| `samples1` | `LATENT` | 要进行插值的第一组潜在样本。它作为插值过程的起始点。 |
| `samples2` | `LATENT` | 要进行插值的第二组潜在样本。它作为插值过程的终点。 |
| `ratio` | `FLOAT` | 一个浮点值，用于确定插值输出中每组样本的权重。比率为0时产生第一组的副本，比率为1时产生第二组的副本。 |

## 输出

| 参数名称 | 数据类型 | 作用 |
| --- | --- | --- |
| `latent` | `LATENT` | 输出是一个新的潜在样本集，代表两个输入集之间的插值状态，基于指定的比率。 |

---
