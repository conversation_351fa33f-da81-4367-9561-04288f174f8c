
LatentMultiplyノードは、指定された乗数によってサンプルの潜在表現をスケーリングするように設計されています。この操作により、潜在空間内の特徴の強度や大きさを調整し、生成されたコンテンツの微調整や特定の潜在方向内でのバリエーションの探索が可能になります。

## 入力

| パラメータ    | Data Type | 説明 |
|--------------|-------------|-------------|
| `samples`    | `LATENT`    | `samples`パラメータは、スケーリングされる潜在表現を表します。これは、乗算操作が実行される入力データを定義するために重要です。 |
| `multiplier` | `FLOAT`     | `multiplier`パラメータは、潜在サンプルに適用されるスケーリング係数を指定します。これは、生成された出力の微妙な制御を可能にする潜在特徴の大きさを調整する上で重要な役割を果たします。 |

## 出力

| パラメータ | データ型 | 説明 |
|-----------|-------------|-------------|
| `latent`  | `LATENT`    | 出力は、指定された乗数でスケーリングされた入力潜在サンプルの修正版です。これにより、潜在空間内の特徴の強度を調整することで、バリエーションの探索が可能になります。 |
