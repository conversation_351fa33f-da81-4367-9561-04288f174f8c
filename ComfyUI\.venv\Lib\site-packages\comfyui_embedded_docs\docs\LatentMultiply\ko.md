
LatentMultiply 노드는 샘플의 잠재적 표현을 지정된 배수로 확장하도록 설계되었습니다. 이 작업은 잠재 공간 내에서 특징의 강도나 크기를 조정하여 생성된 콘텐츠의 미세 조정이나 주어진 잠재 방향 내에서의 변형 탐색을 가능하게 합니다.

## 입력

| 매개변수     | 데이터 유형 | 설명                                                                                                                                                  |
| ------------ | ----------- | ----------------------------------------------------------------------------------------------------------------------------------------------------- |
| `samples`    | `LATENT`    | 'samples' 매개변수는 확장될 잠재적 표현을 나타냅니다. 이는 곱셈 작업이 수행될 입력 데이터를 정의하는 데 중요합니다.                                   |
| `multiplier` | `FLOAT`     | 'multiplier' 매개변수는 잠재 샘플에 적용될 확장 계수를 지정합니다. 이는 잠재 특징의 크기를 조정하여 생성된 출력에 대한 세밀한 제어를 가능하게 합니다. |

## 출력

| 매개변수 | 데이터 유형 | 설명                                                                                                                                       |
| -------- | ----------- | ------------------------------------------------------------------------------------------------------------------------------------------ |
| `latent` | `LATENT`    | 출력은 지정된 배수로 확장된 입력 잠재 샘플의 수정된 버전입니다. 이는 잠재 공간 내에서 특징의 강도를 조정하여 변형을 탐색할 수 있게 합니다. |
