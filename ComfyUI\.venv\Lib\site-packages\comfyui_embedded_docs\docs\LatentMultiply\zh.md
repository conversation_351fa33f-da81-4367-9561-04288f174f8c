
此节点设计用于通过指定的乘数来缩放样本的潜在表示。此操作允许调整潜在空间内特征的强度或大小，从而实现对生成内容的微调或探索给定潜在方向内的变体。

## 输入

| 参数名称 | 数据类型 | 作用 |
| --- | --- | --- |
| `samples` | `LATENT` | `samples`参数代表要缩放的潜在表示。它对于定义乘法操作将执行的输入数据至关重要。 |
| `multiplier` | `FLOAT` | `multiplier`参数指定要应用于潜在样本的缩放因子。它在调整潜在特征的大小方面起着关键作用，允许对生成的输出进行精细控制。 |

## 输出

| 参数名称 | 数据类型 | 作用 |
| --- | --- | --- |
| `latent` | `LATENT` | 输出是输入潜在样本的修改版本，已按指定的乘数进行了缩放。这允许通过调整其特征的强度来探索潜在空间内的变体。 |

---
