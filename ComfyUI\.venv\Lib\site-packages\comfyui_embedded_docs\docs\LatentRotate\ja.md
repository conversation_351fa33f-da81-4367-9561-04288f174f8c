
LatentRotateノードは、指定された角度で画像の潜在表現を回転させるように設計されています。潜在空間を操作して回転効果を実現する複雑さを抽象化し、ユーザーが生成モデルの潜在空間で画像を簡単に変換できるようにします。

## 入力

| パラメータ | データ型 | 説明 |
|-----------|-------------|-------------|
| `samples` | `LATENT`    | 'samples'パラメータは、回転される画像の潜在表現を表します。これは回転操作の開始点を決定する上で重要です。 |
| `rotation` | COMBO[STRING] | 'rotation'パラメータは、潜在画像が回転される角度を指定します。これは結果として得られる画像の向きに直接影響を与えます。 |

## 出力

| パラメータ | データ型 | 説明 |
|-----------|-------------|-------------|
| `latent`  | `LATENT`    | 出力は、指定された角度で回転された入力潜在表現の修正版です。 |
