
LatentRotate 노드는 이미지의 잠재 표현을 지정된 각도로 회전하도록 설계되었습니다. 잠재 공간을 조작하여 회전 효과를 얻는 복잡성을 추상화하여 사용자가 생성 모델의 잠재 공간에서 이미지를 쉽게 변환할 수 있도록 합니다.

## 입력

| 매개변수   | 데이터 유형   | 설명                                                                                                              |
| ---------- | ------------- | ----------------------------------------------------------------------------------------------------------------- |
| `samples`  | `LATENT`      | 'samples' 매개변수는 회전될 이미지의 잠재 표현을 나타냅니다. 회전 작업의 시작점을 결정하는 데 중요합니다.         |
| `rotation` | COMBO[STRING] | 'rotation' 매개변수는 잠재 이미지가 회전될 각도를 지정합니다. 이는 결과 이미지의 방향에 직접적인 영향을 미칩니다. |

## 출력

| 매개변수 | 데이터 유형 | 설명                                                            |
| -------- | ----------- | --------------------------------------------------------------- |
| `latent` | `LATENT`    | 출력은 지정된 각도로 회전된 입력 잠재 표현의 수정된 버전입니다. |
