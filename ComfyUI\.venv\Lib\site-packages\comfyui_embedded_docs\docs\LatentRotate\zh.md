LatentRotate节点旨在通过指定角度旋转图像的潜在表示。它抽象了操作潜在空间以实现旋转效果的复杂性，使用户能够在生成模型的潜在空间中轻松变换图像。

## 输入

| 参数名称   | 数据类型 | 作用                                                         |
|------------|----------|--------------------------------------------------------------|
| `samples`  | LATENT   | `samples` 参数代表要旋转的图像的潜在表示。它对于确定旋转操作的起始点至关重要。 |
| `rotation` | COMBO[STRING] | `rotation` 参数指定了潜在图像应旋转的角度。它直接影响生成图像的方向。 |

## 输出

| 参数名称 | 数据类型 | 作用                                                         |
|----------|----------|--------------------------------------------------------------|
| `latent` | LATENT   | 输出是输入潜在表示的修改版本，已按指定角度旋转。          |
