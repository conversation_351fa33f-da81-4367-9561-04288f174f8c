
LatentSubtractノードは、一つの潜在表現から別の潜在表現を減算するために設計されています。この操作により、ある潜在空間に表現された特徴や属性を別の潜在空間から効果的に除去することで、生成モデルの出力の特性を操作または修正することができます。

## 入力

| パラメータ    | Data Type | 説明 |
|--------------|-------------|-------------|
| `samples1`   | `LATENT`    | 減算される最初の潜在サンプルセットです。減算操作の基礎として機能します。 |
| `samples2`   | `LATENT`    | 最初のセットから減算される第二の潜在サンプルセットです。この操作により、属性や特徴を除去することで、生成モデルの出力を変更することができます。 |

## 出力

| パラメータ | データ型 | 説明 |
|-----------|-------------|-------------|
| `latent`  | `LATENT`    | 最初のセットから第二の潜在サンプルセットを減算した結果です。この修正された潜在表現は、さらなる生成タスクに使用できます。 |
