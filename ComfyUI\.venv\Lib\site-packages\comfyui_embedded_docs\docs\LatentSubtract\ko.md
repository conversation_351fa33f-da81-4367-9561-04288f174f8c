
LatentSubtract 노드는 한 잠재적 표현에서 다른 잠재적 표현을 빼기 위해 설계되었습니다. 이 작업은 한 잠재 공간에서 다른 잠재 공간으로 표현된 특징이나 속성을 효과적으로 제거하여 생성 모델의 출력 특성을 조작하거나 수정하는 데 사용할 수 있습니다.

## 입력

| 매개변수   | 데이터 유형 | 설명                                                                                                                               |
| ---------- | ----------- | ---------------------------------------------------------------------------------------------------------------------------------- |
| `samples1` | `LATENT`    | 뺄셈의 기준이 되는 첫 번째 잠재적 샘플 세트입니다.                                                                                 |
| `samples2` | `LATENT`    | 첫 번째 세트에서 뺄 두 번째 잠재적 샘플 세트입니다. 이 작업은 속성이나 특징을 제거하여 생성 모델의 결과 출력을 변경할 수 있습니다. |

## 출력

| 매개변수 | 데이터 유형 | 설명                                                                                                                    |
| -------- | ----------- | ----------------------------------------------------------------------------------------------------------------------- |
| `latent` | `LATENT`    | 첫 번째 세트에서 두 번째 잠재적 샘플 세트를 뺀 결과입니다. 이 수정된 잠재적 표현은 추가 생성 작업에 사용할 수 있습니다. |
