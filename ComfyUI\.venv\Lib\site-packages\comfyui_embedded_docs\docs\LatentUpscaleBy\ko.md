
LatentUpscaleBy 노드는 이미지의 잠재 표현을 업스케일하기 위해 설계되었습니다. 이 노드는 스케일 팩터와 업스케일 방법을 조정할 수 있어 잠재 샘플의 해상도를 유연하게 향상시킬 수 있습니다.

## 입력

| 매개변수         | 데이터 유형   | 설명                                                                                                                                              |
| ---------------- | ------------- | ------------------------------------------------------------------------------------------------------------------------------------------------- |
| `samples`        | `LATENT`      | 업스케일될 이미지의 잠재 표현입니다. 이 매개변수는 업스케일링 과정을 거칠 입력 데이터를 결정하는 데 중요합니다.                                   |
| `upscale_method` | COMBO[STRING] | 잠재 샘플을 업스케일하는 데 사용되는 방법을 지정합니다. 방법의 선택은 업스케일된 출력의 품질과 특성에 크게 영향을 미칠 수 있습니다.               |
| `scale_by`       | `FLOAT`       | 잠재 샘플이 스케일되는 팩터를 결정합니다. 이 매개변수는 출력의 해상도에 직접적인 영향을 미치며, 업스케일링 과정을 정밀하게 제어할 수 있게 합니다. |

## 출력

| 매개변수 | 데이터 유형 | 설명                                                                                                                                           |
| -------- | ----------- | ---------------------------------------------------------------------------------------------------------------------------------------------- |
| `latent` | `LATENT`    | 추가 처리나 생성 작업을 위해 준비된 업스케일된 잠재 표현입니다. 이 출력은 생성된 이미지의 해상도를 향상시키거나 후속 모델 작업에 필수적입니다. |
