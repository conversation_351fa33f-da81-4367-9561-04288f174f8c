
LatentUpscaleノードは、画像の潜在表現を拡大するために設計されています。出力画像の寸法や拡大方法を調整することができ、潜在画像の解像度を柔軟に向上させることが可能です。

## 入力

| パラメータ | データ型 | 説明 |
|-----------|-------------|-------------|
| `samples` | `LATENT`    | 拡大される画像の潜在表現。このパラメータは拡大プロセスの開始点を決定するために重要です。 |
| `upscale_method` | COMBO[STRING] | 潜在画像を拡大するために使用される方法を指定します。異なる方法は拡大された画像の品質や特性に影響を与える可能性があります。 |
| `width`   | `INT`       | 拡大された画像の希望する幅。0に設定すると、高さに基づいてアスペクト比を維持するために計算されます。 |
| `height`  | `INT`       | 拡大された画像の希望する高さ。0に設定すると、幅に基づいてアスペクト比を維持するために計算されます。 |
| `crop`    | COMBO[STRING] | 拡大された画像をどのようにトリミングするかを決定し、出力の最終的な外観と寸法に影響を与えます。 |

## 出力

| パラメータ | データ型 | 説明 |
|-----------|-------------|-------------|
| `latent`  | `LATENT`    | さらなる処理や生成の準備が整った、拡大された画像の潜在表現。 |
