
LatentUpscale 노드는 이미지의 잠재 표현을 업스케일하기 위해 설계되었습니다. 이 노드는 출력 이미지의 크기와 업스케일 방법을 조정할 수 있어 잠재 이미지의 해상도를 유연하게 향상시킬 수 있습니다.

## 입력

| 매개변수         | 데이터 유형   | 설명                                                                                                                               |
| ---------------- | ------------- | ---------------------------------------------------------------------------------------------------------------------------------- |
| `samples`        | `LATENT`      | 업스케일될 이미지의 잠재 표현입니다. 이 매개변수는 업스케일링 과정의 시작점을 결정하는 데 중요합니다.                              |
| `upscale_method` | COMBO[STRING] | 잠재 이미지를 업스케일하는 데 사용되는 방법을 지정합니다. 다양한 방법은 업스케일된 이미지의 품질과 특성에 영향을 미칠 수 있습니다. |
| `width`          | `INT`         | 업스케일된 이미지의 원하는 너비입니다. 0으로 설정하면 높이에 따라 비율을 유지하며 계산됩니다.                                      |
| `height`         | `INT`         | 업스케일된 이미지의 원하는 높이입니다. 0으로 설정하면 너비에 따라 비율을 유지하며 계산됩니다.                                      |
| `crop`           | COMBO[STRING] | 업스케일된 이미지가 어떻게 잘릴지를 결정하여 출력의 최종 외관과 크기에 영향을 줍니다.                                              |

## 출력

| 매개변수 | 데이터 유형 | 설명                                                                |
| -------- | ----------- | ------------------------------------------------------------------- |
| `latent` | `LATENT`    | 추가 처리나 생성을 위해 준비된 업스케일된 이미지의 잠재 표현입니다. |
