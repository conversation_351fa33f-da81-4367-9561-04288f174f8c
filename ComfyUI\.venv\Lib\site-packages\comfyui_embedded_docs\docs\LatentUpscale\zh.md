此节点设计用于放大图像的潜在表示。它允许调整输出图像的尺寸和放大方法，提供在提高潜在图像分辨率方面的灵活性。

## 输入

| 参数名称 | 数据类型 | 作用 |
| --- | --- | --- |
| `samples` | `LATENT` | 要放大的图像的潜在表示，它是确定放大过程起点的关键参数。 |
| `upscale_method` | COMBO[STRING] | 指定用于放大潜在图像的方法。不同的方法会影响放大图像的质量和特性。 |
| `width` | `INT` | 期望放大图像的宽度。如果设置为0，将根据高度计算以保持纵横比。 |
| `height` | `INT` | 期望放大图像的高度。如果设置为0，将根据宽度计算以保持纵横比。 |
| `crop` | COMBO[STRING] | 确定如何裁剪放大后的图像，影响输出的最终外观和尺寸。 |

## 输出

| 参数名称 | 数据类型 | 作用 |
| --- | --- | --- |
| `latent` | `LATENT` | 图像的放大潜在表示，准备好进行进一步处理或生成。 |
