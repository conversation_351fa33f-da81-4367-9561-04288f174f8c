
LoadImageMaskノードは、指定されたパスから画像とその関連マスクを読み込み、さらなる画像操作や分析タスクとの互換性を確保するために処理します。マスクのアルファチャンネルの存在など、さまざまな画像フォーマットと条件に対応し、標準化されたフォーマットに変換して画像とマスクを下流処理のために準備します。

## 入力

| パラメータ | データ型 | 説明 |
|-----------|-------------|-------------|
| `image`   | COMBO[STRING] | 'image'パラメータは、読み込みおよび処理される画像ファイルを指定します。これは、マスク抽出とフォーマット変換のためのソース画像を提供することで、出力を決定する上で重要な役割を果たします。 |
| `channel` | COMBO[STRING] | 'channel'パラメータは、マスク生成に使用される画像のカラーチャンネルを指定します。これにより、さまざまなカラーチャンネルに基づくマスク作成の柔軟性が提供され、さまざまな画像処理シナリオでノードの有用性が向上します。 |

## 出力

| パラメータ | データ型 | 説明 |
|-----------|-------------|-------------|
| `mask`    | `MASK`      | このノードは、指定された画像とチャンネルから生成されたマスクを出力し、画像操作タスクでのさらなる処理に適した標準化されたフォーマットで準備します。 |
