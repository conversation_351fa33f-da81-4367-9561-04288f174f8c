LoadImageMask 节点旨在从指定路径加载图像及其相关遮罩，处理它们以确保与进一步的图像操作或分析任务兼容。它专注于处理各种图像格式和条件，例如遮罩的 alpha 通道的存在，并通过对它们进行标准化格式转换，为下游处理准备图像和遮罩。

## 输入

| 参数名称   | 数据类型 | 作用                                                         |
|------------|----------|--------------------------------------------------------------|
| `image`    | COMBO[STRING] | 'image' 参数指定要加载和处理的图像文件。它通过提供源图像用于遮罩提取和格式转换，在确定输出方面起着关键作用。 |
| `channel`  | COMBO[STRING] | 'channel' 参数指定用于生成遮罩的图像颜色通道。这允许根据不同的颜色通道灵活创建遮罩，增强节点在各种图像处理场景中的实用性。 |

## 输出

| 参数名称 | 数据类型 | 作用                                                         |
|----------|----------|--------------------------------------------------------------|
| `mask`   | `MASK`   | 此节点输出从指定图像和通道生成的遮罩，以标准化格式准备，适用于图像操作任务中的进一步处理。 |
