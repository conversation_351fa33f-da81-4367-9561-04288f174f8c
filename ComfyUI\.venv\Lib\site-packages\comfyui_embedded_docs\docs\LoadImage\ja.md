
LoadImageノードは、指定されたパスから画像をロードし、前処理を行うために設計されています。複数フレームを持つ画像フォーマットを処理し、EXIFデータに基づく回転などの必要な変換を適用し、ピクセル値を正規化し、オプションでアルファチャンネルを持つ画像のマスクを生成します。このノードは、パイプライン内でのさらなる処理や分析のために画像を準備するために不可欠です。

## 入力

| パラメータ | データ型 | 説明 |
|-----------|--------------|-------------|
| `image`   | COMBO[STRING] | `image`パラメータは、ロードおよび処理される画像の識別子を指定します。これは、画像ファイルへのパスを決定し、その後の変換と正規化のために画像をロードするために重要です。 |

## 出力

| パラメータ | データ型 | 説明 |
|-----------|-------------|-------------|
| `image`   | `IMAGE`     | 必要な変換が適用され、ピクセル値が正規化された処理済み画像です。さらなる処理や分析の準備が整っています。 |
| `mask`    | `MASK`      | 画像にマスクを提供するオプションの出力で、画像に透明性のためのアルファチャンネルが含まれるシナリオで役立ちます。 |
