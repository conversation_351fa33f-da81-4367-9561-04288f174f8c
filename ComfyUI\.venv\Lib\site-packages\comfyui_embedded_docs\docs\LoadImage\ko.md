
LoadImage 노드는 지정된 경로에서 이미지를 불러오고 전처리하도록 설계되었습니다. 여러 프레임을 가진 이미지 형식을 처리하고, EXIF 데이터에 기반한 회전과 같은 필요한 변환을 적용하며, 픽셀 값을 정규화하고, 선택적으로 알파 채널이 있는 이미지에 대한 마스크를 생성합니다. 이 노드는 파이프라인 내에서 이미지를 추가 처리 또는 분석하기 위해 준비하는 데 필수적입니다.

## 입력

| 매개변수 | 데이터 유형   | 설명                                                                                                                                                             |
| -------- | ------------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `image`  | COMBO[STRING] | 'image' 매개변수는 불러오고 처리할 이미지의 식별자를 지정합니다. 이는 이미지 파일의 경로를 결정하고, 이후 변환 및 정규화를 위해 이미지를 불러오는 데 중요합니다. |

## 출력

| 매개변수 | 데이터 유형 | 설명                                                                                                         |
| -------- | ----------- | ------------------------------------------------------------------------------------------------------------ |
| `image`  | `IMAGE`     | 필요한 변환이 적용되고 픽셀 값이 정규화된 처리된 이미지입니다. 추가 처리 또는 분석을 위해 준비되어 있습니다. |
| `mask`   | `MASK`      | 이미지에 알파 채널이 포함된 경우 투명성을 위한 마스크를 제공하는 선택적 출력입니다.                          |
