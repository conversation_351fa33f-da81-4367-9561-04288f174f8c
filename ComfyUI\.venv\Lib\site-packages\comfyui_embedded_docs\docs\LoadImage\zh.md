
在节点上右键,在菜单中选择**Open in MaskEditor-在遮罩编辑器中打开**，可以打开已载入图像的遮罩编辑器

>上传后的图像会默认上传至*ComfyUI/input*文件夹，默认加载图像为 **input**文件夹内图像

加载图像节点旨在从指定路径加载和预处理图像。它处理具有多帧的图像格式，应用必要的变换，如根据EXIF数据旋转，归一化像素值，并可选择为具有alpha通道的图像生成遮罩。此节点对于准备图像以在管道内进行进一步处理或分析至关重要。

## 输入

| 参数名称 | 数据类型 | 作用                                                         |
|----------|----------|--------------------------------------------------------------|
| `image`  | COMBO[STRING] | `image` 参数指定要加载和处理的图像的标识符。它对于确定图像文件的路径，随后加载图像以进行转换和归一化至关重要。 |

## 输出

| 参数名称 | 数据类型 | 作用                                                         |
|----------|----------|--------------------------------------------------------------|
| `image`  | `IMAGE`  | 已处理的图像，像素值已归一化，并根据需要应用了变换。它准备好进行进一步的处理或分析。 |
| `mask`   | `MASK`   | （可选）为图像提供的遮罩输出，在图像包含用于透明度的alpha通道的场景中非常有用。 |
