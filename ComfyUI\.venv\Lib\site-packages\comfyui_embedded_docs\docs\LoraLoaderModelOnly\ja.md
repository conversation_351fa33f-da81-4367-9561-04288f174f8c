このノードは、`ComfyUI/models/loras` フォルダーにあるモデルを検出し、また、extra_model_paths.yaml ファイルで設定された追加パスのモデルも読み込みます。場合によっては、**ComfyUIのインターフェースを更新する**必要があるかもしれませんので、対応するフォルダー内のモデルファイルを読み込むことができます。

このノードは、CLIPモデルを必要とせずにLoRAモデルをロードすることに特化しており、LoRAパラメータに基づいて既存のモデルを強化または修正することに焦点を当てています。LoRAパラメータを通じてモデルの強度を動的に調整することができ、モデルの動作を細かく制御することが可能です。

## 入力

| フィールド         | Comfy dtype       | 説明                                                                                   |
|-------------------|-------------------|---------------------------------------------------------------------------------------|
| `model`           | `MODEL`           | LoRA調整が適用されるベースモデル。                                                     |
| `lora_name`       | `COMBO[STRING]`   | ロードするLoRAファイルの名前で、モデルに適用する調整を指定します。                     |
| `strength_model`  | `FLOAT`           | LoRA調整の強度を決定し、値が高いほど強い修正を示します。                               |

## 出力

| フィールド | Data Type | 説明                                                              |
|-----------|-------------|------------------------------------------------------------------|
| `model`   | MODEL     | LoRA調整が適用された修正済みモデルで、モデルの動作や能力の変化を反映します。 |
