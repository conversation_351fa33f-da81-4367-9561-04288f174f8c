이 노드는 `ComfyUI/models/loras` 폴더에 있는 모델을 감지하며,
또한 extra_model_paths.yaml 파일에서 설정한 추가 경로의 모델도 읽어옵니다.
때때로 **ComfyUI 인터페이스를 새로 고침**해야 해당 폴더의 모델 파일을 읽을 수 있습니다.

이 노드는 CLIP 모델 없이 LoRA 모델을 로드하는 데 특화되어 있으며, LoRA 매개변수를 기반으로 주어진 모델을 향상시키거나 수정하는 데 중점을 둡니다. LoRA 매개변수를 통해 모델의 강도를 동적으로 조정할 수 있어, 모델의 동작을 세밀하게 제어할 수 있습니다.

## 입력

| 필드             | Comfy dtype     | 설명                                                              |
| ---------------- | --------------- | ----------------------------------------------------------------- |
| `model`          | `MODEL`         | 수정이 적용될 기본 모델로, LoRA 조정이 적용됩니다.                |
| `lora_name`      | `COMBO[STRING]` | 로드할 LoRA 파일의 이름으로, 모델에 적용할 조정을 지정합니다.     |
| `strength_model` | `FLOAT`         | LoRA 조정의 강도를 결정하며, 높은 값은 더 강한 수정을 나타냅니다. |

## 출력

| 필드    | 데이터 유형 | 설명                                                                        |
| ------- | ----------- | --------------------------------------------------------------------------- |
| `model` | `MODEL`     | LoRA 조정이 적용된 수정된 모델로, 모델의 동작이나 기능의 변화를 반영합니다. |
