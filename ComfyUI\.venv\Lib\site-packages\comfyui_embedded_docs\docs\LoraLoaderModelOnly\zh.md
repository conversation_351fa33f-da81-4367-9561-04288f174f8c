该节点会检测位于 `ComfyUI/models/loras` 文件夹下的模型，
同时也会读取你在 extra_model_paths.yaml 文件中配置的额外路径的模型，
有时你可能需要 **刷新 ComfyUI 界面** 才能让它读取到对应文件夹下的模型文件

此节点专门用于加载LoRA模型，而无需CLIP模型，专注于根据LoRA参数增强或修改给定模型。它允许通过LoRA参数动态调整模型的强度，从而对模型的行为进行微调控制。

## 输入

| 参数名称 | 数据类型 | 作用 |
| --- | --- | --- |
| `model` | MODEL | 应用LoRA调整的模型。它作为修改的基础。 |
| `lora_name` | COMBO[STRING] | 要加载的LoRA文件的名称。这指定了要应用于模型的LoRA调整。 |
| `strength_model` | FLOAT | 确定应用于模型的 LoRA 的强度。较高的值表示更显著的修改。 |

## 输出

| 参数名称 | 数据类型 | 作用 |
| --- | --- | --- |
| `model` | MODEL | 应用了LoRA调整的修改后的模型，反映了模型行为或能力的变化。 |
