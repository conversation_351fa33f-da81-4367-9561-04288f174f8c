このノードは、LoRAフォルダ（サブフォルダを含む）内のモデルを自動的に検出します。対応するモデルパスは`ComfyUI\models\loras`です。

LoRAローダーノードは主にLoRAモデルをロードするために使用されます。LoRAモデルはフィルターのようなものと考えることができ、画像に特定のスタイル、コンテンツ、詳細を与えることができます：

- 特定のアートスタイル（水墨画など）を適用
- 特定のキャラクター（ゲームキャラクターなど）の特徴を追加
- 画像に特定の詳細を追加
これらはすべてLoRAを通じて実現できます。

複数のLoRAモデルをロードする必要がある場合は、以下のように複数のノードを直接連結することができます：

## 入力

| パラメータ名 | データタイプ | 機能 |
| --- | --- | --- |
| `model` | MODEL | 通常、基本モデルに接続するために使用 |
| `clip` | CLIP | 通常、CLIPモデルに接続するために使用 |
| `lora_name` | COMBO[STRING] | 使用するLoRAモデルの名前を選択 |
| `strength_model` | FLOAT | 値の範囲は-100.0から100.0で、日常的な画像生成では通常0~1の間で使用。値が大きいほど、モデル調整の効果が顕著になります |
| `strength_clip` | FLOAT | 値の範囲は-100.0から100.0で、日常的な画像生成では通常0~1の間で使用。値が大きいほど、モデル調整の効果が顕著になります |

## 出力

| パラメータ名 | データタイプ | 機能 |
| --- | --- | --- |
| `model` | MODEL | LoRA調整が適用されたモデル |
| `clip` | CLIP | LoRA調整が適用されたCLIPインスタンス |
