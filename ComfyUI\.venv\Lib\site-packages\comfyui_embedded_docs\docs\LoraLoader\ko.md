이 노드는 LoRA 폴더(하위 폴더 포함) 내의 모델을 자동으로 감지합니다. 해당 모델 경로는 `ComfyUI\models\loras`입니다.

LoRA 로더 노드는 주로 LoRA 모델을 로드하는 데 사용됩니다. LoRA 모델은 필터와 같은 것으로 생각할 수 있으며, 이미지에 특정 스타일, 콘텐츠, 세부 사항을 부여할 수 있습니다:

- 특정 아트 스타일(수묵화 등) 적용
- 특정 캐릭터(게임 캐릭터 등)의 특징 추가
- 이미지에 특정 세부 사항 추가
이 모든 것은 LoRA를 통해 실현할 수 있습니다.

여러 LoRA 모델을 로드해야 하는 경우 아래와 같이 여러 노드를 직접 연결할 수 있습니다:

## 입력

| 매개변수 이름 | 데이터 유형 | 기능 |
| --- | --- | --- |
| `model` | MODEL | 일반적으로 기본 모델에 연결하는 데 사용 |
| `clip` | CLIP | 일반적으로 CLIP 모델에 연결하는 데 사용 |
| `lora_name` | COMBO[STRING] | 사용할 LoRA 모델의 이름 선택 |
| `strength_model` | FLOAT | 값 범위는 -100.0에서 100.0이며, 일상적인 이미지 생성에서는 보통 0~1 사이에서 사용. 값이 클수록 모델 조정의 효과가 더 두드러집니다 |
| `strength_clip` | FLOAT | 값 범위는 -100.0에서 100.0이며, 일상적인 이미지 생성에서는 보통 0~1 사이에서 사용. 값이 클수록 모델 조정의 효과가 더 두드러집니다 |

## 출력

| 매개변수 이름 | 데이터 유형 | 기능 |
| --- | --- | --- |
| `model` | MODEL | LoRA 조정이 적용된 모델 |
| `clip` | CLIP | LoRA 조정이 적용된 CLIP 인스턴스 |
