这个节点会自动检测位于 LoRA 文件夹下的模型(包括子文件夹)对应模型路径为 `ComfyUI\models\loras`

LoRA加载器节点主要用于加载LoRA模型，你可以简单把 LoRA 模型理解为滤镜，它可以让你的图片具有特定的风格、内容、细节等。

- 使画面具有特定的画风（如水墨画）
- 使人物具有某类人物的特征（如某些游戏角色）
- 使画面具有特定的细节
以上这些都可以通过LoRA来实现。

如果需要加载 多个LoRA 模型，你可以直接将多个节点进行一个串联, 如下图所示

## 输入

| 参数名称 | 数据类型 | 作用 |
| --- | --- | --- |
| `model` | MODEL | 通常用于连接基础模型 |
| `clip` | CLIP | 通常用于连接CLIP模型 |
| `lora_name` | COMBO[STRING] | 选择要使用的LoRA模型名称 |
| `strength_model` | FLOAT |  取值范围 -100.0 到 100.0 ,日常使用用于图片生成时我们通常使用0~1之间, 取值越大, 应用的这个模型调整的效果越明显 |
| `strength_clip` | FLOAT | 取值范围 -100.0 到 100.0 ,日常使用用于图片生成时我们通常使用0~1之间, 取值越大, 应用的这个模型调整的效果越明显 |

## 输出

| 参数名称 | 数据类型 | 作用 |
| --- | --- | --- |
| `model` | MODEL | 应用了LoRA调整的模型 |
| `clip` | CLIP | 应用了LoRA调整的CLIP实例 |
