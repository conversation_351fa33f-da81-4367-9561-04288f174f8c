
このノードは、加算、減算、論理演算などのさまざまな操作を通じて2つのマスク入力を組み合わせ、新しい修正されたマスクを生成することを専門としています。これは、マスクデータの操作を抽象的に処理し、複雑なマスキング効果を達成するための重要なコンポーネントとして、マスクベースの画像編集および処理ワークフローで機能します。

## 入力

| パラメータ    | Data Type | 説明                                                                                                                                      |
| ------------ | ------------ | ------------------------------------------------------------------------------------------------------------------------------------------------ |
| `destination`| MASK        | ソースマスクとの操作に基づいて修正される主要なマスクです。合成操作の中心的な役割を果たし、修正の基盤として機能します。 |
| `source`     | MASK        | 指定された操作を実行するために、destinationマスクと組み合わせて使用される二次マスクで、最終的な出力マスクに影響を与えます。 |
| `x`          | INT         | ソースマスクがdestinationマスクに適用される水平オフセットで、合成結果の位置に影響を与えます。       |
| `y`          | INT         | ソースマスクがdestinationマスクに適用される垂直オフセットで、合成結果の位置に影響を与えます。         |
| `operation`  | COMBO[STRING]| destinationマスクとsourceマスクの間に適用する操作の種類を指定します。例えば「加算」、「減算」、または論理演算などで、合成効果の性質を決定します。 |

## 出力

| パラメータ | Data Type | 説明                                                                 |
| --------- | ------------ | ---------------------------------------------------------------------------- |
| `mask`    | MASK        | destinationマスクとsourceマスクの間に指定された操作を適用した後の結果マスクで、合成結果を表します。 |
