
이 노드는 두 개의 마스크 입력을 더하기, 빼기 및 논리 연산과 같은 다양한 작업을 통해 결합하여 새로운 수정된 마스크를 생성하는 데 특화되어 있습니다. 이 노드는 복잡한 마스킹 효과를 달성하기 위해 마스크 데이터를 추상적으로 처리하며, 마스크 기반 이미지 편집 및 처리 워크플로우에서 중요한 구성 요소로 작용합니다.

## 입력

| 매개변수      | 데이터 유형   | 설명                                                                                                                           |
| ------------- | ------------- | ------------------------------------------------------------------------------------------------------------------------------ |
| `destination` | MASK          | 소스 마스크와의 작업을 기반으로 수정될 기본 마스크입니다. 합성 작업에서 중심적인 역할을 하며, 수정의 기반으로 작용합니다.      |
| `source`      | MASK          | 지정된 작업을 수행하기 위해 대상 마스크와 함께 사용될 보조 마스크로, 최종 출력 마스크에 영향을 미칩니다.                       |
| `x`           | INT           | 소스 마스크가 대상 마스크에 적용될 수평 오프셋으로, 합성 결과의 위치에 영향을 미칩니다.                                        |
| `y`           | INT           | 소스 마스크가 대상 마스크에 적용될 수직 오프셋으로, 합성 결과의 위치에 영향을 미칩니다.                                        |
| `operation`   | COMBO[STRING] | 대상 및 소스 마스크 간에 적용할 작업의 유형을 지정하며, 'add', 'subtract' 또는 논리 연산과 같은 합성 효과의 성격을 결정합니다. |

## 출력

| 매개변수 | 데이터 유형 | 설명                                                                                      |
| -------- | ----------- | ----------------------------------------------------------------------------------------- |
| `mask`   | MASK        | 대상 및 소스 마스크 간에 지정된 작업을 적용한 후의 결과 마스크로, 합성 결과를 나타냅니다. |
