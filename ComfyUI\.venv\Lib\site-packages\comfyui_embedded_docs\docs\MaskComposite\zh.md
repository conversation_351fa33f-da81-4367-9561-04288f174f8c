遮罩组合节点专门通过各种操作（如加法、减法和逻辑操作）结合两个遮罩输入，以产生一个新的、修改后的遮罩。它抽象地处理遮罩数据的操纵，以实现复杂的遮罩效果，是遮罩基础的图像编辑和处理工作流程中的关键组件。

## 输入

| 参数名称 | 数据类型 | 作用 |
| --- | --- | --- |
| `destination` | MASK | 主要遮罩，将根据与源遮罩的操作进行修改。在组合操作中扮演中心角色，作为修改的基础。 |
| `source` | MASK | 次要遮罩，将与目标遮罩结合使用以执行指定操作，影响最终输出的遮罩。 |
| `x` | INT | 水平偏移量，用于确定源遮罩应用于目标遮罩的位置，影响合成结果的位置。 |
| `y` | INT | 垂直偏移量，用于确定源遮罩应用于目标遮罩的位置，影响合成结果的位置。 |
| `operation` | COMBO[STRING] | 指定要在目标遮罩和源遮罩之间应用的操作类型，如'add'（加法）、'subtract'（减法）或逻辑操作，决定合成效果的性质。 |

## 输出

| 参数名称 | 数据类型 | 作用 |
| --- | --- | --- |
| `mask` | MASK | 在目标遮罩和源遮罩之间应用指定操作后得到的遮罩，代表合成的结果。 |

---
