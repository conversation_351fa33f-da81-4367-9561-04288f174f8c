
`MaskToImage`ノードは、マスクを画像形式に変換するために設計されています。この変換により、マスクを画像として視覚化し、さらなる処理が可能になります。これにより、マスクベースの操作と画像ベースのアプリケーションの間の橋渡しが可能になります。

## 入力

| パラメータ | データ型 | 説明 |
|-----------|-------------|-------------|
| `mask`    | `MASK`      | マスク入力は変換プロセスに不可欠であり、画像形式に変換されるソースデータとして機能します。この入力は、結果として得られる画像の形状と内容を決定します。 |

## 出力

| パラメータ | データ型 | 説明 |
|-----------|-------------|-------------|
| `image`   | `IMAGE`     | 出力は入力マスクの画像表現であり、視覚的な検査やさらなる画像ベースの操作を可能にします。 |
