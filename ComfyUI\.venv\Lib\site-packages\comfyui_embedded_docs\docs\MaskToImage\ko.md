
`MaskToImage` 노드는 마스크를 이미지 형식으로 변환하도록 설계되었습니다. 이 변환은 마스크를 이미지로 시각화하고 추가 처리를 가능하게 하여, 마스크 기반 작업과 이미지 기반 응용 프로그램 간의 다리를 제공합니다.

## 입력

| 매개변수 | 데이터 유형 | 설명                                                                                                                                       |
| -------- | ----------- | ------------------------------------------------------------------------------------------------------------------------------------------ |
| `mask`   | `MASK`      | 마스크 입력은 변환 과정에서 필수적이며, 이미지 형식으로 변환될 소스 데이터를 제공합니다. 이 입력은 결과 이미지의 형태와 내용을 결정합니다. |

## 출력

| 매개변수 | 데이터 유형 | 설명                                                                                         |
| -------- | ----------- | -------------------------------------------------------------------------------------------- |
| `image`  | `IMAGE`     | 출력은 입력 마스크의 이미지 표현으로, 시각적 검사와 추가 이미지 기반 조작을 가능하게 합니다. |
