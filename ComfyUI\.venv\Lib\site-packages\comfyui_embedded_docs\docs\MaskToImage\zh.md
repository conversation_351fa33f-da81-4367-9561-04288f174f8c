
MaskToImage 节点旨在将遮罩转换为图像格式。这种转换允许将遮罩作为图像进行可视化和进一步处理，从而在基于遮罩的操作和基于图像的应用程序之间架起了一座桥梁。

## 输入

| 参数名称 | 数据类型 | 作用                                                         |
|----------|----------|--------------------------------------------------------------|
| `mask`   | `MASK`   | 遮罩输入对于转换过程至关重要，作为将被转换成图像格式的源数据。此输入决定了结果图像的形状和内容。 |

## 输出

| 参数名称 | 数据类型 | 作用                                                         |
|----------|----------|--------------------------------------------------------------|
| `image`  | `IMAGE`  | 输出是输入遮罩的图像表示，使视觉检查和进一步的基于图像的操作成为可能。 |
