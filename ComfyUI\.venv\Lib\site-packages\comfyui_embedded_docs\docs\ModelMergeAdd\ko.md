ModelMergeAdd 노드는 한 모델에서 다른 모델로 주요 패치를 추가하여 두 모델을 병합하도록 설계되었습니다. 이 과정은 첫 번째 모델을 복제한 후 두 번째 모델에서 패치를 적용하여 두 모델의 기능이나 동작을 결합할 수 있게 합니다.

## 입력

| 매개변수 | 데이터 유형 | 설명                                                                                                            |
| -------- | ----------- | --------------------------------------------------------------------------------------------------------------- |
| `model1` | `MODEL`     | 복제될 첫 번째 모델로, 두 번째 모델에서 패치가 추가됩니다. 병합 과정의 기본 모델로 사용됩니다.                  |
| `model2` | `MODEL`     | 주요 패치가 추출되어 첫 번째 모델에 추가되는 두 번째 모델입니다. 병합된 모델에 추가 기능이나 동작을 제공합니다. |

## 출력

| 매개변수 | 데이터 유형 | 설명                                                                                                                                     |
| -------- | ----------- | ---------------------------------------------------------------------------------------------------------------------------------------- |
| `model`  | MODEL       | 두 번째 모델에서 첫 번째 모델로 주요 패치를 추가하여 두 모델을 병합한 결과입니다. 이 병합된 모델은 두 모델의 기능이나 동작을 결합합니다. |
