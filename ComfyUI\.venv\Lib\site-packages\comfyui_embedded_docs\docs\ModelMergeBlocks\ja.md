
ModelMergeBlocksは、異なるモデルの部分に対してカスタマイズ可能なブレンド比率を設定し、2つのモデルを統合するために設計されています。指定されたパラメータに基づいて、2つのソースモデルのコンポーネントを選択的にマージすることで、ハイブリッドモデルの作成を容易にします。

## 入力

| パラメータ | データ型 | 説明 |
|-----------|-------------|-------------|
| `model1`  | `MODEL`     | マージされる最初のモデルです。2番目のモデルからのパッチが適用されるベースモデルとして機能します。 |
| `model2`  | `MODEL`     | 指定されたブレンド比率に基づいて、パッチが抽出され最初のモデルに適用される2番目のモデルです。 |
| `input`   | `FLOAT`     | モデルの入力層に対するブレンド比率を指定します。2番目のモデルの入力層がどの程度最初のモデルにマージされるかを決定します。 |
| `middle`  | `FLOAT`     | モデルの中間層に対するブレンド比率を定義します。このパラメータは、モデルの中間層の統合レベルを制御します。 |
| `out`     | `FLOAT`     | モデルの出力層に対するブレンド比率を決定します。2番目のモデルの出力層の寄与を調整することで、最終出力に影響を与えます。 |

## 出力

| パラメータ | データ型 | 説明 |
|-----------|-------------|-------------|
| `model`   | MODEL     | 指定されたブレンド比率に従ってパッチが適用された、2つの入力モデルのハイブリッドである結果のマージモデルです。 |
