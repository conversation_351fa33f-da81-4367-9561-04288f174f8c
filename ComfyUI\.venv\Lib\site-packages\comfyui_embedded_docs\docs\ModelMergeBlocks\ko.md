
ModelMergeBlocks는 고급 모델 병합 작업을 위해 설계되었으며, 모델의 다양한 부분에 대해 사용자 지정 가능한 블렌딩 비율을 통해 두 모델을 통합할 수 있습니다. 이 노드는 지정된 매개변수에 따라 두 소스 모델의 구성 요소를 선택적으로 병합하여 하이브리드 모델을 생성하는 데 도움을 줍니다.

## 입력

| 매개변수 | 데이터 유형 | 설명                                                                                                                            |
| -------- | ----------- | ------------------------------------------------------------------------------------------------------------------------------- |
| `model1` | `MODEL`     | 병합될 첫 번째 모델입니다. 두 번째 모델에서 패치가 적용되는 기본 모델로 사용됩니다.                                             |
| `model2` | `MODEL`     | 지정된 블렌딩 비율에 따라 첫 번째 모델에 적용될 패치가 추출되는 두 번째 모델입니다.                                             |
| `input`  | `FLOAT`     | 모델의 입력 레이어에 대한 블렌딩 비율을 지정합니다. 두 번째 모델의 입력 레이어가 첫 번째 모델에 얼마나 병합되는지를 결정합니다. |
| `middle` | `FLOAT`     | 모델의 중간 레이어에 대한 블렌딩 비율을 정의합니다. 이 매개변수는 모델의 중간 레이어 통합 수준을 제어합니다.                    |
| `out`    | `FLOAT`     | 모델의 출력 레이어에 대한 블렌딩 비율을 결정합니다. 두 번째 모델의 출력 레이어 기여도를 조정하여 최종 출력을 영향을 미칩니다.   |

## 출력

| 매개변수 | 데이터 유형 | 설명                                                                                      |
| -------- | ----------- | ----------------------------------------------------------------------------------------- |
| `model`  | MODEL       | 지정된 블렌딩 비율에 따라 패치가 적용된 두 입력 모델의 하이브리드인 결과 병합 모델입니다. |
