
融合模型（分层）节点旨在进行高级的模型融合操作，它允许将两个模型通过可定制的混合比例进行整合，以实现不同模型部分的融合。该节点通过基于指定参数选择性地合并两个源模型的组件，从而方便创建混合模型。

## 输入

| 参数名称 | 数据类型 | 作用 |
| --- | --- | --- |
| `model1` | `MODEL` | 要合并的第一个模型。它作为基础模型，在其上应用第二个模型的补丁。 |
| `model2` | `MODEL` | 提取补丁并将其应用于第一个模型的第二个模型，基于指定的混合比例。 |
| `input` | `FLOAT` | 指定模型输入层的混合比例。它决定了第二个模型输入层融合到第一个模型中的程度。 |
| `middle` | `FLOAT` | 定义模型中间层的混合比例。此参数控制模型中间层的整合程度。 |
| `out` | `FLOAT` | 确定模型输出层的混合比例。它通过调整第二个模型输出层的贡献来影响最终输出。 |

## 输出

| 参数名称 | 数据类型 | 作用 |
| --- | --- | --- |
| `model` | `MODEL` | 结果融合的模型，它是两个输入模型的混合体，根据指定的混合比例应用补丁。 |
