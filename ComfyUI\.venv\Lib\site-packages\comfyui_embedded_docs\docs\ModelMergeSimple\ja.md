
ModelMergeSimpleノードは、指定された比率に基づいて2つのモデルのパラメータをブレンドすることで、モデルをマージするために設計されています。このノードは、両方の入力モデルの強みや特性を組み合わせたハイブリッドモデルの作成を容易にします。

`ratio`パラメータは、2つのモデル間のブレンド比率を決定します。この値が1の場合、出力モデルは100% `model1` であり、この値が0の場合、出力モデルは100% `model2` です。

## 入力

| パラメータ | データ型 | 説明 |
|-----------|-------------|-------------|
| `model1`  | `MODEL`     | マージされる最初のモデルです。これは、2番目のモデルからのパッチが適用されるベースモデルとして機能します。 |
| `model2`  | `MODEL`     | 指定された比率に影響されて、最初のモデルにパッチが適用される2番目のモデルです。 |
| `ratio`   | `FLOAT`     | この値が1の場合、出力モデルは100% `model1` であり、この値が0の場合、出力モデルは100% `model2` です。 |

## 出力

| パラメータ | データ型 | 説明 |
|-----------|-------------|-------------|
| `model`   | MODEL     | 指定された比率に従って、両方の入力モデルから要素を取り入れた結果のマージされたモデルです。 |
