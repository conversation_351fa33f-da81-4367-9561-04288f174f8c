
ModelMergeSimple 노드는 지정된 비율에 따라 두 모델의 매개변수를 혼합하여 병합하도록 설계되었습니다. 이 노드는 두 입력 모델의 강점이나 특성을 결합한 하이브리드 모델을 생성하는 데 도움을 줍니다.

`ratio` 매개변수는 두 모델 간의 혼합 비율을 결정합니다. 이 값이 1일 때 출력 모델은 100% `model1`이며, 이 값이 0일 때 출력 모델은 100% `model2`입니다.

## 입력

| 매개변수 | 데이터 유형 | 설명                                                                                          |
| -------- | ----------- | --------------------------------------------------------------------------------------------- |
| `model1` | `MODEL`     | 병합할 첫 번째 모델입니다. 두 번째 모델의 패치가 적용되는 기본 모델로 사용됩니다.             |
| `model2` | `MODEL`     | 지정된 비율에 따라 첫 번째 모델에 패치가 적용되는 두 번째 모델입니다.                         |
| `ratio`  | `FLOAT`     | 이 값이 1일 때 출력 모델은 100% `model1`이며, 이 값이 0일 때 출력 모델은 100% `model2`입니다. |

## 출력

| 매개변수 | 데이터 유형 | 설명                                                                  |
| -------- | ----------- | --------------------------------------------------------------------- |
| `model`  | MODEL       | 지정된 비율에 따라 두 입력 모델의 요소를 통합한 결과 병합 모델입니다. |
