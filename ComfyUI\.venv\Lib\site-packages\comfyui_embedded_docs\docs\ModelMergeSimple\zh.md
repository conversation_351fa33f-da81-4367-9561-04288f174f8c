
这个节点的作用主要是将两个合并成一个模型作为后续的绘图模型输出，当你想使用两个模型的时候，你想要融合他们的特征作为后续的输出时，这个节点就非常有用。
其中 `ratio` 参数的作用是决定融合的比例，当这个值为 1 时，则输出的模型为 100% 的`model1`,当这个值为 0 时，则输出的模型为100% 的  `model2`

## 输入

| 参数名称 | 数据类型 | 作用 |
| --- | --- | --- |
| `model1` | `MODEL` | 要合并的第一个模型。它作为基础模型，在其上应用第二个模型的补丁。 |
| `model2` | `MODEL` | 应用其补丁到第一个模型的第二个模型，受指定比例的影响。 |
| `ratio`   | `FLOAT`  | 当这个值为 1 时，则输出的模型为 100% 的`model1`,当这个值为 0 时，则输出的模型为100% 的  `model2` |

## 输出

| 参数名称 | 数据类型 | 作用 |
| --- | --- | --- |
| `model`  | MODEL    | 结果融合的模型，根据指定比例整合了两个输入模型的元素。 |
