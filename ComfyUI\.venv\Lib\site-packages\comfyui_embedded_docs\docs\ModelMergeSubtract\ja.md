
このノードは、高度なモデルマージ操作のために設計されており、指定された乗数に基づいてあるモデルのパラメータを別のモデルから減算します。これにより、あるモデルのパラメータが別のモデルに与える影響を調整することで、モデルの動作をカスタマイズし、新しいハイブリッドモデルの作成を可能にします。

## 入力

| パラメータ     | データ型 | 説明 |
|---------------|--------------|-------------|
| `model1`      | `MODEL`     | パラメータが減算されるベースモデルです。 |
| `model2`      | `MODEL`     | ベースモデルから減算されるパラメータを持つモデルです。 |
| `multiplier`  | `FLOAT`     | ベースモデルのパラメータに対する減算効果をスケールする浮動小数点値です。 |

## 出力

| パラメータ | データ型 | 説明 |
|-----------|-------------|-------------|
| `model`   | MODEL     | あるモデルのパラメータを別のモデルから減算し、乗数でスケールされた結果のモデルです。 |
