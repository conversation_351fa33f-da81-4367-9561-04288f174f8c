
このノードは、連続EDM（エネルギーベースの拡散モデル）サンプリング技術を統合することで、モデルのサンプリング能力を強化するように設計されています。これにより、モデルのサンプリングプロセス内でノイズレベルを動的に調整でき、生成品質と多様性をより精密に制御できます。

## 入力

| パラメータ   | Data Type | Python dtype        | 説明 |
|-------------|--------------|----------------------|-------------|
| `model`     | `MODEL`     | `torch.nn.Module`   | 連続EDMサンプリング能力を強化するためのモデルです。これは、高度なサンプリング技術を適用するための基盤となります。 |
| `sampling`  | COMBO[STRING] | `str`             | 適用されるサンプリングのタイプを指定します。「eps」はイプシロンサンプリング、「v_prediction」は速度予測を意味し、サンプリングプロセス中のモデルの動作に影響を与えます。 |
| `sigma_max` | `FLOAT`     | `float`             | ノイズレベルの最大シグマ値で、サンプリング中のノイズ注入プロセスにおける上限を制御します。 |
| `sigma_min` | `FLOAT`     | `float`             | ノイズレベルの最小シグマ値で、ノイズ注入の下限を設定し、モデルのサンプリング精度に影響を与えます。 |

## 出力

| パラメータ | Data Type | Python dtype        | 説明 |
|-----------|-------------|----------------------|-------------|
| `model`   | MODEL     | `torch.nn.Module`   | 連続EDMサンプリング能力を統合した強化モデルで、生成タスクでのさらなる使用に準備が整っています。 |
