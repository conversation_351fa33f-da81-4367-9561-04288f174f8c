
이 노드는 연속 EDM(Energy-based Diffusion Models) 샘플링 기법을 통합하여 모델의 샘플링 기능을 향상시키도록 설계되었습니다. 모델의 샘플링 과정에서 노이즈 레벨을 동적으로 조정할 수 있어, 생성 품질과 다양성에 대한 보다 정교한 제어를 제공합니다.

## 입력

| 매개변수    | 데이터 유형   | Python dtype      | 설명                                                                                                                                          |
| ----------- | ------------- | ----------------- | --------------------------------------------------------------------------------------------------------------------------------------------- |
| `model`     | `MODEL`       | `torch.nn.Module` | 연속 EDM 샘플링 기능이 향상된 모델입니다. 이는 고급 샘플링 기법을 적용하는 기초가 됩니다.                                                     |
| `sampling`  | COMBO[STRING] | `str`             | 적용할 샘플링 유형을 지정합니다. 'eps'는 엡실론 샘플링을, 'v_prediction'은 속도 예측을 의미하며, 샘플링 과정에서 모델의 동작에 영향을 줍니다. |
| `sigma_max` | `FLOAT`       | `float`           | 노이즈 레벨의 최대 시그마 값으로, 샘플링 중 노이즈 주입 과정에서 상한선을 제어할 수 있습니다.                                                 |
| `sigma_min` | `FLOAT`       | `float`           | 노이즈 레벨의 최소 시그마 값으로, 노이즈 주입의 하한선을 설정하여 모델의 샘플링 정밀도에 영향을 줍니다.                                       |

## 출력

| 매개변수 | 데이터 유형 | Python dtype      | 설명                                                                                             |
| -------- | ----------- | ----------------- | ------------------------------------------------------------------------------------------------ |
| `model`  | MODEL       | `torch.nn.Module` | 연속 EDM 샘플링 기능이 통합된 향상된 모델로, 생성 작업에 추가적으로 사용할 준비가 되어 있습니다. |
