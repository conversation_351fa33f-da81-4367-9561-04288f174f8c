
このノードは、モデルのサンプリング動作を離散サンプリング戦略を適用することで修正するように設計されています。epsilon、v_prediction、lcm、x0などの異なるサンプリング方法を選択でき、オプションでゼロショットノイズ比（zsnr）設定に基づいてモデルのノイズ削減戦略を調整します。

## 入力

| パラメータ | Data Type | Python dtype     | 説明 |
|-----------|--------------|-------------------|-------------|
| `model`   | MODEL     | `torch.nn.Module` | 離散サンプリング戦略が適用されるモデル。このパラメータは、変更を受ける基本モデルを定義するために重要です。 |
| `sampling`| COMBO[STRING] | `str`           | モデルに適用される離散サンプリング方法を指定します。方法の選択は、モデルがサンプルを生成する方法に影響を与え、異なるサンプリング戦略を提供します。 |
| `zsnr`    | `BOOLEAN`   | `bool`           | 有効にすると、ゼロショットノイズ比に基づいてモデルのノイズ削減戦略を調整するブールフラグです。これにより、生成されるサンプルの品質と特性に影響を与えることができます。 |

## 出力

| パラメータ | Data Type | Python dtype     | 説明 |
|-----------|-------------|-------------------|-------------|
| `model`   | MODEL     | `torch.nn.Module` | 適用された離散サンプリング戦略を持つ修正済みモデル。このモデルは、指定された方法と調整を使用してサンプルを生成する準備が整っています。 |
