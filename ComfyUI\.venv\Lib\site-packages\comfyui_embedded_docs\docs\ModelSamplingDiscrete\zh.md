
该节点旨在通过应用离散采样策略来修改模型的采样行为。它允许选择不同的采样方法，如 epsilon、v_prediction、lcm 或 x0，并可选地根据零样本噪声比率（zsnr）设置调整模型的噪声降低策略。

## 输入

| 参数名称 | 数据类型 | 作用                                                         |
| -------- | -------- | ------------------------------------------------------------ |
| `model`  | MODEL    | 将应用离散采样策略的模型。此参数定义了将进行修改的基础模型。 |
| `sampling` | COMBO[STRING] | 指定要应用于模型的离散采样方法。选择的方法影响模型如何生成样本，提供了不同的采样策略。 |
| `zsnr`    | `BOOLEAN` | 一个布尔标志，启用时根据零样本噪声比率调整模型的噪声减少策略。这可以影响生成样本的质量和特性。 |

## 输出

| 参数名称 | 数据类型 | 作用                                                         |
| -------- | -------- | ------------------------------------------------------------ |
| `model`  | MODEL    | 修改后的模型，应用了离散采样策略。现在该模型可以使用指定的方法和调整来生成样本。 |
