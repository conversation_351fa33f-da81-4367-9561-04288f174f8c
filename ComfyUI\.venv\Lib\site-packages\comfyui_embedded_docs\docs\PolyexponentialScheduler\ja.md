
PolyexponentialSchedulerノードは、多項指数ノイズスケジュールに基づいてノイズレベル（シグマ）のシーケンスを生成するように設計されています。このスケジュールは、シグマの対数における多項式関数であり、拡散プロセス全体を通じてノイズレベルの柔軟でカスタマイズ可能な進行を可能にします。

## 入力

| パラメータ  | Data Type | 説明                                                                                                                             |
| ----------- | ----------- | -------------------------------------------------------------------------------------------------------------------------------- |
| `steps`     | INT         | 拡散プロセスにおけるステップ数を指定し、生成されるノイズレベルの粒度に影響を与えます。                                           |
| `sigma_max` | FLOAT       | ノイズスケジュールの上限を設定する最大ノイズレベル。                                                                             |
| `sigma_min` | FLOAT       | ノイズスケジュールの下限を設定する最小ノイズレベル。                                                                             |
| `rho`       | FLOAT       | 多項指数ノイズスケジュールの形状を制御するパラメータで、最小値と最大値の間でノイズレベルがどのように進行するかに影響を与えます。 |

## 出力

| パラメータ | Data Type | 説明                                                                                           |
| ---------- | ----------- | ---------------------------------------------------------------------------------------------- |
| `sigmas`   | SIGMAS      | 指定された多項指数ノイズスケジュールに合わせたノイズレベル（シグマ）のシーケンスを出力します。 |
