
`PolyexponentialScheduler` 노드는 폴리익스포넨셜 노이즈 스케줄을 기반으로 시그마(σ) 값의 시퀀스를 생성하도록 설계되었습니다. 이 스케줄은 시그마의 로그에 대한 다항식 함수로, 확산 과정에서 노이즈 레벨의 유연하고 맞춤형 진행을 가능하게 합니다.

## 입력

| 매개변수    | 데이터 유형 | 설명                                                                                                                |
| ----------- | ----------- | ------------------------------------------------------------------------------------------------------------------- |
| `steps`     | INT         | 확산 과정에서의 단계 수를 지정하여 생성된 노이즈 레벨의 세분화에 영향을 미칩니다.                                   |
| `sigma_max` | FLOAT       | 노이즈 스케줄의 최대 노이즈 레벨을 설정하여 상한선을 정합니다.                                                      |
| `sigma_min` | FLOAT       | 노이즈 스케줄의 최소 노이즈 레벨을 설정하여 하한선을 정합니다.                                                      |
| `rho`       | FLOAT       | 폴리익스포넨셜 노이즈 스케줄의 형태를 제어하는 매개변수로, 최소값과 최대값 사이의 노이즈 레벨 진행에 영향을 줍니다. |

## 출력

| 매개변수 | 데이터 유형 | 설명                                                                           |
| -------- | ----------- | ------------------------------------------------------------------------------ |
| `sigmas` | SIGMAS      | 지정된 폴리익스포넨셜 노이즈 스케줄에 맞춘 노이즈 레벨(시그마)의 시퀀스입니다. |
