
多项式指数调度器（PolyexponentialScheduler）节点旨在基于一个多项式指数噪声计划生成一系列噪声水平（sigmas）。该计划是sigma对数的多项式函数，允许在扩散过程中灵活且可定制地调整噪声水平的进展。

## 输入

| 参数名称   | 数据类型 | 作用                                                         |
|------------|----------|--------------------------------------------------------------|
| `steps`    | `INT`    | 指定扩散过程中的步骤数，影响生成噪声水平的粒度。           |
| `sigma_max`| `FLOAT`  | 最大噪声水平，设定噪声计划的上限。                         |
| `sigma_min`| `FLOAT`  | 最小噪声水平，设定噪声计划的下限。                         |
| `rho`      | `FLOAT`  | 控制多项式指数噪声计划形状的参数，影响噪声水平在最小值和最大值之间的进展。 |

## 输出

| 参数名称 | 数据类型 | 作用                                                         |
|----------|----------|--------------------------------------------------------------|
| `sigmas` | `SIGMAS`| 输出是按照指定的多项式指数噪声计划定制的一系列噪声水平。 |

---
