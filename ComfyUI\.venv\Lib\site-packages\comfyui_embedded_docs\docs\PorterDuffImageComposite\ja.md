
PorterDuffImageCompositeノードは、Porter-Duff合成オペレーターを使用して画像合成を行うように設計されています。さまざまなブレンドモードに従ってソース画像と宛先画像を組み合わせることができ、画像の透明度を操作し、創造的な方法で画像を重ね合わせることにより、複雑な視覚効果を作成できます。

## 入力

| パラメータ | データ型 | 説明 |
| --------- | ------------ | ----------- |
| `source`  | `IMAGE`     | 宛先画像の上に合成されるソース画像テンソルです。選択された合成モードに基づいて最終的な視覚的結果を決定する上で重要な役割を果たします。 |
| `source_alpha` | `MASK` | ソース画像のアルファチャンネルで、ソース画像の各ピクセルの透明度を指定します。ソース画像が宛先画像とどのようにブレンドされるかに影響を与えます。 |
| `destination` | `IMAGE` | ソース画像が合成される背景として機能する宛先画像テンソルです。ブレンドモードに基づいて最終的な合成画像に寄与します。 |
| `destination_alpha` | `MASK` | 宛先画像のアルファチャンネルで、宛先画像のピクセルの透明度を定義します。ソース画像と宛先画像のブレンドに影響を与えます。 |
| `mode` | COMBO[STRING] | 適用するPorter-Duff合成モードで、ソース画像と宛先画像がどのようにブレンドされるかを決定します。各モードは異なる視覚効果を生み出します。 |

## 出力

| パラメータ | データ型 | 説明 |
| --------- | ------------ | ----------- |
| `image`   | `IMAGE`     | 指定されたPorter-Duffモードの適用結果として得られる合成画像です。 |
| `mask`    | `MASK`      | 合成画像のアルファチャンネルで、各ピクセルの透明度を示します。 |
