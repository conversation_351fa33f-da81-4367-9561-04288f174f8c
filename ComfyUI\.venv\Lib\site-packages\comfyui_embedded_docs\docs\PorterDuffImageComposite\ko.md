
PorterDuffImageComposite 노드는 <PERSON><PERSON><PERSON> 합성 연산자를 사용하여 이미지 합성을 수행하도록 설계되었습니다. 다양한 블렌딩 모드에 따라 소스 이미지와 대상 이미지를 결합할 수 있으며, 이미지 투명성을 조작하고 창의적인 방식으로 이미지를 겹쳐 복잡한 시각적 효과를 생성할 수 있습니다.

## 입력

| 매개변수            | 데이터 유형   | 설명                                                                                                                                       |
| ------------------- | ------------- | ------------------------------------------------------------------------------------------------------------------------------------------ |
| `source`            | `IMAGE`       | 대상 이미지 위에 합성될 소스 이미지 텐서입니다. 선택한 합성 모드에 따라 최종 시각적 결과를 결정하는 데 중요한 역할을 합니다.               |
| `source_alpha`      | `MASK`        | 소스 이미지의 알파 채널로, 소스 이미지의 각 픽셀의 투명도를 지정합니다. 소스 이미지가 대상 이미지와 어떻게 블렌딩되는지에 영향을 미칩니다. |
| `destination`       | `IMAGE`       | 소스 이미지가 합성될 배경 역할을 하는 대상 이미지 텐서입니다. 블렌딩 모드에 따라 최종 합성 이미지에 기여합니다.                            |
| `destination_alpha` | `MASK`        | 대상 이미지의 알파 채널로, 대상 이미지 픽셀의 투명도를 정의합니다. 소스 이미지와 대상 이미지의 블렌딩에 영향을 미칩니다.                   |
| `mode`              | COMBO[STRING] | 적용할 Porter-Duff 합성 모드로, 소스 이미지와 대상 이미지가 어떻게 블렌딩되는지를 결정합니다. 각 모드는 다른 시각적 효과를 생성합니다.     |

## 출력

| 매개변수 | 데이터 유형 | 설명                                                            |
| -------- | ----------- | --------------------------------------------------------------- |
| `image`  | `IMAGE`     | 지정된 Porter-Duff 모드의 적용 결과로 생성된 합성 이미지입니다. |
| `mask`   | `MASK`      | 합성 이미지의 알파 채널로, 각 픽셀의 투명도를 나타냅니다.       |
