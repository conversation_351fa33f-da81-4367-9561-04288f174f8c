
El nodo PreviewImage está diseñado para crear imágenes de vista previa temporales. Genera automáticamente un nombre de archivo temporal único para cada imagen, comprime la imagen a un nivel especificado y la guarda en un directorio temporal. Esta funcionalidad es particularmente útil para generar vistas previas de imágenes durante el procesamiento sin afectar los archivos originales.

## Entradas

| Parámetro | Data Type | Descripción |
|-----------|-------------|-------------|
| `images`  | `IMAGE`     | La entrada 'images' especifica las imágenes que se procesarán y guardarán como imágenes de vista previa temporales. Esta es la entrada principal para el nodo, determinando qué imágenes pasarán por el proceso de generación de vista previa. |

## Salidas

El nodo no tiene tipos de salida.
