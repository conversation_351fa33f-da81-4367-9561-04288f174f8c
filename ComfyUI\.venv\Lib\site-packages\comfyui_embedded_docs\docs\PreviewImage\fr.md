
Le nœud PreviewImage est conçu pour créer des images d'aperçu temporaires. Il génère automatiquement un nom de fichier temporaire unique pour chaque image, compresse l'image à un niveau spécifié et l'enregistre dans un répertoire temporaire. Cette fonctionnalité est particulièrement utile pour générer des aperçus d'images pendant le traitement sans affecter les fichiers originaux.

## Entrées

| Paramètre | Type de Donnée | Description |
|-----------|-------------|-------------|
| `images`  | `IMAGE`     | L'entrée 'images' spécifie les images à traiter et à enregistrer en tant qu'images d'aperçu temporaires. C'est l'entrée principale pour le nœud, déterminant quelles images subiront le processus de génération d'aperçu. |

## Sorties

Le nœud n'a pas de sortie.
