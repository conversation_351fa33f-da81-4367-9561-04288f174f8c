
PreviewImage 노드는 임시 미리보기 이미지를 생성하도록 설계되었습니다. 각 이미지에 대해 고유한 임시 파일 이름을 자동으로 생성하고, 지정된 수준으로 이미지를 압축하여 임시 디렉토리에 저장합니다. 이 기능은 원본 파일에 영향을 주지 않고 처리 중인 이미지의 미리보기를 생성하는 데 특히 유용합니다.

## 입력

| 매개변수 | 데이터 유형 | 설명                                                                                                                                                  |
| -------- | ----------- | ----------------------------------------------------------------------------------------------------------------------------------------------------- |
| `images` | `IMAGE`     | 'images' 입력은 처리되어 임시 미리보기 이미지로 저장될 이미지를 지정합니다. 이는 노드의 주요 입력으로, 미리보기 생성 과정을 거칠 이미지를 결정합니다. |

## 출력

이 노드는 출력 유형이 없습니다.
