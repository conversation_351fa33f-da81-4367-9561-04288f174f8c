The primitive node can recognize the type of input connected to it and provide input data accordingly. When this node is connected to different input types, it will change to different input states. It can be used to use a unified parameter among multiple different nodes, such as using the same seed in multiple Ksampler.

Currently, the `Primitive Primitive Node` supports the following data types for connection:

- String
- Number (float / Int)

Usage Example:
