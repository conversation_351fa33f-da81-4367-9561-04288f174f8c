El nodo primitive puede reconocer el tipo de entrada conectada a él y proporcionar datos de entrada en consecuencia. Cuando este nodo se conecta a diferentes tipos de entrada, cambiará a diferentes estados de entrada. Se puede usar para utilizar un parámetro unificado entre varios nodos diferentes, como usar la misma semilla en múltiples Ksampler.

Actualmente, el `Primitive Primitive Node` admite los siguientes tipos de datos para la conexión:

- String
- Number (float / Int)

Ejemplo de uso:
