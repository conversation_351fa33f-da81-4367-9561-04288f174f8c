Le nœud primitif peut reconnaître le type d'entrée qui lui est connecté et fournir les données d'entrée en conséquence. Lorsque ce nœud est connecté à différents types d'entrée, il changera d'état d'entrée. Il peut être utilisé pour appliquer un paramètre unifié parmi plusieurs nœuds différents, comme utiliser la même graine dans plusieurs Ksampler.

Actuellement, le `Primitive Primitive Node` prend en charge les types de données suivants pour la connexion :

- String (Chaîne)
- Number (float / Int) (Nombre)

Exemple d'utilisation :
