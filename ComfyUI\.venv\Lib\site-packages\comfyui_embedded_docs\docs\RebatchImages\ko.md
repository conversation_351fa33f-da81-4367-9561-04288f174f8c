
RebatchImages 노드는 이미지 배치를 새로운 배치 구성으로 재조직하며, 지정된 배치 크기로 조정합니다. 이 과정은 배치 작업에서 이미지 데이터를 효율적으로 처리하고 최적화하는 데 필수적이며, 이미지가 원하는 배치 크기에 따라 그룹화되어 효율적으로 처리될 수 있도록 합니다.

## Input types

| Field        | 데이터 유형 | Description                                                                                                                                             |
| ------------ | ----------- | ------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `images`     | `IMAGE`     | 재배치할 이미지 목록입니다. 이 매개변수는 재배치 과정을 거칠 입력 데이터를 결정하는 데 중요합니다.                                                      |
| `batch_size` | `INT`       | 출력 배치의 원하는 크기를 지정합니다. 이 매개변수는 입력 이미지가 어떻게 그룹화되고 처리되는지를 직접적으로 영향을 미치며, 출력의 구조에 영향을 줍니다. |

## Output types

| Field   | 데이터 유형 | Description                                                                                                                                            |
| ------- | ----------- | ------------------------------------------------------------------------------------------------------------------------------------------------------ |
| `image` | `IMAGE`     | 출력은 지정된 배치 크기에 따라 재조직된 이미지 배치 목록으로 구성됩니다. 이는 배치 작업에서 이미지 데이터를 유연하고 효율적으로 처리할 수 있게 합니다. |
