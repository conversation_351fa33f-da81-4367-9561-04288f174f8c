
RebatchLatentsノードは、指定されたバッチサイズに基づいて、潜在表現のバッチを新しいバッチ構成に再編成するように設計されています。これにより、潜在サンプルが適切にグループ化され、次元やサイズの変動を処理し、さらなる処理やモデル推論を容易にします。

## 入力

| パラメータ    | Data Type | 説明 |
|--------------|-------------|-------------|
| `latents`    | `LATENT`    | `latents`パラメータは、再バッチ化される入力潜在表現を表します。出力バッチの構造と内容を決定する上で重要です。 |
| `batch_size` | `INT`      | `batch_size`パラメータは、出力でのバッチごとのサンプル数を指定します。入力潜在の新しいバッチへのグループ化と分割に直接影響を与えます。 |

## 出力

| パラメータ | データ型 | 説明 |
|-----------|-------------|-------------|
| `latent`  | `LATENT`    | 出力は、指定されたバッチサイズに従って調整された潜在表現の再編成されたバッチです。さらなる処理や分析を容易にします。 |
