
RebatchLatents 노드는 지정된 배치 크기에 따라 잠재 표현의 배치를 새로운 구성으로 재조직하도록 설계되었습니다. 이는 잠재 샘플이 적절하게 그룹화되도록 하여, 차원과 크기의 변화를 처리하고 추가 처리나 모델 추론을 용이하게 합니다.

## 입력

| 매개변수     | 데이터 유형 | 설명                                                                                                                                                 |
| ------------ | ----------- | ---------------------------------------------------------------------------------------------------------------------------------------------------- |
| `latents`    | `LATENT`    | 'latents' 매개변수는 리배치될 입력 잠재 표현을 나타냅니다. 이는 출력 배치의 구조와 내용을 결정하는 데 중요합니다.                                    |
| `batch_size` | `INT`       | 'batch_size' 매개변수는 출력에서 배치당 원하는 샘플 수를 지정합니다. 이는 입력 잠재 표현의 새로운 배치로의 그룹화와 분할에 직접적인 영향을 미칩니다. |

## 출력

| 매개변수 | 데이터 유형 | 설명                                                                                                            |
| -------- | ----------- | --------------------------------------------------------------------------------------------------------------- |
| `latent` | `LATENT`    | 출력은 지정된 배치 크기에 따라 조정된 잠재 표현의 재조직된 배치입니다. 이는 추가 처리나 분석을 용이하게 합니다. |
