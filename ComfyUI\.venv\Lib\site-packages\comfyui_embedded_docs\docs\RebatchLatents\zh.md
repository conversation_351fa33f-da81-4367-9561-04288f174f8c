
此节点设计用于根据指定的批量大小将潜在表示的批量重新组织成新的批量配置。它确保潜在样本被适当分组，处理尺寸和大小的变化，以便于进一步处理或模型推断。

## 输入

| 参数名称 | 数据类型 | 作用 |
| --- | --- | --- |
| `latents` | `LATENT` | `latents`参数代表要重新批量处理的输入潜在表示。它对于确定输出批量的结构和内容至关重要。 |
| `batch_size` | `INT` | `batch_size`参数指定输出中每个批量所需的样本数量。它直接影响将输入潜在表示分组和划分为新批量的方式。 |

## 输出

| 参数名称 | 数据类型 | 作用 |
| --- | --- | --- |
| `latent` | `LATENT` | 输出是重新组织的潜在表示批量，根据指定的批量大小进行了调整。它便于进一步的处理或分析。 |
