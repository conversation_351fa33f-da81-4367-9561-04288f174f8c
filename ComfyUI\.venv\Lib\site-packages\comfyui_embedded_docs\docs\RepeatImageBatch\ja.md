
RepeatImageBatchノードは、指定された回数だけ特定の画像を複製し、同一の画像バッチを作成するために設計されています。この機能は、バッチ処理やデータ拡張など、同じ画像の複数のインスタンスを必要とする操作に役立ちます。

## 入力

| フィールド   | Data Type | 説明                                                                 |
|-------------|-------------|---------------------------------------------------------------------|
| `image`     | `IMAGE`     | `image`パラメータは複製される画像を表します。バッチ全体で複製されるコンテンツを定義するために重要です。 |
| `amount`    | `INT`       | `amount`パラメータは入力画像が複製される回数を指定します。出力バッチのサイズに直接影響を与え、柔軟なバッチ作成を可能にします。 |

## 出力

| フィールド | Data Type | 説明                                                              |
|-----------|-------------|------------------------------------------------------------------|
| `image`   | `IMAGE`     | 出力は、指定された`amount`に従って複製された、入力画像と同一の画像バッチです。 |
