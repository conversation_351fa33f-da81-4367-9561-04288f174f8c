
RepeatLatentBatchノードは、指定された回数だけ潜在表現のバッチを複製するように設計されています。ノイズマスクやバッチインデックスなどの追加データを含むことも可能です。この機能は、データ拡張や特定の生成タスクなど、同じ潜在データの複数のインスタンスを必要とする操作において重要です。

## 入力

| パラメータ | データ型 | 説明 |
|-----------|-------------|-------------|
| `samples` | `LATENT`    | `samples`パラメータは、複製される潜在表現を表します。繰り返し処理を行うデータを定義するために重要です。 |
| `amount`  | `INT`       | `amount`パラメータは、入力サンプルが繰り返される回数を指定します。出力バッチのサイズに直接影響を与え、計算負荷や生成データの多様性に影響を与えます。 |

## 出力

| パラメータ | データ型 | 説明 |
|-----------|-------------|-------------|
| `latent`  | `LATENT`    | 出力は、指定された`amount`に従って複製された入力潜在表現の修正版です。必要に応じて、複製されたノイズマスクや調整されたバッチインデックスを含むことがあります。 |
