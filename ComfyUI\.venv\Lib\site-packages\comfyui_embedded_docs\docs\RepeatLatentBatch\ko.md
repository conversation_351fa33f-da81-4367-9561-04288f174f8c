
RepeatLatentBatch 노드는 주어진 잠재 표현 배치를 지정된 횟수만큼 복제하도록 설계되었습니다. 여기에는 노이즈 마스크나 배치 인덱스와 같은 추가 데이터가 포함될 수 있습니다. 이 기능은 데이터 증강이나 특정 생성 작업과 같이 동일한 잠재 데이터를 여러 번 사용하는 작업에 필수적입니다.

## 입력

| 매개변수  | 데이터 유형 | 설명                                                                                                                                                          |
| --------- | ----------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `samples` | `LATENT`    | 'samples' 매개변수는 복제될 잠재 표현을 나타냅니다. 반복될 데이터를 정의하는 데 필수적입니다.                                                                 |
| `amount`  | `INT`       | 'amount' 매개변수는 입력 샘플이 반복될 횟수를 지정합니다. 이는 출력 배치의 크기에 직접적인 영향을 미치며, 계산 부하와 생성된 데이터의 다양성에 영향을 줍니다. |

## 출력

| 매개변수 | 데이터 유형 | 설명                                                                                                                                                 |
| -------- | ----------- | ---------------------------------------------------------------------------------------------------------------------------------------------------- |
| `latent` | `LATENT`    | 출력은 지정된 'amount'에 따라 복제된 입력 잠재 표현의 수정된 버전입니다. 필요에 따라 복제된 노이즈 마스크와 조정된 배치 인덱스를 포함할 수 있습니다. |
