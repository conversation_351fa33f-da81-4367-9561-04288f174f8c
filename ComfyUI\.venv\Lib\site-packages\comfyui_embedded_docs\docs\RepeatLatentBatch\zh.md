
此节点设计用于复制给定批量的潜在表示指定的次数，可能包括额外的数据，如噪声遮罩和批量索引。此功能对于需要相同潜在数据的多个实例的操作至关重要，例如数据增强或特定的生成任务。

## 输入

| 参数名称 | 数据类型 | 作用 |
| --- | --- | --- |
| `samples` | `LATENT` | `samples`参数表示要复制的潜在表示。它对于定义将经历重复的数据至关重要。 |
| `amount` | `INT` | `amount`参数指定输入样本应重复的次数。它直接影响输出批量的大小，从而影响计算负载和生成数据的多样性。 |

## 输出

| 参数名称 | 数据类型 | 作用 |
| --- | --- | --- |
| `latent` | `LATENT` | 输出是根据指定的`amount`复制的输入潜在表示的修改版本。如果适用，它可能包括复制的噪声遮罩和调整后的批量索引。 |
