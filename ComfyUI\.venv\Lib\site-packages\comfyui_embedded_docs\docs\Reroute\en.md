Node Name: Reroute Node
Node Purpose: Mainly used to organize the logic of overly long connection lines in the ComfyUI workflow.

## How to Use Reroute Nodes

| Menu Options | Description |
| --- | --- |
| Show Type | Display the node's type property |
| Hide Type By Default | Hide the node's type property by default |
| Set Vertical | Set the node's wiring direction to vertical |
| Set Horizontal | Set the node's wiring direction to horizontal |

When your wiring logic is too long and complex, and you want to tidy up the interface, you can insert a ```Reroute``` node between two connection points. The input and output of this node are not type-restricted, and the default style is horizontal. You can change the wiring direction to vertical through the right-click menu.
