Node Name: Reroute Node
Node Purpose: Principalmente utilizado para organizar la lógica de líneas de conexión excesivamente largas en el flujo de trabajo de ComfyUI.

## How to Use Reroute Nodes | Cómo Usar Nodos Redirigir

| Menu Options | Spanish | Description |
| --- | --- |
| Show Type | Mostrar Tipo | Display the node's type property |
| Hide Type By Default | Ocultar Tipo por Defecto | Hide the node's type property by default |
| Set Vertical | Establecer Vertical | Set the node's wiring direction to vertical |
| Set Horizontal | Establecer Horizontal | Set the node's wiring direction to horizontal |
Cuando tu lógica de cableado es demasiado larga y compleja, y deseas ordenar la interfaz, puedes insertar un nodo ```Reroute``` entre dos puntos de conexión. La entrada y salida de este nodo no están restringidas por tipo, y el estilo predeterminado es horizontal. Puedes cambiar la dirección del cableado a vertical a través del menú de clic derecho.
