Nom du nœud : Reroute Node (Nœud de Dérivation)  
But du nœud : Principalement utilisé pour organiser la logique des lignes de connexion trop longues dans le flux de travail ComfyUI.

## Comment utiliser les nœuds Reroute

| Options de menu | Description |
| --- | --- |
| Show Type | Afficher la propriété de type du nœud |
| Hide Type By Default | Masquer la propriété de type du nœud par défaut |
| Set Vertical | Définir la direction du câblage du nœud à vertical |
| Set Horizontal | Définir la direction du câblage du nœud à horizontal |

Lorsque votre logique de câblage est trop longue et complexe, et que vous souhaitez organiser l'interface, vous pouvez insérer un nœud ```Reroute``` entre deux points de connexion. L'entrée et la sortie de ce nœud ne sont pas restreintes par le type, et le style par défaut est horizontal. Vous pouvez changer la direction du câblage en vertical via le menu contextuel.
