
ノード名: Reroute Node（リルートノード）
ノードの目的: ComfyUIワークフローにおいて、過度に長い接続線のロジックを整理するために主に使用されます。

## Rerouteノードの使い方

| メニューオプション | 説明 |
| --- | --- |
| Show Type | ノードのタイププロパティを表示 |
| Hide Type By Default | デフォルトでノードのタイププロパティを非表示 |
| Set Vertical | ノードの配線方向を垂直に設定 |
| Set Horizontal | ノードの配線方向を水平に設定 |

配線ロジックが長すぎて複雑な場合、インターフェースを整理したいときは、2つの接続ポイントの間に```Reroute```ノードを挿入することができます。このノードの入力と出力はタイプ制限がなく、デフォルトスタイルは水平です。右クリックメニューを使用して配線方向を垂直に変更することができます。
