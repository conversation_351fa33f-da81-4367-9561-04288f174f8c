
노드 이름: Reroute Node
노드 목적: 주로 ComfyUI 워크플로우에서 지나치게 긴 연결선의 논리를 정리하는 데 사용됩니다.

## Reroute 노드 사용 방법

| 메뉴 옵션 | 설명 |
| --- | --- |
| Show Type | 노드의 유형 속성을 표시합니다 |
| Hide Type By Default | 기본적으로 노드의 유형 속성을 숨깁니다 |
| Set Vertical | 노드의 배선 방향을 수직으로 설정합니다 |
| Set Horizontal | 노드의 배선 방향을 수평으로 설정합니다 |

배선 논리가 너무 길고 복잡하여 인터페이스를 정리하고 싶을 때, 두 연결 지점 사이에 ```Reroute``` 노드를 삽입할 수 있습니다. 이 노드의 입력과 출력은 유형 제한이 없으며, 기본 스타일은 수평입니다. 오른쪽 클릭 메뉴를 통해 배선 방향을 수직으로 변경할 수 있습니다.
