
RescaleCFG 노드는 모델 출력의 조건 및 비조건 스케일을 지정된 배수에 따라 조정하여 보다 균형 잡히고 제어된 생성 과정을 목표로 설계되었습니다. 이 노드는 모델의 출력을 리스케일하여 조건 및 비조건 구성 요소의 영향을 수정함으로써 모델의 성능이나 출력 품질을 향상시킬 수 있습니다.

## 입력

| 매개변수     | 데이터 유형 | 설명                                                                                                                                                                     |
| ------------ | ----------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| `model`      | MODEL       | 모델 매개변수는 조정할 생성 모델을 나타냅니다. 이 노드는 모델의 출력에 리스케일링 기능을 적용하여 생성 과정을 직접적으로 영향을 미칩니다.                                |
| `multiplier` | `FLOAT`     | 배수 매개변수는 모델 출력에 적용되는 리스케일링의 정도를 제어합니다. 이는 원래 구성 요소와 리스케일된 구성 요소 간의 균형을 결정하여 최종 출력의 특성에 영향을 미칩니다. |

## 출력

| 매개변수 | 데이터 유형 | 설명                                                                                                                                                       |
| -------- | ----------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `model`  | MODEL       | 조정된 조건 및 비조건 스케일을 가진 수정된 모델입니다. 이 모델은 적용된 리스케일링으로 인해 잠재적으로 향상된 특성을 가진 출력을 생성할 것으로 예상됩니다. |
