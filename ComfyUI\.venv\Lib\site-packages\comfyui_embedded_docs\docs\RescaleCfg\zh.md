RescaleCFG 节点旨在根据指定的乘数调整模型输出的条件和非条件比例，目的是实现更加平衡和可控的生成过程。它通过重新调整模型的输出来修改条件和非条件组件的影响，从而可能增强模型的性能或输出质量。

## 输入

| 参数名称 | 数据类型 | 作用                                                         |
| -------- | -------- | ------------------------------------------------------------ |
| `model`  | MODEL    | 表示要调整的生成模型。节点对模型的输出应用重新缩放函数，直接影响生成过程。 |
| `multiplier` | `FLOAT` | 控制应用于模型输出的重新缩放程度。它决定了原始和重新缩放组件之间的平衡，影响最终输出的特性。 |

## 输出

| 参数名称 | 数据类型 | 作用                                       |
| -------- | -------- | ------------------------------------------ |
| `model`  | MODEL    | 修改后的模型，调整了条件和非条件比例。预期该模型由于应用的重新缩放，将产生具有潜在增强特性的输出。 |
