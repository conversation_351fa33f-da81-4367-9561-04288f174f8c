
SamplerCustom 노드는 다양한 응용 프로그램을 위한 유연하고 맞춤화된 샘플링 메커니즘을 제공하도록 설계되었습니다. 사용자가 특정 요구에 맞춘 다양한 샘플링 전략을 선택하고 구성할 수 있게 하여 샘플링 프로세스의 적응성과 효율성을 향상시킵니다.

## 입력

| 매개변수       | 데이터 유형    | 설명                                                                                                                                       |
| -------------- | -------------- | ------------------------------------------------------------------------------------------------------------------------------------------ |
| `model`        | `MODEL`        | 'model' 입력 유형은 샘플링에 사용할 모델을 지정하며, 샘플링 동작과 출력에 중요한 역할을 합니다.                                            |
| `add_noise`    | `BOOLEAN`      | 'add_noise' 입력 유형은 샘플링 과정에 노이즈를 추가할지 여부를 사용자가 지정할 수 있게 하여 생성된 샘플의 다양성과 특성에 영향을 미칩니다. |
| `noise_seed`   | `INT`          | 'noise_seed' 입력 유형은 노이즈 생성에 대한 시드를 제공하여 노이즈 추가 시 샘플링 과정의 재현성과 일관성을 보장합니다.                     |
| `cfg`          | `FLOAT`        | 'cfg' 입력 유형은 샘플링 프로세스의 구성을 설정하여 샘플링 매개변수와 동작을 세밀하게 조정할 수 있게 합니다.                               |
| `positive`     | `CONDITIONING` | 'positive' 입력 유형은 긍정적 조건 정보를 나타내며, 샘플링 과정이 지정된 긍정적 속성과 일치하는 샘플을 생성하도록 안내합니다.              |
| `negative`     | `CONDITIONING` | 'negative' 입력 유형은 부정적 조건 정보를 나타내며, 샘플링 과정이 지정된 부정적 속성을 나타내는 샘플 생성을 피하도록 유도합니다.           |
| `sampler`      | `SAMPLER`      | 'sampler' 입력 유형은 사용할 특정 샘플링 전략을 선택하여 생성된 샘플의 성격과 품질에 직접적인 영향을 미칩니다.                             |
| `sigmas`       | `SIGMAS`       | 'sigmas' 입력 유형은 샘플링 과정에서 사용할 노이즈 레벨을 정의하여 샘플 공간의 탐색과 출력의 다양성에 영향을 미칩니다.                     |
| `latent_image` | `LATENT`       | 'latent_image' 입력 유형은 샘플링 과정의 초기 잠재 이미지를 제공하여 샘플 생성의 시작점으로 작용합니다.                                    |

## 출력

| 매개변수          | 데이터 유형 | 설명                                                                                                                 |
| ----------------- | ----------- | -------------------------------------------------------------------------------------------------------------------- |
| `output`          | `LATENT`    | 'output'는 샘플링 과정의 주요 결과를 나타내며, 생성된 샘플을 포함합니다.                                             |
| `denoised_output` | `LATENT`    | 'denoised_output'는 디노이즈 과정이 적용된 후의 샘플을 나타내며, 생성된 샘플의 명확성과 품질을 향상시킬 수 있습니다. |
