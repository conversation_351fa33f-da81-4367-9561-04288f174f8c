
`SamplerCustom` 节点旨在为各种应用提供灵活且可定制的采样机制。它允许用户选择并配置不同的采样策略，以满足其特定需求，从而增强采样过程的适应性和效率。

## 输入

| 参数名称       | 数据类型 | 作用                                                         |
|----------------|----------|--------------------------------------------------------------|
| `model`        | MODEL    | `model` 输入类型指定用于采样的模型，对确定采样行为和输出起着关键作用。 |
| `add_noise`    | BOOLEAN  | `add_noise` 输入类型允许用户指定是否应在采样过程中添加噪声，影响生成样本的多样性和特性。 |
| `noise_seed`   | INT      | `noise_seed` 输入类型为噪声生成提供种子，确保在添加噪声时采样过程的可复现性和一致性。 |
| `cfg`          | FLOAT    | `cfg` 输入类型设置采样过程的配置，允许微调采样参数和行为。                     |
| `positive`     | CONDITIONING | `positive` 输入类型代表正面条件信息，指导采样过程生成符合指定正面属性的样本。 |
| `negative`     | CONDITIONING | `negative` 输入类型代表负面条件信息，引导采样过程避免生成显示指定负面属性的样本。 |
| `sampler`      | SAMPLER  | `sampler` 输入类型选择要使用的特定采样策略，直接影响生成样本的性质和质量。   |
| `sigmas`       | SIGMAS   | `sigmas` 输入类型定义采样过程中要使用的噪声水平，影响样本空间的探索和输出的多样性。 |
| `latent_image` | LATENT   | `latent_image` 输入类型为采样过程提供初始潜在图像，作为样本生成的起点。     |

## 输出

| 参数名称         | 数据类型 | 作用                                                         |
|-----------------|----------|--------------------------------------------------------------|
| `output`        | LATENT   | `output` 代表采样过程的主要结果，包含生成的样本。             |
| `denoised_output` | LATENT   | `denoised_output` 代表应用去噪过程后的样本，可能增强生成样本的清晰度和质量。 |
