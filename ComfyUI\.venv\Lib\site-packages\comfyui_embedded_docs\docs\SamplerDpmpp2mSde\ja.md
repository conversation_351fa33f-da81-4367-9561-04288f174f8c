
このノードは、DPMPP_2M_SDEモデル用のサンプラーを生成するために設計されています。指定されたソルバータイプ、ノイズレベル、計算デバイスの好みに基づいてサンプルを作成することができます。サンプラー設定の複雑さを抽象化し、カスタマイズされた設定でサンプルを生成するための簡潔なインターフェースを提供します。

## 入力

| パラメータ       | Data Type | 説明                                                                 |
|-----------------|-------------|---------------------------------------------------------------------|
| `solver_type`   | COMBO[STRING] | サンプリングプロセスで使用されるソルバータイプを指定し、'midpoint'と'heun'のオプションを提供します。この選択は、サンプリング中に適用される数値積分法に影響を与えます。 |
| `eta`           | `FLOAT`     | 数値積分におけるステップサイズを決定し、サンプリングプロセスの粒度に影響を与えます。値が大きいほどステップサイズが大きくなります。 |
| `s_noise`       | `FLOAT`     | サンプリングプロセス中に導入されるノイズのレベルを制御し、生成されるサンプルの変動性に影響を与えます。 |
| `noise_device`  | COMBO[STRING] | ノイズ生成プロセスが実行される計算デバイス（'gpu'または'cpu'）を示し、パフォーマンスと効率に影響を与えます。 |

## 出力

| パラメータ       | Data Type | 説明                                                                 |
|-----------------|-------------|---------------------------------------------------------------------|
| `sampler`       | `SAMPLER`   | 指定されたパラメータに従って構成されたサンプラーで、サンプル生成の準備が整っています。 |
