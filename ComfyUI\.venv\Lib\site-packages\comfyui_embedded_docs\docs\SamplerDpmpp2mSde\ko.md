
이 노드는 DPMPP_2M_SDE 모델을 위한 샘플러를 생성하도록 설계되었습니다. 이는 지정된 솔버 유형, 노이즈 레벨 및 계산 장치 선호도에 따라 샘플을 생성할 수 있게 합니다. 샘플러 설정의 복잡성을 추상화하여 사용자에게 맞춤형 설정으로 샘플을 생성할 수 있는 간편한 인터페이스를 제공합니다.

## 입력

| 매개변수       | 데이터 유형   | 설명                                                                                                                                                         |
| -------------- | ------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| `solver_type`  | COMBO[STRING] | 샘플링 과정에서 사용할 솔버 유형을 지정하며, 'midpoint'와 'heun' 중에서 선택할 수 있습니다. 이 선택은 샘플링 중 적용되는 수치적 통합 방법에 영향을 미칩니다. |
| `eta`          | `FLOAT`       | 수치적 통합에서의 스텝 크기를 결정하며, 샘플링 과정의 세분화에 영향을 줍니다. 높은 값은 더 큰 스텝 크기를 나타냅니다.                                        |
| `s_noise`      | `FLOAT`       | 샘플링 과정에서 도입되는 노이즈의 수준을 조절하여 생성된 샘플의 변동성을 조절합니다.                                                                         |
| `noise_device` | COMBO[STRING] | 노이즈 생성 과정이 실행되는 계산 장치('gpu' 또는 'cpu')를 나타내며, 성능과 효율성에 영향을 미칩니다.                                                         |

## 출력

| 매개변수  | 데이터 유형 | 설명                                                                        |
| --------- | ----------- | --------------------------------------------------------------------------- |
| `sampler` | `SAMPLER`   | 지정된 매개변수에 따라 구성된 샘플러로, 샘플 생성 준비가 완료된 상태입니다. |
