
このノードは、DPM++ SDE（確率微分方程式）モデル用のサンプラーを生成するために設計されています。CPUとGPUの両方の実行環境に適応し、利用可能なハードウェアに基づいてサンプラーの実装を最適化します。

## 入力

| パラメータ      | Data Type | 説明 |
|----------------|-------------|-------------|
| `eta`          | FLOAT       | SDEソルバーのステップサイズを指定し、サンプリングプロセスの粒度に影響を与えます。|
| `s_noise`      | FLOAT       | サンプリングプロセス中に適用されるノイズのレベルを決定し、生成されるサンプルの多様性に影響を与えます。|
| `r`            | FLOAT       | サンプリングプロセスにおけるノイズ削減の比率を制御し、生成されるサンプルの明瞭さと品質に影響を与えます。|
| `noise_device` | COMBO[STRING]| サンプラーの実行環境（CPUまたはGPU）を選択し、利用可能なハードウェアに基づいてパフォーマンスを最適化します。|

## 出力

| パラメータ    | Data Type | 説明 |
|----------------|-------------|-------------|
| `sampler`    | SAMPLER     | 指定されたパラメータで構成されたサンプラーで、サンプリング操作に使用する準備が整っています。 |
