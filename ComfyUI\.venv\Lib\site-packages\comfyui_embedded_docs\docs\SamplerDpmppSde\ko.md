
이 노드는 DPM++ SDE (Stochastic Differential Equation) 모델을 위한 샘플러를 생성하도록 설계되었습니다. 이는 CPU와 GPU 실행 환경 모두에 적응하여, 사용 가능한 하드웨어에 따라 샘플러의 구현을 최적화합니다.

## 입력

| 매개변수       | 데이터 유형   | 설명                                                                                         |
| -------------- | ------------- | -------------------------------------------------------------------------------------------- |
| `eta`          | FLOAT         | SDE 솔버의 스텝 크기를 지정하여 샘플링 과정의 세분화에 영향을 줍니다.                        |
| `s_noise`      | FLOAT         | 샘플링 과정에서 적용할 노이즈의 수준을 결정하여 생성된 샘플의 다양성에 영향을 미칩니다.      |
| `r`            | FLOAT         | 샘플링 과정에서 노이즈 감소 비율을 조절하여 생성된 샘플의 명확성과 품질에 영향을 미칩니다.   |
| `noise_device` | COMBO[STRING] | 샘플러의 실행 환경(CPU 또는 GPU)을 선택하여 사용 가능한 하드웨어에 따라 성능을 최적화합니다. |

## 출력

| 매개변수  | 데이터 유형 | 설명                                                                          |
| --------- | ----------- | ----------------------------------------------------------------------------- |
| `sampler` | SAMPLER     | 지정된 매개변수로 구성된 샘플러로, 샘플링 작업에 사용할 준비가 되어 있습니다. |
