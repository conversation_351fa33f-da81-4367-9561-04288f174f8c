
SaveAnimatedPNGノードは、フレームのシーケンスからアニメーションPNG画像を作成および保存するために設計されています。個々の画像フレームを統合して一貫したアニメーションを組み立て、フレームの持続時間、ループ、およびメタデータのカスタマイズを可能にします。

## 入力

| フィールド          | Data Type | 説明                                                                                 |
|-------------------|-------------|-------------------------------------------------------------------------------------|
| `images`          | `IMAGE`     | 処理され、アニメーションPNGとして保存される画像のリスト。リスト内の各画像はアニメーションのフレームを表します。 |
| `filename_prefix` | `STRING`    | 出力ファイルの基本名を指定し、生成されたアニメーションPNGファイルの接頭辞として使用されます。 |
| `fps`             | `FLOAT`     | アニメーションのフレーム毎秒数を指定し、フレームが表示される速度を制御します。 |
| `compress_level`  | `INT`       | アニメーションPNGファイルに適用される圧縮レベルで、ファイルサイズと画像の明瞭さに影響を与えます。 |

## 出力

| フィールド | Data Type | 説明                                                                       |
|-------|-------------|-----------------------------------------------------------------------------------|
| `ui`  | N/A         | 生成されたアニメーションPNG画像を表示し、アニメーションが単一フレームか複数フレームかを示すUIコンポーネントを提供します。 |
