
此节点设计用于从一系列帧创建和保存动画PNG图像。它处理将单独的图像帧组装成连贯的动画，并允许自定义帧持续时间、循环和包含元数据。

## 输入

| 参数名称 | 数据类型 | 作用 |
| --- | --- | --- |
| `images` | `IMAGE` | 要处理并保存为动画PNG的图像列表。列表中的每个图像代表动画中的一帧。 |
| `filename_prefix` | STRING   | 指定输出文件的基本名称，将用作生成的动画PNG文件的前缀。 |
| `fps` | `FLOAT` | 动画的每秒帧数率，控制帧显示的速度。 |
| `compress_level` | `INT` | 应用于动画PNG文件的压缩级别，影响文件大小和图像清晰度。 |

## 输出

| 参数名称 | 数据类型 | 作用 |
| --- | --- | --- |
| `ui` | - | 提供一个UI组件，展示生成的动画PNG图像，并指示动画是单帧还是多帧。 |

---
