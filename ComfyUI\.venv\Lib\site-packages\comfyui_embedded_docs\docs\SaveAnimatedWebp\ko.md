
이 노드는 이미지 시퀀스를 애니메이션 WEBP 파일로 저장하도록 설계되었습니다. 개별 프레임을 통합하여 일관된 애니메이션을 생성하고, 지정된 메타데이터를 적용하며, 품질 및 압축 설정에 따라 출력을 최적화합니다.

## 입력

| 필드              | 데이터 유형   | 설명                                                                                                                                 |
| ----------------- | ------------- | ------------------------------------------------------------------------------------------------------------------------------------ |
| `images`          | `IMAGE`       | 애니메이션 WEBP의 프레임으로 저장될 이미지 목록입니다. 이 매개변수는 애니메이션의 시각적 콘텐츠를 정의하는 데 필수적입니다.          |
| `filename_prefix` | `STRING`      | 출력 파일의 기본 이름을 지정하며, 카운터와 '.webp' 확장자가 추가됩니다. 이 매개변수는 저장된 파일을 식별하고 정리하는 데 중요합니다. |
| `fps`             | `FLOAT`       | 애니메이션의 초당 프레임 수로, 재생 속도에 영향을 미칩니다.                                                                          |
| `lossless`        | `BOOLEAN`     | 무손실 압축을 사용할지 여부를 나타내는 불리언 값으로, 애니메이션의 파일 크기와 품질에 영향을 미칩니다.                               |
| `quality`         | `INT`         | 0에서 100 사이의 값으로 압축 품질 수준을 설정하며, 값이 높을수록 더 나은 이미지 품질을 제공하지만 파일 크기가 커집니다.              |
| `method`          | COMBO[STRING] | 사용할 압축 방법을 지정하며, 인코딩 속도와 파일 크기에 영향을 미칠 수 있습니다.                                                      |

## 출력

| 필드 | 데이터 유형 | 설명                                                                                                                         |
| ---- | ----------- | ---------------------------------------------------------------------------------------------------------------------------- |
| `ui` | N/A         | 저장된 애니메이션 WEBP 이미지와 메타데이터를 표시하는 UI 컴포넌트를 제공하며, 애니메이션이 활성화되었는지 여부를 나타냅니다. |
