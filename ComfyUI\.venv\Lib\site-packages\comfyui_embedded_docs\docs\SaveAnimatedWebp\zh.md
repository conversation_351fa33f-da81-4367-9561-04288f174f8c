此节点设计用于将一系列图像保存为动画WEBP文件。它处理将单独的帧聚合成连贯的动画，应用指定的元数据，并基于质量和压缩设置优化输出。

## 输入

| 参数名称 | 数据类型 | 作用 |
| --- | --- | --- |
| `images` | `IMAGE` | 要作为动画WEBP帧保存的图像列表。此参数对于定义动画的视觉内容至关重要。 |
| `filename_prefix` | STRING   | 指定输出文件的基本名称，将与计数器和“.webp”扩展名一起附加。此参数对于识别和组织保存的文件至关重要。 |
| `fps` | `FLOAT` | 动画的每秒帧数率，影响播放速度。 |
| `lossless` | `BOOLEAN` | 布尔值，指示是否使用无损压缩，影响动画的文件大小和质量。 |
| `quality` | `INT` | 0到100之间的值，设置压缩质量级别，值越高，图像质量越好，但文件大小越大。 |
| `method` | COMBO[STRING] | 指定要使用的压缩方法，这可以影响编码速度和文件大小。 |

## 输出

| 参数名称 | 数据类型 | 作用 |
| --- | --- | --- |
| `ui` | - | 提供一个UI组件，展示保存的动画WEBP图像及其元数据，并指示是否启用了动画。 |

---
