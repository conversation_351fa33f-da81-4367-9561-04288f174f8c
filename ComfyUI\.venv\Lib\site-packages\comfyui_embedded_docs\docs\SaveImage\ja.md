
**ノードの機能：** `Save Image-画像を保存`ノードは、主にComfyUIの**output**フォルダに画像を保存するために使用されます。中間処理で画像を保存せずにプレビューだけしたい場合は、`Preview Image-画像のプレビュー`ノードを使用できます。
デフォルトの保存場所：`ComfyUI/output/`

## 入力

| パラメータ | データ型 | 説明 |
|-----------|-------------|-------------|
| `images` | `IMAGE` | 保存する画像。このパラメータは、ディスクに処理および保存される画像データを直接含むため、重要です。 |
| `filename_prefix` | STRING   | `ComfyUI/output/`フォルダに保存される画像のファイル名プレフィックス。デフォルトは`ComfyUI`ですが、カスタマイズ可能です。 |

## 右クリックメニューオプション

画像生成が完了した後、対応するメニューを右クリックすると、以下のノード固有のオプションと機能が表示されます：

| オプション名 | 機能 |
|------------|------|
| `Save Image-画像を保存` | 画像をローカルに保存 |
| `Copy Image-画像をコピー` | 画像をクリップボードにコピー |
| `Open Image-画像を開く` | ブラウザの新しいタブで画像を開く |

保存された画像は一般的にPNG形式で、すべての画像生成データが含まれています。対応するワークフローを再生成に使用したい場合は、対応する画像をComfyUIにロードするだけでワークフローをロードできます。
