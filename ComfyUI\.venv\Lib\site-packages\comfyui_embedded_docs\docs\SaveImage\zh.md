
**节点功能：**`Save Image-保存图像`节点主要用于把图像保存到comfyUI的**output**文件夹中，如果中间过程你只是想预览而不是保存图像，可以使用`Preview Image-预览图像`节点
默认保存图片位置： `ComfyUI/output/`

## 输入

| 参数名称           | 数据类型 | 作用                                                         |
|------------------|----------|--------------------------------------------------------------|
| `images`         | `IMAGE`  | 要保存的图像。此参数至关重要，因为它直接包含将被处理和保存到磁盘的图像数据。 |
| `filename_prefix` | STRING   | 保存到`ComfyUI/output/`文件夹中的图像的文件名前缀，默认为`ComfyUI`你也可以自定义对应的名称 |

## 节点右键功能菜单

在图片生成完成后在对应的菜单上右键有以下几个节点专属的选项及功能

| 选项名称 | 功能 |
|----------|------|
| `Save Image-保存图像` | 保存图像到本地 |
| `Copy Image-复制图像` | 复制图像到剪贴板 |
| `Open Image-打开图像` | 在浏览新标签页打开图片 |
保存的图片一般为 PNG 格式，并且包含了所有的图片生成的数据，如果你想要使用对应的工作流进行再次生成，把对应图片载入 ComfyUI 中即可加载对应的工作流
