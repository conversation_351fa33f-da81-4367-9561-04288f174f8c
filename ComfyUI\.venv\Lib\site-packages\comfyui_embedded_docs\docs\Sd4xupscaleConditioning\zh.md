此节点专门通过4倍放大过程增强图像分辨率，同时结合条件元素来细化输出。它利用扩散技术在放大图像的同时，允许调整缩放比率和噪声增强，以微调增强过程。

## 输入

| 参数名称 | 数据类型 | 作用 |
| --- | --- | --- |
| `images` | `IMAGE` | 要放大的输入图像。此参数至关重要，因为它直接影响输出图像的质量和分辨率。 |
| `positive` | `CONDITIONING` | 正面条件元素，指导放大过程朝着输出图像中的期望属性或特征发展。 |
| `negative` | `CONDITIONING` | 负面条件元素，放大过程应避免这些元素，有助于引导输出远离不希望的属性或特征。 |
| `scale_ratio` | `FLOAT` | 确定图像分辨率增加的因子。更高的缩放比率会产生更大的输出图像，允许更详细和清晰的细节。 |
| `noise_augmentation` | `FLOAT` | 控制放大过程中应用的噪声增强水平。这可以用来引入变异性并提高输出图像的鲁棒性。 |

## 输出

| 参数名称 | 数据类型 | 作用 |
| --- | --- | --- |
| `positive` | `CONDITIONING` | 放大过程后得到的精细正面条件元素。 |
| `negative` | `CONDITIONING` | 放大过程后得到的精细负面条件元素。 |
| `latent` | `LATENT` | 在放大过程中生成的潜在表示，可用于进一步处理或模型训练。 |
