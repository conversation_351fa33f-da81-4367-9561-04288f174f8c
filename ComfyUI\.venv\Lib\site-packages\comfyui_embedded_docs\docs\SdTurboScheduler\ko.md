
SDTurboScheduler는 이미지 샘플링을 위한 시그마 값의 시퀀스를 생성하도록 설계되었습니다. 이는 지정된 디노이즈 수준과 단계 수에 따라 시퀀스를 조정합니다. 특정 모델의 샘플링 기능을 활용하여 이러한 시그마 값을 생성하며, 이는 이미지 생성 중 디노이즈 과정을 제어하는 데 필수적입니다.

## 입력

| 매개변수  | 데이터 유형 | 설명                                                                                                                            |
| --------- | ----------- | ------------------------------------------------------------------------------------------------------------------------------- |
| `model`   | `MODEL`     | 모델 매개변수는 시그마 값 생성을 위한 생성 모델을 지정합니다. 이는 스케줄러의 특정 샘플링 동작과 기능을 결정하는 데 중요합니다. |
| `steps`   | `INT`       | 단계 매개변수는 생성될 시그마 시퀀스의 길이를 결정하며, 디노이즈 과정의 세분화에 직접적인 영향을 미칩니다.                      |
| `denoise` | `FLOAT`     | 디노이즈 매개변수는 시그마 시퀀스의 시작점을 조정하여 이미지 생성 중 적용되는 디노이즈 수준을 세밀하게 제어할 수 있게 합니다.   |

## 출력

| 매개변수 | 데이터 유형 | 설명                                                                                                                                           |
| -------- | ----------- | ---------------------------------------------------------------------------------------------------------------------------------------------- |
| `sigmas` | `SIGMAS`    | 지정된 모델, 단계 및 디노이즈 수준에 따라 생성된 시그마 값의 시퀀스입니다. 이 값들은 이미지 생성에서 디노이즈 과정을 제어하는 데 필수적입니다. |
