
このノードは、潜在サンプルのセットにノイズマスクを適用するように設計されています。指定されたマスクを統合することで、入力サンプルのノイズ特性を変更します。

## 入力

| パラメータ | データ型 | 説明 |
|-----------|-------------|-------------|
| `samples` | `LATENT`    | ノイズマスクが適用される潜在サンプル。このパラメータは、変更される基礎コンテンツを決定する上で重要です。 |
| `mask`    | `MASK`      | 潜在サンプルに適用されるマスク。サンプル内のノイズ変更の領域と強度を定義します。 |

## 出力

| パラメータ | データ型 | 説明 |
|-----------|-------------|-------------|
| `latent`  | `LATENT`    | ノイズマスクが適用された修正済みの潜在サンプル。 |
