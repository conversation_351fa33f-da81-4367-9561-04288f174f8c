
SolidMaskノードは、指定された値で全体が均一なマスクを生成します。特定の寸法と強度のマスクを作成するように設計されており、さまざまな画像処理やマスキングタスクに役立ちます。

## 入力

| パラメータ | データ型 | 説明 |
|-----------|-------------|-------------|
| `value`   | FLOAT       | マスクの強度値を指定し、全体の外観と後続の操作での有用性に影響を与えます。 |
| `width`   | INT         | 生成されるマスクの幅を決定し、そのサイズとアスペクト比に直接影響を与えます。 |
| `height`  | INT         | 生成されるマスクの高さを設定し、そのサイズとアスペクト比に影響を与えます。 |

## 出力

| パラメータ | データ型 | 説明 |
|-----------|-------------|-------------|
| `mask`    | MASK        | 指定された寸法と値で均一なマスクを出力します。 |
