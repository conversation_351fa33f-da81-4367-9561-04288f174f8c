
SplitImageWithAlpha 노드는 이미지의 색상과 알파 구성 요소를 분리하도록 설계되었습니다. 입력 이미지 텐서를 처리하여 RGB 채널을 색상 구성 요소로, 알파 채널을 투명성 구성 요소로 추출하여 이러한 개별 이미지 측면을 조작하는 작업을 용이하게 합니다.

## 입력

| 매개변수 | 데이터 유형 | 설명                                                                                                                                   |
| -------- | ----------- | -------------------------------------------------------------------------------------------------------------------------------------- |
| `image`  | `IMAGE`     | 'image' 매개변수는 RGB 및 알파 채널이 분리될 입력 이미지 텐서를 나타냅니다. 이 작업에 필수적이며 분리를 위한 소스 데이터를 제공합니다. |

## 출력

| 매개변수 | 데이터 유형 | 설명                                                                                                   |
| -------- | ----------- | ------------------------------------------------------------------------------------------------------ |
| `image`  | `IMAGE`     | 'image' 출력은 입력 이미지의 분리된 RGB 채널을 나타내며, 투명성 정보 없이 색상 구성 요소를 제공합니다. |
| `mask`   | `MASK`      | 'mask' 출력은 입력 이미지의 분리된 알파 채널을 나타내며, 투명성 정보를 제공합니다.                     |
