
SplitSigmas 노드는 지정된 단계에 따라 시그마 값의 시퀀스를 두 부분으로 나누도록 설계되었습니다. 이 기능은 시그마 시퀀스의 초기 및 후속 부분을 다르게 처리하거나 조작해야 하는 작업에 필수적이며, 이러한 값을 보다 유연하고 목표 지향적으로 조작할 수 있게 합니다.

## 입력

| 매개변수 | 데이터 유형 | 설명                                                                                                                                                                                      |
| -------- | ----------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `sigmas` | `SIGMAS`    | 'sigmas' 매개변수는 분할할 시그마 값의 시퀀스를 나타냅니다. 이는 분할 지점을 결정하고 결과적으로 두 개의 시그마 값 시퀀스를 생성하는 데 필수적이며, 노드의 실행과 결과에 영향을 미칩니다. |
| `step`   | `INT`       | 'step' 매개변수는 시그마 시퀀스를 분할할 인덱스를 지정합니다. 이는 두 개의 결과 시그마 시퀀스 간의 경계를 정의하는 데 중요한 역할을 하며, 노드의 기능과 출력의 특성에 영향을 미칩니다.    |

## 출력

| 매개변수 | 데이터 유형 | 설명                                                                                                                                                                                   |
| -------- | ----------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `sigmas` | `SIGMAS`    | 노드는 시그마 값의 두 시퀀스를 출력하며, 각각은 지정된 단계에서 나누어진 원래 시퀀스의 일부를 나타냅니다. 이러한 출력은 시그마 값을 차별화하여 처리해야 하는 후속 작업에 필수적입니다. |
