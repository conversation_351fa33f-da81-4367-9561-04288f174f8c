`SplitSigmas` 节点旨在将一系列 sigma 值根据指定的步骤分割成两部分。这一功能对于需要对 sigma 序列的初始部分和后续部分进行不同处理或处理的操作至关重要，它允许对这些值进行更灵活和针对性的操作。

## 输入

| 参数名称 | 数据类型 | 作用                                                         |
|----------|----------|--------------------------------------------------------------|
| `sigmas` | SIGMAS   | `sigmas` 参数代表要被分割的 sigma 值序列。它对于确定分割点和结果的两个 sigma 值序列至关重要，影响节点的执行和结果。 |
| `step`   | INT      | `step` 参数指定应在何处分割 sigma 序列。它在定义两个结果 sigma 序列之间的边界时起着关键作用，影响节点的功能和输出的特性。 |

## 输出

| 参数名称 | 数据类型 | 作用                                                         |
|----------|----------|--------------------------------------------------------------|
| `sigmas` | SIGMAS   | 节点输出两个 sigma 值序列，每个序列代表在指定步骤处分割的原始序列的一部分。这些输出对于需要对 sigma 值进行差异化处理的后续操作至关重要。 |
