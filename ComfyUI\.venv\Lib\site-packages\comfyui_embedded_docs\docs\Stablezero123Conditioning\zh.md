此节点设计用于处理和为StableZero123模型使用的条件数据，专注于以特定格式准备输入，这些格式与这些模型兼容并经过优化。

## 输入

| 参数名称 | 数据类型 | 作用 |
| --- | --- | --- |
| `clip_vision` | `CLIP_VISION` | 处理视觉数据以符合模型要求，增强模型对视觉上下文的理解。 |
| `init_image` | `IMAGE` | 作为模型的初始图像输入，为进一步基于图像的操作设定基线。 |
| `vae` | `VAE` | 集成变分自编码器输出，促进模型生成或修改图像的能力。 |
| `width` | `INT` | 指定输出图像的宽度，允许根据模型需求动态调整大小。 |
| `height` | `INT` | 确定输出图像的高度，实现输出尺寸的定制化。 |
| `batch_size` | `INT` | 控制单批次处理的图像数量，优化计算效率。 |
| `elevation` | `FLOAT` | 调整3D模型渲染的仰角，增强模型的空间理解。 |
| `azimuth` | `FLOAT` | 修改3D模型可视化的方位角，改善模型对方向的感知。 |

## 输出

| 参数名称 | 数据类型 | 作用 |
| --- | --- | --- |
| `positive` | `CONDITIONING` | 生成正面条件向量，帮助模型加强正面特征。 |
| `negative` | `CONDITIONING` | 产生负面条件向量，协助模型避免某些特征。 |
| `latent` | `LATENT` | 创建潜在表示，促进模型对数据的深入理解。 |
