
<PhotoProvider>
      <PhotoView src="/conditioning/style_model/Apply_Style_model.jpg">
        <img src="/conditioning/style_model/Apply_Style_model.jpg" alt="comfyUIノード-Apply Style model | スタイルモデル適用" className='rounded-lg' priority/>
      </PhotoView>
</PhotoProvider>

このノードは、CLIPビジョンモデルの出力に基づいてスタイルモデルを適用し、与えられたコンディショニングのスタイルを強化または変更します。スタイルモデルのコンディショニングを既存のコンディショニングに統合し、生成プロセスでスタイルをシームレスにブレンドすることができます。

## 入力

### 必須

| パラメータ             | Comfy dtype          | 説明 |
|-----------------------|-----------------------|-------------|
| `conditioning`        | `CONDITIONING`       | スタイルモデルのコンディショニングが適用される元のコンディショニングデータです。強化または変更される基本的なコンテキストやスタイルを定義するために重要です。 |
| `style_model`         | `STYLE_MODEL`        | CLIPビジョンモデルの出力に基づいて新しいコンディショニングを生成するために使用されるスタイルモデルです。適用される新しいスタイルを定義する上で重要な役割を果たします。 |
| `clip_vision_output`  | `CLIP_VISION_OUTPUT` | スタイルモデルが新しいコンディショニングを生成するために使用するCLIPビジョンモデルの出力です。スタイル適用に必要な視覚的コンテキストを提供します。 |

## 出力

| パラメータ            | Comfy dtype           | 説明 |
|----------------------|-----------------------|-------------|
| `conditioning`       | `CONDITIONING`        | スタイルモデルの出力を組み込んだ強化または変更されたコンディショニングです。さらなる処理や生成の準備が整った最終的なスタイル付きコンディショニングを表します。 |
