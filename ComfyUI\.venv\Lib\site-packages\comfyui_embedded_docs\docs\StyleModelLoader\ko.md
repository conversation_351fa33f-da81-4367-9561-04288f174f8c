이 노드는 `ComfyUI/models/style_models` 폴더에 있는 모델을 감지하며,
또한 extra_model_paths.yaml 파일에서 설정한 추가 경로의 모델도 읽어옵니다.
때때로 **ComfyUI 인터페이스를 새로 고침**해야 해당 폴더의 모델 파일을 읽을 수 있습니다.

StyleModelLoader 노드는 지정된 경로에서 스타일 모델을 로드하기 위해 설계되었습니다. 이 노드는 특정 예술적 스타일을 이미지에 적용할 수 있는 스타일 모델을 검색하고 초기화하는 데 중점을 두어, 로드된 스타일 모델을 기반으로 시각적 출력을 사용자 정의할 수 있도록 합니다.

## 입력

| 필수 입력          | 데이터 유형   | Python dtype | 설명                                                                                                                                                                                                              |
| ------------------ | ------------- | ------------ | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `style_model_name` | COMBO[STRING] | str          | 로드할 스타일 모델의 이름을 지정합니다. 이 이름은 사전 정의된 디렉토리 구조 내에서 모델 파일을 찾는 데 사용되며, 사용자 입력 또는 애플리케이션 요구 사항에 따라 다양한 스타일 모델을 동적으로 로드할 수 있습니다. |

## 출력

| 출력          | 데이터 유형 | Python dtype | 설명                                                                                                                                                                             |
| ------------- | ----------- | ------------ | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `style_model` | STYLE_MODEL | StyleModel   | 로드된 스타일 모델을 반환하며, 이미지를 스타일링하는 데 사용할 준비가 되어 있습니다. 이를 통해 다양한 예술적 스타일을 적용하여 시각적 출력을 동적으로 사용자 정의할 수 있습니다. |
