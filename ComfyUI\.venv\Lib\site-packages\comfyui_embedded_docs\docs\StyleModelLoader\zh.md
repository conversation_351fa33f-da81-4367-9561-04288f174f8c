该节点会检测位于 `ComfyUI/models/style_models` 文件夹下的模型，
同时也会读取你在 extra_model_paths.yaml 文件中配置的额外路径的模型，
有时你可能需要 **刷新 ComfyUI 界面** 才能让它读取到对应文件夹下的模型文件

风格模型加载节点旨在从指定路径加载一个风格模型。它专注于检索和初始化可以用于将特定艺术风格应用于图像的风格模型，从而实现基于加载的风格模型定制视觉输出。

## 输入

| 参数名称 | 数据类型 | 作用 |
| --- | --- | --- |
| `style_model_name` | COMBO[STRING] | 指定要加载的风格模型的名称。此名称用于在预定义的目录结构中定位模型文件，允许根据用户输入或应用程序需求动态加载不同的风格模型。 |

## 输出

| 参数名称 | 数据类型 | 作用 |
| --- | --- | --- |
| `style_model` | STYLE_MODEL | 返回加载的风格模型，准备用于将风格应用于图像。这使得通过应用不同的艺术风格动态定制视觉输出成为可能。 |

---
