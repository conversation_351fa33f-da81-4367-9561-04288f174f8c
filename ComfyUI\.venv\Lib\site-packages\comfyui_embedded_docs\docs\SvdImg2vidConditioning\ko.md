
이 노드는 SVD_img2vid 모델에 맞춰 비디오 생성 작업을 위한 조건 데이터를 생성하도록 설계되었습니다. 초기 이미지, 비디오 매개변수 및 VAE 모델을 포함한 다양한 입력을 받아 비디오 프레임 생성을 안내하는 조건 데이터를 생성합니다.

## 입력

| 매개변수             | Comfy dtype        | 설명 |
|----------------------|--------------------|-------------|
| `clip_vision`         | `CLIP_VISION`      | 초기 이미지에서 시각적 특징을 인코딩하는 데 사용되는 CLIP 비전 모델을 나타내며, 비디오 생성 시 이미지의 내용과 맥락을 이해하는 데 중요한 역할을 합니다. |
| `init_image`          | `IMAGE`            | 비디오가 생성될 초기 이미지로, 비디오 생성 과정의 시작점 역할을 합니다. |
| `vae`                 | `VAE`              | 초기 이미지를 잠재 공간으로 인코딩하는 데 사용되는 변분 오토인코더(VAE) 모델로, 일관되고 연속적인 비디오 프레임 생성을 촉진합니다. |
| `width`               | `INT`              | 생성될 비디오 프레임의 원하는 너비로, 비디오 해상도의 맞춤화를 허용합니다. |
| `height`              | `INT`              | 비디오 프레임의 원하는 높이로, 비디오의 종횡비와 해상도를 제어할 수 있습니다. |
| `video_frames`        | `INT`              | 비디오에 대해 생성될 프레임 수를 지정하여 비디오의 길이를 결정합니다. |
| `motion_bucket_id`    | `INT`              | 비디오 생성 시 적용할 모션 유형을 분류하는 식별자로, 역동적이고 매력적인 비디오를 만드는 데 도움을 줍니다. |
| `fps`                 | `INT`              | 비디오의 초당 프레임 수(fps)로, 생성된 비디오의 부드러움과 현실감에 영향을 미칩니다. |
| `augmentation_level`  | `FLOAT`            | 초기 이미지에 적용되는 증강 수준을 제어하는 매개변수로, 생성된 비디오 프레임의 다양성과 변동성에 영향을 미칩니다. |

## 출력

| 매개변수     | Comfy dtype        | 설명 |
|---------------|--------------------|-------------|
| `positive`    | `CONDITIONING`     | 비디오 생성 과정을 원하는 방향으로 안내하기 위한 인코딩된 특징과 매개변수로 구성된 긍정적 조건 데이터입니다. |
| `negative`    | `CONDITIONING`     | 긍정적 조건과 대조를 이루는 부정적 조건 데이터로, 생성된 비디오에서 특정 패턴이나 특징을 피하는 데 사용할 수 있습니다. |
| `latent`      | `LATENT`           | 비디오의 각 프레임에 대해 생성된 잠재 표현으로, 비디오 생성 과정의 기초적인 구성 요소로 작용합니다. |
