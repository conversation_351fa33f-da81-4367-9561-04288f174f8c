Le nœud Terminal Log (Manager) est principalement utilisé pour afficher les informations d'exécution de ComfyUI dans le terminal au sein de l'interface ComfyUI. Pour l'utiliser, vous devez définir le `mode` sur le mode **logging**. Cela lui permettra d'enregistrer les informations de journal correspondantes pendant la tâche de génération d'image. Si le `mode` est défini sur le mode **stop**, il n'enregistrera pas les informations de journal. Lorsque vous accédez et utilisez ComfyUI via des connexions à distance ou des connexions réseau local, le nœud Terminal Log (Manager) devient particulièrement utile. Il vous permet de visualiser directement les messages d'erreur du CMD au sein de l'interface ComfyUI, facilitant ainsi la compréhension de l'état actuel du fonctionnement de ComfyUI.
