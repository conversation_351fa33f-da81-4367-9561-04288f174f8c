이 노드는 `ComfyUI/models/upscale_models` 폴더에 있는 모델을 감지하며, 또한 extra_model_paths.yaml 파일에서 설정한 추가 경로의 모델도 읽어옵니다. 때때로 **ComfyUI 인터페이스를 새로 고침**해야 해당 폴더의 모델 파일을 읽을 수 있습니다.

UpscaleModelLoader 노드는 지정된 디렉토리에서 업스케일 모델을 로드하도록 설계되었습니다. 이 노드는 이미지 업스케일링 작업을 위한 업스케일 모델의 검색 및 준비를 용이하게 하며, 모델이 평가를 위해 올바르게 로드되고 구성되었는지 확인합니다.

## 입력

| 필드          | Comfy dtype       | 설명                                                                       |
|----------------|-------------------|-----------------------------------------------------------------------------------|
| `model_name`   | `COMBO[STRING]`    | 로드할 업스케일 모델의 이름을 지정하여 업스케일 모델 디렉토리에서 올바른 모델 파일을 식별하고 검색합니다. |

## 출력

| 필드            | Comfy dtype         | 설명                                                              |
|-------------------|---------------------|--------------------------------------------------------------------------|
| `upscale_model`  | `UPSCALE_MODEL`     | 로드되고 준비된 업스케일 모델을 반환하여 이미지 업스케일링 작업에 사용할 준비가 되어 있습니다. |
