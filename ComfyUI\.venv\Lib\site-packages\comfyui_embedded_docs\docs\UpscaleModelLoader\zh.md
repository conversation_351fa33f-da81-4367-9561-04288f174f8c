该节点会检测位于 `ComfyUI/models/upscale_models` 文件夹下的模型，
同时也会读取你在 extra_model_paths.yaml 文件中配置的额外路径的模型，
有时你可能需要 **刷新 ComfyUI 界面** 才能让它读取到对应文件夹下的模型文件

放大模型加载节点旨在从指定目录加载放大模型。它便于检索和准备放大模型以用于图像放大任务，确保模型被正确加载和配置以进行评估。

## 输入

| 参数名称 | 数据类型 | 作用 |
| --- | --- | --- |
| `model_name` | COMBO[STRING] | 指定要加载的放大模型的名称。此参数对于从放大模型目录中识别和检索正确的模型文件至关重要。 |

## 输出

| 参数名称 | 数据类型 | 作用 |
| --- | --- | --- |
| `upscale_model` | UPSCALE_MODEL | 返回已加载和准备好的放大模型，准备用于图像放大任务。 |
