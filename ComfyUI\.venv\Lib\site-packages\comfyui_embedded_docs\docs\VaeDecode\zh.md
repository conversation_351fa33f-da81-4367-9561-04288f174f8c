
VAEDecode节点旨在使用指定的变分自编码器（VAE）将潜在表示解码成图像。它的目的是从压缩的数据表示生成图像，从而促进从它们的潜在空间编码重建图像。

## 输入

| 参数名称 | 数据类型 | 作用                                                         |
|----------|----------|--------------------------------------------------------------|
| `samples` | LATENT   | `samples` 参数代表要解码成图像的潜在表示。它对解码过程至关重要，因为它提供了重建图像的压缩数据。 |
| `vae`     | VAE      | `vae` 参数指定用于将潜在表示解码成图像的变分自编码器模型。它对确定解码机制和重建图像的质量至关重要。 |

## 输出

| 参数名称 | 数据类型 | 作用                                                         |
|----------|----------|--------------------------------------------------------------|
| `image`  | IMAGE    | 输出是使用指定的VAE模型从提供的潜在表示重建的图像。       |
