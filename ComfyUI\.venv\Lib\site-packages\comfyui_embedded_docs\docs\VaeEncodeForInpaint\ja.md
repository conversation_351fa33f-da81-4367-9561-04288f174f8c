
このノードは、インペインティングタスクに適した潜在表現に画像をエンコードするために設計されており、VAEモデルによる最適なエンコードのために入力画像とマスクを調整する追加の前処理ステップを組み込んでいます。

## 入力

| パラメータ | データ型 | 説明 |
|-----------|-------------|-------------|
| `pixels`  | `IMAGE`     | エンコードされる入力画像。この画像は、エンコード前にVAEモデルの期待する入力寸法に合わせて前処理とリサイズが行われます。 |
| `vae`     | VAE       | 画像を潜在表現にエンコードするために使用されるVAEモデル。変換プロセスにおいて重要な役割を果たし、出力される潜在空間の品質と特性を決定します。 |
| `mask`    | `MASK`      | インペインティングされる入力画像の領域を示すマスク。エンコード前に画像を修正し、VAEが関連する領域に焦点を当てることを保証します。 |
| `grow_mask_by` | `INT` | 潜在空間でのシームレスな遷移を確保するためにインペインティングマスクをどれだけ拡張するかを指定します。大きな値はインペインティングの影響を受ける領域を増加させます。 |

## 出力

| パラメータ | データ型 | 説明 |
|-----------|-------------|-------------|
| `latent`  | `LATENT`    | 出力には、画像のエンコードされた潜在表現とノイズマスクが含まれており、どちらも後続のインペインティングタスクにとって重要です。 |
