
このノードは、指定されたVAEモデルを使用して画像を潜在空間表現にエンコードするために設計されています。エンコードプロセスの複雑さを抽象化し、画像を潜在表現に変換する簡単な方法を提供します。

## 入力

| パラメータ | データ型 | 説明 |
|-----------|-------------|-------------|
| `pixels`  | `IMAGE`     | 'pixels'パラメータは、潜在空間にエンコードされる画像データを表します。これは、エンコードプロセスの直接入力として機能し、出力される潜在表現を決定する上で重要な役割を果たします。 |
| `vae`     | VAE       | 'vae'パラメータは、画像データを潜在空間にエンコードするために使用されるVariational Autoencoderモデルを指定します。生成される潜在表現のエンコードメカニズムと特性を定義するために不可欠です。 |

## 出力

| パラメータ | データ型 | 説明 |
|-----------|-------------|-------------|
| `latent`  | `LATENT`    | 出力は、入力画像の潜在空間表現であり、その本質的な特徴を圧縮された形でカプセル化しています。 |
