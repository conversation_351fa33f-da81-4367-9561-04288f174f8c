该节点会检测位于 `ComfyUI/models/vae` 文件夹下的模型，
同时也会读取你在 extra_model_paths.yaml 文件中配置的额外路径的模型，
有时你可能需要 **刷新 ComfyUI 界面** 才能让它读取到对应文件夹下的模型文件

变分自编码器（VAE）加载节点专门用于加载变分自编码器模型，特别适用于处理标准和近似变分自编码器。它支持通过名称加载VAE，包括对 'taesd' 和 'taesdxl' 模型的专门处理，并根据VAE的特定配置动态调整。

## 输入

| 参数名称 | 数据类型 | 作用 |
| --- | --- | --- |
| `vae_name` | COMBO[STRING] | 指定要加载的VAE的名称。此参数决定了将获取和加载哪个VAE模型，支持一系列预定义的VAE名称，包括 'taesd' 和 'taesdxl'。 |

## 输出

| 参数名称 | 数据类型 | 作用 |
| --- | --- | --- |
| `vae` | VAE | 返回加载的VAE模型，准备进行进一步的操作，如编码或解码。输出是一个模型对象，封装了加载模型的状态。 |

---
