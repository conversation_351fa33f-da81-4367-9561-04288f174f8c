
VAESave 노드는 VAE 모델과 그 메타데이터(프롬프트 및 추가 PNG 정보 포함)를 지정된 출력 디렉토리에 저장하도록 설계되었습니다. 이 노드는 모델 상태와 관련 정보를 파일로 직렬화하여 훈련된 모델의 보존 및 공유를 용이하게 합니다.

## 입력

| 매개변수          | 데이터 유형 | 설명                                                                                                            |
| ----------------- | ----------- | --------------------------------------------------------------------------------------------------------------- |
| `vae`             | VAE         | 저장할 VAE 모델입니다. 이 매개변수는 직렬화 및 저장할 모델의 상태를 나타내므로 중요합니다.                      |
| `filename_prefix` | STRING      | 모델 및 메타데이터가 저장될 파일 이름의 접두사입니다. 이는 체계적인 저장 및 모델의 쉬운 검색을 가능하게 합니다. |

## 출력

이 노드는 출력 유형이 없습니다.
