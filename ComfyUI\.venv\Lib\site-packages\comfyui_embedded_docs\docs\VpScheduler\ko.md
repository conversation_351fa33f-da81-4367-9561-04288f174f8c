
VPScheduler 노드는 Variance Preserving (VP) 스케줄링 방법에 기반하여 노이즈 레벨(시그마)의 시퀀스를 생성하도록 설계되었습니다. 이 시퀀스는 확산 모델에서 디노이징 과정을 안내하는 데 필수적이며, 이미지나 다른 데이터 유형의 제어된 생성을 가능하게 합니다.

## 입력

| 매개변수   | 데이터 유형 | 설명                                                                                       |
| ---------- | ----------- | ------------------------------------------------------------------------------------------ |
| `steps`    | INT         | 확산 과정에서의 단계 수를 지정하여 생성된 노이즈 레벨의 세분성을 결정합니다.               |
| `beta_d`   | FLOAT       | 전체 노이즈 레벨 분포를 결정하여 생성된 노이즈 레벨의 분산에 영향을 미칩니다.              |
| `beta_min` | FLOAT       | 노이즈 레벨의 최소 경계를 설정하여 노이즈가 특정 임계값 이하로 떨어지지 않도록 보장합니다. |
| `eps_s`    | FLOAT       | 시작 엡실론 값을 조정하여 확산 과정에서 초기 노이즈 레벨을 미세 조정합니다.                |

## 출력

| 매개변수 | 데이터 유형 | 설명                                                                                                                     |
| -------- | ----------- | ------------------------------------------------------------------------------------------------------------------------ |
| `sigmas` | SIGMAS      | VP 스케줄링 방법에 기반하여 생성된 노이즈 레벨(시그마)의 시퀀스로, 확산 모델에서 디노이징 과정을 안내하는 데 사용됩니다. |
