{"id": "242a6140-7341-49ca-876b-c01366b39b84", "revision": 0, "last_node_id": 38, "last_link_id": 52, "nodes": [{"id": 10, "type": "CLIPLoader", "pos": [100, 300], "size": [270, 106], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP", "type": "CLIP", "links": [34, 35]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.40", "Node name for S&R": "CLIPLoader", "models": [{"name": "oldt5_xxl_fp8_e4m3fn_scaled.safetensors", "url": "https://huggingface.co/comfyanonymous/cosmos_1.0_text_encoder_and_VAE_ComfyUI/resolve/main/text_encoders/oldt5_xxl_fp8_e4m3fn_scaled.safetensors", "directory": "text_encoders"}]}, "widgets_values": ["oldt5_xxl_fp8_e4m3fn_scaled.safetensors", "cosmos", "default"], "color": "#322", "bgcolor": "#533"}, {"id": 15, "type": "VAELoader", "pos": [100, 450], "size": [270, 58], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "links": [17, 47]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.40", "Node name for S&R": "VAELoader", "models": [{"name": "wan_2.1_vae.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/vae/wan_2.1_vae.safetensors", "directory": "vae"}]}, "widgets_values": ["wan_2.1_vae.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 7, "type": "CLIPTextEncode", "pos": [420, 390], "size": [420, 120], "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 34}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [6]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.40", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": [""], "color": "#223", "bgcolor": "#335"}, {"id": 3, "type": "K<PERSON><PERSON><PERSON>", "pos": [880, 150], "size": [315, 262], "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 33}, {"name": "positive", "type": "CONDITIONING", "link": 4}, {"name": "negative", "type": "CONDITIONING", "link": 6}, {"name": "latent_image", "type": "LATENT", "link": 48}], "outputs": [{"name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [7]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.40", "Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [1084135958981475, "randomize", 35, 4, "euler", "karras", 1]}, {"id": 8, "type": "VAEDecode", "pos": [890, 450], "size": [210, 46], "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 7}, {"name": "vae", "type": "VAE", "link": 17}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [51]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.40", "Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 37, "type": "CreateVideo", "pos": [1220, 140], "size": [270, 78], "flags": {}, "order": 11, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 51}, {"name": "audio", "shape": 7, "type": "AUDIO", "link": null}], "outputs": [{"name": "VIDEO", "type": "VIDEO", "links": [52]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.40", "Node name for S&R": "CreateVideo"}, "widgets_values": [16]}, {"id": 36, "type": "LoadImage", "pos": [470, 630], "size": [274.080078125, 314], "flags": {}, "order": 2, "mode": 4, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [49]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.40", "Node name for S&R": "LoadImage"}, "widgets_values": ["end_image.png", "image"]}, {"id": 34, "type": "CosmosPredict2ImageToVideoLatent", "pos": [880, 540], "size": [320, 170], "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "vae", "type": "VAE", "link": 47}, {"name": "start_image", "shape": 7, "type": "IMAGE", "link": 50}, {"name": "end_image", "shape": 7, "type": "IMAGE", "link": 49}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [48]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.40", "Node name for S&R": "CosmosPredict2ImageToVideoLatent"}, "widgets_values": [848, 480, 93, 1]}, {"id": 6, "type": "CLIPTextEncode", "pos": [420, 180], "size": [422.84503173828125, 164.31304931640625], "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 35}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [4]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.40", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["The night of the city, from the perspective of the car's front window, real shooting, real scene, late-night emo style, emotional, sad, neon lights, sense of the future, rain, cinematic feel, cyberpunk\n"], "color": "#232", "bgcolor": "#353"}, {"id": 38, "type": "SaveVideo", "pos": [1220, 260], "size": [700, 690], "flags": {}, "order": 12, "mode": 0, "inputs": [{"name": "video", "type": "VIDEO", "link": 52}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.40", "Node name for S&R": "SaveVideo"}, "widgets_values": ["video/ComfyUI", "auto", "auto"]}, {"id": 13, "type": "UNETLoader", "pos": [100, 180], "size": [280, 82], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [33]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.40", "Node name for S&R": "UNETLoader", "models": [{"name": "cosmos_predict2_2B_video2world_480p_16fps.safetensors", "url": "https://huggingface.co/Comfy-Org/Cosmos_Predict2_repackaged/resolve/main/cosmos_predict2_2B_video2world_480p_16fps.safetensors", "directory": "diffusion_models"}]}, "widgets_values": ["cosmos_predict2_2B_video2world_480p_16fps.safetensors", "default"], "color": "#322", "bgcolor": "#533"}, {"id": 35, "type": "LoadImage", "pos": [130, 630], "size": [274.080078125, 314], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [50]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.40", "Node name for S&R": "LoadImage"}, "widgets_values": ["input.png", "image"]}, {"id": 33, "type": "<PERSON>downNote", "pos": [-360, 140], "size": [429.9635009765625, 282.6522216796875], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [], "title": "Model links", "properties": {}, "widgets_values": ["[Tutorial](http://docs.comfy.org/tutorials/video/cosmos/cosmos-predict2-video2world)｜[教程](http://docs.comfy.org/zh-CN/tutorials/video/cosmos/cosmos-predict2-video2world)\n\n**Diffusion model**\n\n[cosmos_predict2_2B_video2world_480p_16fps.safetensors](https://huggingface.co/Comfy-Org/Cosmos_Predict2_repackaged/resolve/main/cosmos_predict2_2B_video2world_480p_16fps.safetensors)\n\n**Text encoder**\n\n[oldt5_xxl_fp8_e4m3fn_scaled.safetensors](https://huggingface.co/comfyanonymous/cosmos_1.0_text_encoder_and_VAE_ComfyUI/resolve/main/text_encoders/oldt5_xxl_fp8_e4m3fn_scaled.safetensors)\n\n**VAE**\n\n[wan_2.1_vae.safetensors](https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/vae/wan_2.1_vae.safetensors)\n\n\n```\nComfyUI/\n├── models/\n│   ├── diffusion_models/\n│   │   └─── cosmos_predict2_2B_t2i.pt \n│   ├── text_encoders/\n│   │   └─── oldt5_xxl_fp8_e4m3fn_scaled.safetensors\n│   └── vae/\n│       └──  wan_2.1_vae.safetensors\n```"], "color": "#432", "bgcolor": "#653"}], "links": [[4, 6, 0, 3, 1, "CONDITIONING"], [6, 7, 0, 3, 2, "CONDITIONING"], [7, 3, 0, 8, 0, "LATENT"], [17, 15, 0, 8, 1, "VAE"], [33, 13, 0, 3, 0, "MODEL"], [34, 10, 0, 7, 0, "CLIP"], [35, 10, 0, 6, 0, "CLIP"], [47, 15, 0, 34, 0, "VAE"], [48, 34, 0, 3, 3, "LATENT"], [49, 36, 0, 34, 2, "IMAGE"], [50, 35, 0, 34, 1, "IMAGE"], [51, 8, 0, 37, 0, "IMAGE"], [52, 37, 0, 38, 0, "VIDEO"]], "groups": [{"id": 1, "title": "Step1 - Load models here", "bounding": [90, 110, 300.74005126953125, 412.10009765625], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 2, "title": "Step 2 - Upload start and end images", "bounding": [90, 540, 760, 430], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 3, "title": "Step3 - Prompt", "bounding": [410, 110, 440, 410], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.5284090828668973, "offset": [1006.4800869650588, 259.9759282958659]}, "frontendVersion": "1.21.7", "VHS_latentpreview": false, "VHS_latentpreviewrate": 0, "VHS_MetadataImage": true, "VHS_KeepIntermediate": true}, "version": 0.4}