{"id": "175503e4-8e20-492f-9a34-526ba0955381", "revision": 0, "last_node_id": 62, "last_link_id": 130, "nodes": [{"id": 49, "type": "CLIPVisionLoader", "pos": [-167.44577026367188, 549.6415405273438], "size": [373.4332275390625, 59.70098876953125], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP_VISION", "type": "CLIP_VISION", "slot_index": 0, "links": [94]}], "properties": {"Node name for S&R": "CLIPVisionLoader", "models": [{"name": "clip_vision_h.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/clip_vision/clip_vision_h.safetensors", "directory": "clip_vision"}]}, "widgets_values": ["clip_vision_h.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 39, "type": "VAELoader", "pos": [-167.44577026367188, 436.0390625], "size": [375.7519226074219, 58.68564987182617], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "slot_index": 0, "links": [76, 117]}], "properties": {"Node name for S&R": "VAELoader", "models": [{"name": "wan_2.1_vae.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/vae/wan_2.1_vae.safetensors", "directory": "vae"}]}, "widgets_values": ["wan_2.1_vae.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 38, "type": "CLIPLoader", "pos": [-167.44577026367188, 285.86041259765625], "size": [377.0605163574219, 106], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP", "type": "CLIP", "slot_index": 0, "links": [74, 75]}], "properties": {"Node name for S&R": "CLIPLoader", "models": [{"name": "umt5_xxl_fp8_e4m3fn_scaled.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/text_encoders/umt5_xxl_fp8_e4m3fn_scaled.safetensors", "directory": "text_encoders"}]}, "widgets_values": ["umt5_xxl_fp8_e4m3fn_scaled.safetensors", "wan", "default"], "color": "#322", "bgcolor": "#533"}, {"id": 37, "type": "UNETLoader", "pos": [-167.44577026367188, 157.3340301513672], "size": [376.1029357910156, 82], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [110]}], "properties": {"Node name for S&R": "UNETLoader", "models": [{"name": "wan2.1_fun_camera_v1.1_1.3B_bf16.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/diffusion_models/wan2.1_fun_camera_v1.1_1.3B_bf16.safetensors", "directory": "diffusion_models"}]}, "widgets_values": ["wan2.1_fun_camera_v1.1_1.3B_bf16.safetensors", "default"], "color": "#322", "bgcolor": "#533"}, {"id": 52, "type": "LoadImage", "pos": [-170, 710], "size": [366.6621398925781, 326], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [109, 114]}, {"name": "MASK", "type": "MASK", "slot_index": 1, "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["input.webp", "image"], "color": "#322", "bgcolor": "#533"}, {"id": 54, "type": "ModelSamplingSD3", "pos": [739.6448364257812, 143.84304809570312], "size": [315, 58], "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 110}], "outputs": [{"name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [111]}], "properties": {"Node name for S&R": "ModelSamplingSD3"}, "widgets_values": [8]}, {"id": 3, "type": "K<PERSON><PERSON><PERSON>", "pos": [746.4344482421875, 246.2737579345703], "size": [315, 262], "flags": {}, "order": 12, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 111}, {"name": "positive", "type": "CONDITIONING", "link": 118}, {"name": "negative", "type": "CONDITIONING", "link": 119}, {"name": "latent_image", "type": "LATENT", "link": 120}], "outputs": [{"name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [35]}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [987948718394762, "fixed", 20, 6, "uni_pc", "simple", 1]}, {"id": 51, "type": "CLIPVisionEncode", "pos": [749.5863037109375, 835.120361328125], "size": [304.77197265625, 78.06840515136719], "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "clip_vision", "type": "CLIP_VISION", "link": 94}, {"name": "image", "type": "IMAGE", "link": 109}], "outputs": [{"name": "CLIP_VISION_OUTPUT", "type": "CLIP_VISION_OUTPUT", "slot_index": 0, "links": [113]}], "properties": {"Node name for S&R": "CLIPVisionEncode"}, "widgets_values": ["none"]}, {"id": 58, "type": "CreateVideo", "pos": [1344.8922119140625, 148.6174774169922], "size": [210, 78], "flags": {}, "order": 14, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 128}, {"name": "audio", "shape": 7, "type": "AUDIO", "link": null}], "outputs": [{"name": "VIDEO", "type": "VIDEO", "links": [129]}], "properties": {"Node name for S&R": "CreateVideo"}, "widgets_values": [16]}, {"id": 8, "type": "VAEDecode", "pos": [1100.927734375, 148.53817749023438], "size": [210, 46], "flags": {}, "order": 13, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 35}, {"name": "vae", "type": "VAE", "link": 76}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [128, 130]}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 60, "type": "SaveAnimatedWEBP", "pos": [1802.4578857421875, 163.4488525390625], "size": [270, 154], "flags": {}, "order": 15, "mode": 4, "inputs": [{"name": "images", "type": "IMAGE", "link": 130}], "outputs": [], "properties": {}, "widgets_values": ["ComfyUI", 6, true, 80, "default"]}, {"id": 7, "type": "CLIPTextEncode", "pos": [255.7493896484375, 420.1629943847656], "size": [425.27801513671875, 180.6060791015625], "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 75}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [116]}], "title": "CLIP Text Encode (Negative Prompt)", "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["色调艳丽，过曝，静态，细节模糊不清，字幕，风格，作品，画作，画面，静止，整体发灰，最差质量，低质量，JPEG压缩残留，丑陋的，残缺的，多余的手指，画得不好的手部，画得不好的脸部，畸形的，毁容的，形态畸形的肢体，手指融合，静止不动的画面，杂乱的背景，三条腿，背景人很多，倒着走"], "color": "#322", "bgcolor": "#533"}, {"id": 59, "type": "SaveVideo", "pos": [1092.015869140625, 268.6248779296875], "size": [677.1019897460938, 775.1019897460938], "flags": {}, "order": 16, "mode": 0, "inputs": [{"name": "video", "type": "VIDEO", "link": 129}], "outputs": [], "properties": {"Node name for S&R": "SaveVideo"}, "widgets_values": ["video/ComfyUI", "auto", "auto"]}, {"id": 57, "type": "WanCamera<PERSON>dding", "pos": [250, 710], "size": [272.0084228515625, 322.0967102050781], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [{"name": "camera_embedding", "type": "WAN_CAMERA_EMBEDDING", "links": [124]}, {"name": "width", "type": "INT", "links": [125]}, {"name": "height", "type": "INT", "links": [126]}, {"name": "length", "type": "INT", "links": [127]}], "properties": {"Node name for S&R": "WanCamera<PERSON>dding"}, "widgets_values": ["Zoom In", 512, 512, 81, 1, 0.5, 0.5, 0.5, 0.5]}, {"id": 6, "type": "CLIPTextEncode", "pos": [250.68882751464844, 158.49562072753906], "size": [424.43255615234375, 204.60206604003906], "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 74}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [115]}], "title": "CLIP Text Encode (Positive Prompt)", "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["Origami-style Chinese landscapes, miniature landscapes, bridges, waterfalls"], "color": "#232", "bgcolor": "#353"}, {"id": 56, "type": "WanCameraImageToVideo", "pos": [748.0342407226562, 555.3203125], "size": [305.7822265625, 232.4929656982422], "flags": {}, "order": 11, "mode": 0, "inputs": [{"name": "positive", "type": "CONDITIONING", "link": 115}, {"name": "negative", "type": "CONDITIONING", "link": 116}, {"name": "vae", "type": "VAE", "link": 117}, {"name": "clip_vision_output", "shape": 7, "type": "CLIP_VISION_OUTPUT", "link": 113}, {"name": "start_image", "shape": 7, "type": "IMAGE", "link": 114}, {"name": "camera_conditions", "shape": 7, "type": "WAN_CAMERA_EMBEDDING", "link": 124}, {"name": "width", "type": "INT", "widget": {"name": "width"}, "link": 125}, {"name": "height", "type": "INT", "widget": {"name": "height"}, "link": 126}, {"name": "length", "type": "INT", "widget": {"name": "length"}, "link": 127}], "outputs": [{"name": "positive", "type": "CONDITIONING", "links": [118]}, {"name": "negative", "type": "CONDITIONING", "links": [119]}, {"name": "latent", "type": "LATENT", "links": [120]}], "properties": {"Node name for S&R": "WanCameraImageToVideo"}, "widgets_values": [832, 480, 81, 1]}, {"id": 62, "type": "<PERSON>downNote", "pos": [-680, 110], "size": [480, 410], "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [], "title": "Model Links", "properties": {}, "widgets_values": ["[Tutorial](http://docs.comfy.org/tutorials/video/wan/fun-camera) | [教程](http://docs.comfy.org/zh-CN/tutorials/video/wan/fun-camera)\n\n**Diffusion Model**\n- [wan2.1_fun_camera_v1.1_1.3B_bf16.safetensors](https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/diffusion_models/wan2.1_fun_camera_v1.1_1.3B_bf16.safetensors)\n\n**VAE**\n- [wan_2.1_vae.safetensors](https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/vae/wan_2.1_vae.safetensors?download=true)\n\n**Text encoders**   Chose one of following model\n- [umt5_xxl_fp16.safetensors](https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/text_encoders/umt5_xxl_fp16.safetensors?download=true)\n- [umt5_xxl_fp8_e4m3fn_scaled.safetensors](https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/text_encoders/umt5_xxl_fp8_e4m3fn_scaled.safetensors?download=true)\n\n\n**clip_vision**\n- [clip_vision_h.safetensors](https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/clip_vision/clip_vision_h.safetensors)\n\nFile save location\n\n```\nComfyUI/\n├───📂 models/\n│   ├───📂 diffusion_models/\n│   │   └───wan2.1_fun_camera_v1.1_1.3B_bf16.safetensors\n│   ├───📂text_encoders/\n│   │   └─── umt5_xxl_fp8_e4m3fn_scaled.safetensors # or other version\n│   └───📂vae/\n│         └──  wan_2.1_vae.safetensors\n```\n"], "color": "#432", "bgcolor": "#653"}], "links": [[35, 3, 0, 8, 0, "LATENT"], [74, 38, 0, 6, 0, "CLIP"], [75, 38, 0, 7, 0, "CLIP"], [76, 39, 0, 8, 1, "VAE"], [94, 49, 0, 51, 0, "CLIP_VISION"], [109, 52, 0, 51, 1, "IMAGE"], [110, 37, 0, 54, 0, "MODEL"], [111, 54, 0, 3, 0, "MODEL"], [113, 51, 0, 56, 3, "CLIP_VISION_OUTPUT"], [114, 52, 0, 56, 4, "IMAGE"], [115, 6, 0, 56, 0, "CONDITIONING"], [116, 7, 0, 56, 1, "CONDITIONING"], [117, 39, 0, 56, 2, "VAE"], [118, 56, 0, 3, 1, "CONDITIONING"], [119, 56, 1, 3, 2, "CONDITIONING"], [120, 56, 2, 3, 3, "LATENT"], [124, 57, 0, 56, 5, "WAN_CAMERA_EMBEDDING"], [125, 57, 1, 56, 6, "INT"], [126, 57, 2, 56, 7, "INT"], [127, 57, 3, 56, 8, "INT"], [128, 8, 0, 58, 0, "IMAGE"], [129, 58, 0, 59, 0, "VIDEO"], [130, 8, 0, 60, 0, "IMAGE"]], "groups": [{"id": 1, "title": "Step 1 - Load Models Here", "bounding": [-180, 80, 400, 540], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 2, "title": "Step2 - Start Image", "bounding": [-180, 640, 400, 420], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 3, "title": "Step 3 - Prompt", "bounding": [240, 80, 450, 540], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 4, "title": "Step 4 - Camera Settings", "bounding": [240, 640, 300, 420], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.7627768444385934, "offset": [770.4342568105058, 115.41062135371325]}, "frontendVersion": "1.22.1"}, "version": 0.4}