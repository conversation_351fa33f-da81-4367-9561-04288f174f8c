{"id": "96995b8f-85c5-47af-b3cf-7b6a24675694", "revision": 0, "last_node_id": 188, "last_link_id": 284, "nodes": [{"id": 68, "type": "CreateVideo", "pos": [790, 60], "size": [270, 78], "flags": {"collapsed": false}, "order": 38, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 139}, {"name": "audio", "shape": 7, "type": "AUDIO", "link": null}], "outputs": [{"name": "VIDEO", "type": "VIDEO", "links": [129]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "CreateVideo"}, "widgets_values": [16]}, {"id": 70, "type": "SaveAnimatedWEBP", "pos": [1550, 60], "size": [670, 900], "flags": {}, "order": 37, "mode": 4, "inputs": [{"name": "images", "type": "IMAGE", "link": 130}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "SaveAnimatedWEBP"}, "widgets_values": ["ComfyUI", 6, true, 80, "default"]}, {"id": 48, "type": "ModelSamplingSD3", "pos": [50, 60], "size": [315, 58], "flags": {}, "order": 23, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 283}], "outputs": [{"name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [95]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "ModelSamplingSD3"}, "widgets_values": [8.000000000000002]}, {"id": 148, "type": "MaskToImage", "pos": [-480, 810], "size": [270, 26], "flags": {}, "order": 20, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 221}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [222]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "MaskToImage"}, "widgets_values": []}, {"id": 58, "type": "TrimVideoLatent", "pos": [410, 400], "size": [315, 60], "flags": {"collapsed": false}, "order": 35, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 116}, {"name": "trim_amount", "type": "INT", "widget": {"name": "trim_amount"}, "link": 115}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [117]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "TrimVideoLatent"}, "widgets_values": [0]}, {"id": 8, "type": "VAEDecode", "pos": [410, 510], "size": [315, 46], "flags": {"collapsed": false}, "order": 36, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 117}, {"name": "vae", "type": "VAE", "link": 76}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [130, 139]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 131, "type": "PrimitiveInt", "pos": [-1230, 710], "size": [310, 90], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "INT", "type": "INT", "links": [209]}], "title": "Length", "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "PrimitiveInt"}, "widgets_values": [81, "fixed"], "color": "#322", "bgcolor": "#533"}, {"id": 7, "type": "CLIPTextEncode", "pos": [-430, 400], "size": [425.27801513671875, 180.6060791015625], "flags": {}, "order": 22, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 274}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [97]}], "title": "CLIP Text Encode (Negative Prompt)", "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["过曝，静态，细节模糊不清，字幕，风格，作品，画作，画面，静止，整体发灰，最差质量，低质量，JPEG压缩残留，丑陋的，残缺的，多余的手指，画得不好的手部，画得不好的脸部，畸形的，毁容的，形态畸形的肢体，手指融合，静止不动的画面，杂乱的背景，三条腿，背景人很多，倒着走,过曝，"], "color": "#223", "bgcolor": "#335"}, {"id": 150, "type": "ImageBatch", "pos": [-160, 810], "size": [140, 46], "flags": {}, "order": 26, "mode": 0, "inputs": [{"name": "image1", "type": "IMAGE", "link": 259}, {"name": "image2", "type": "IMAGE", "link": 224}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [219]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "ImageBatch"}, "widgets_values": []}, {"id": 145, "type": "ImageBatch", "pos": [-160, 700], "size": [140, 46], "flags": {}, "order": 28, "mode": 0, "inputs": [{"name": "image1", "type": "IMAGE", "link": 219}, {"name": "image2", "type": "IMAGE", "link": 260}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [225, 226]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "ImageBatch"}, "widgets_values": []}, {"id": 149, "type": "RepeatImageBatch", "pos": [-480, 700], "size": [270, 58], "flags": {}, "order": 25, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 222}, {"name": "amount", "type": "INT", "widget": {"name": "amount"}, "link": 257}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [224, 261]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "RepeatImageBatch"}, "widgets_values": [30]}, {"id": 153, "type": "ImageBatch", "pos": [440, 820], "size": [140, 46], "flags": {}, "order": 27, "mode": 0, "inputs": [{"name": "image1", "type": "IMAGE", "link": 230}, {"name": "image2", "type": "IMAGE", "link": 261}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [232]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "ImageBatch"}, "widgets_values": []}, {"id": 155, "type": "ImageBatch", "pos": [440, 918], "size": [140, 46], "flags": {}, "order": 29, "mode": 0, "inputs": [{"name": "image1", "type": "IMAGE", "link": 232}, {"name": "image2", "type": "IMAGE", "link": 231}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [233, 235]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "ImageBatch"}, "widgets_values": []}, {"id": 156, "type": "ImageToMask", "pos": [440, 700], "size": [270, 58], "flags": {}, "order": 31, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 233}], "outputs": [{"name": "MASK", "type": "MASK", "links": [234]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "ImageToMask"}, "widgets_values": ["red"]}, {"id": 146, "type": "SolidMask", "pos": [-480, 890], "size": [270, 106], "flags": {}, "order": 16, "mode": 0, "inputs": [{"name": "width", "type": "INT", "widget": {"name": "width"}, "link": 275}, {"name": "height", "type": "INT", "widget": {"name": "height"}, "link": 278}], "outputs": [{"name": "MASK", "type": "MASK", "links": [221]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "SolidMask"}, "widgets_values": [1, 512, 512]}, {"id": 154, "type": "MaskToImage", "pos": [100, 700], "size": [270, 26], "flags": {}, "order": 21, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 229}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [230, 231]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "MaskToImage"}, "widgets_values": []}, {"id": 152, "type": "SolidMask", "pos": [100, 890], "size": [270, 106], "flags": {}, "order": 17, "mode": 0, "inputs": [{"name": "width", "type": "INT", "widget": {"name": "width"}, "link": 276}, {"name": "height", "type": "INT", "widget": {"name": "height"}, "link": 279}], "outputs": [{"name": "MASK", "type": "MASK", "links": [229]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "SolidMask"}, "widgets_values": [0, 512, 512]}, {"id": 157, "type": "PreviewImage", "pos": [100, 1060], "size": [610, 340], "flags": {}, "order": 32, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 235}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 3, "type": "K<PERSON><PERSON><PERSON>", "pos": [410, 60], "size": [315, 262], "flags": {}, "order": 34, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 95}, {"name": "positive", "type": "CONDITIONING", "link": 98}, {"name": "negative", "type": "CONDITIONING", "link": 99}, {"name": "latent_image", "type": "LATENT", "link": 160}], "outputs": [{"name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [116]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [95385376145355, "randomize", 4, 1, "uni_pc", "simple", 1]}, {"id": 39, "type": "VAELoader", "pos": [-840, 530], "size": [350, 58], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "slot_index": 0, "links": [76, 101]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "VAELoader", "models": [{"name": "wan_2.1_vae.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/vae/wan_2.1_vae.safetensors", "directory": "vae"}]}, "widgets_values": ["wan_2.1_vae.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 147, "type": "LoadImage", "pos": [-830, 1100], "size": [274.080078125, 314.00006103515625], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [260]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "LoadImage"}, "widgets_values": ["end.webp", "image"], "color": "#322", "bgcolor": "#533"}, {"id": 176, "type": "Note", "pos": [-510, 1480], "size": [560, 140], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["In fact, VACE supports the generation from any frame to video. You can use the method of creating image and mask sequences in our examples to create sequences containing any frames and their corresponding masks, thus achieving the image generation from any frame to video.\n\n---\n\n实际上 VACE 支持任意帧到视频的生成，你可以使用我们示例中创建图像和蒙版序列的方法来创建包含任意帧的序列和对应的蒙版序列，从而实现任意帧到视频的图像生成"], "color": "#432", "bgcolor": "#653"}, {"id": 184, "type": "CLIPLoader", "pos": [-1280, 400], "size": [370, 106], "flags": {}, "order": 4, "mode": 4, "inputs": [], "outputs": [{"name": "CLIP", "type": "CLIP", "slot_index": 0, "links": []}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "CLIPLoader", "models": [{"name": "umt5_xxl_fp16.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/text_encoders/umt5_xxl_fp16.safetensors", "directory": "text_encoders"}]}, "widgets_values": ["umt5_xxl_fp16.safetensors", "wan", "default"], "color": "#322", "bgcolor": "#533"}, {"id": 185, "type": "PrimitiveNode", "pos": [-1199.02001953125, 1079.8485107421875], "size": [210, 82], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [{"name": "INT", "type": "INT", "widget": {"name": "width"}, "links": [275, 276, 277]}], "title": "<PERSON><PERSON><PERSON>", "properties": {"Run widget replace on values": false}, "widgets_values": [512, "fixed"]}, {"id": 186, "type": "PrimitiveNode", "pos": [-1198.6580810546875, 1219.302490234375], "size": [210, 82.62108612060547], "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [{"name": "INT", "type": "INT", "widget": {"name": "height"}, "links": [278, 279, 280]}], "title": "Height", "properties": {"Run widget replace on values": false}, "widgets_values": [512, "fixed"]}, {"id": 77, "type": "<PERSON>downNote", "pos": [-1830, 10], "size": [490, 800], "flags": {}, "order": 7, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["[Tutorial](https://docs.comfy.org/tutorials/video/wan/vace) | [教程](https://docs.comfy.org/zh-CN/tutorials/video/wan/vace)\n\n[Causvid Lora by <PERSON><PERSON><PERSON>](https://www.reddit.com/r/StableDiffusion/comments/1knuafk/causvid_lora_massive_speedup_for_wan21_made_by/)\n\n## 14B Support 480P 720P\n\n**Diffusion Model**\n- [wan2.1_vace_14B_fp16.safetensors](https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/diffusion_models/wan2.1_vace_14B_fp16.safetensors)\n\n**LoRA**\n- [Wan21_CausVid_14B_T2V_lora_rank32.safetensors](https://huggingface.co/Kijai/WanVideo_comfy/blob/main/Wan21_CausVid_14B_T2V_lora_rank32.safetensors)\n\nFYI: Using an RTX 4090, it takes approximately 40 minutes at 81 frames and 720P resolution.\n\nAfter using Wan21_CausVid_14B_T2V_lora_rank32.safetensors, it only takes about 4 minutes.\n\n## 1.3B Support 480P only\n\n**Diffusion Model**\n- [wan2.1_vace_1.3B_fp16.safetensors](https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/diffusion_models/wan2.1_vace_14B_fp16.safetensors)\n\n**LoRA**\n- [Wan21_CausVid_bidirect2_T2V_1_3B_lora_rank32.safetensors](https://huggingface.co/Kijai/WanVideo_comfy/blob/main/Wan21_CausVid_bidirect2_T2V_1_3B_lora_rank32.safetensors)\n\n## Other Models\n\n* You may already have these models if you use Wan workflow before.\n\n**VAE**\n- [wan_2.1_vae.safetensors](https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/vae/wan_2.1_vae.safetensors?download=true)\n\n**Text encoders**   Chose one of following model\n- [umt5_xxl_fp16.safetensors](https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/text_encoders/umt5_xxl_fp16.safetensors?download=true)\n- [umt5_xxl_fp8_e4m3fn_scaled.safetensors](https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/text_encoders/umt5_xxl_fp8_e4m3fn_scaled.safetensors?download=true)\n\n> You can choose between fp16 of fp8; I used fp16 to match what kijai's wrapper is compatible with.\n\nFile save location\n\n```\nComfyUI/\n├── models/\n│   ├── diffusion_models/\n│   │   ├-── wan2.1_vace_14B_fp16.safetensors\n│   │   └─── wan2.1_vace_1.3B_fp16.safetensors \n│   ├── text_encoders/\n│   │   └─── umt5_xxl_fp8_e4m3fn_scaled.safetensors # or fp16\n│   ├── loras/\n│   │   ├── Wan21_CausVid_14B_T2V_lora_rank32.safetensors\n│   │   └── Wan21_CausVid_bidirect2_T2V_1_3B_lora_rank32.safetensors\n│   └── vae/\n│       └──  wan_2.1_vae.safetensors\n```\n"], "color": "#432", "bgcolor": "#653"}, {"id": 181, "type": "<PERSON>downNote", "pos": [-1480, 1040], "size": [210, 110], "flags": {}, "order": 8, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["| Model                                                         | 480P | 720P |\n| ------------------------------------------------------------ | ---- | ---- |\n| [VACE-1.3B](https://huggingface.co/Wan-AI/Wan2.1-VACE-1.3B) | ✅   | ❌   |\n| [VACE-14B](https://huggingface.co/Wan-AI/Wan2.1-VACE-14B)   | ✅   | ✅   |"], "color": "#432", "bgcolor": "#653"}, {"id": 171, "type": "PrimitiveInt", "pos": [-1240, 900], "size": [310, 90], "flags": {}, "order": 9, "mode": 0, "inputs": [], "outputs": [{"name": "INT", "type": "INT", "links": [257]}], "title": "Length", "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "PrimitiveInt"}, "widgets_values": [82, "increment"], "color": "#322", "bgcolor": "#533"}, {"id": 151, "type": "PreviewImage", "pos": [-480, 1060], "size": [510, 340], "flags": {}, "order": 30, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 225}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 144, "type": "LoadImage", "pos": [-830, 690], "size": [274.080078125, 314.00006103515625], "flags": {}, "order": 10, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [259]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "LoadImage"}, "widgets_values": ["start.webp", "image"], "color": "#322", "bgcolor": "#533"}, {"id": 69, "type": "SaveVideo", "pos": [790, 190], "size": [676.23095703125, 774.23095703125], "flags": {}, "order": 39, "mode": 0, "inputs": [{"name": "video", "type": "VIDEO", "link": 129}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "SaveVideo"}, "widgets_values": ["video/ComfyUI", "auto", "auto"]}, {"id": 49, "type": "WanVaceToVideo", "pos": [50, 180], "size": [315, 254], "flags": {}, "order": 33, "mode": 0, "inputs": [{"name": "positive", "type": "CONDITIONING", "link": 96}, {"name": "negative", "type": "CONDITIONING", "link": 97}, {"name": "vae", "type": "VAE", "link": 101}, {"name": "control_video", "shape": 7, "type": "IMAGE", "link": 226}, {"name": "control_masks", "shape": 7, "type": "MASK", "link": 234}, {"name": "reference_image", "shape": 7, "type": "IMAGE", "link": null}, {"name": "width", "type": "INT", "widget": {"name": "width"}, "link": 277}, {"name": "height", "type": "INT", "widget": {"name": "height"}, "link": 280}, {"name": "length", "type": "INT", "widget": {"name": "length"}, "link": 209}], "outputs": [{"name": "positive", "type": "CONDITIONING", "links": [98]}, {"name": "negative", "type": "CONDITIONING", "links": [99]}, {"name": "latent", "type": "LATENT", "links": [160]}, {"name": "trim_latent", "type": "INT", "links": [115]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "WanVaceToVideo"}, "widgets_values": [512, 512, 45, 1, 1]}, {"id": 38, "type": "CLIPLoader", "pos": [-840, 390], "size": [350, 106], "flags": {}, "order": 11, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP", "type": "CLIP", "slot_index": 0, "links": [272, 282]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "CLIPLoader", "models": [{"name": "umt5_xxl_fp8_e4m3fn_scaled.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/text_encoders/umt5_xxl_fp8_e4m3fn_scaled.safetensors?download=true", "directory": "text_encoders"}]}, "widgets_values": ["umt5_xxl_fp8_e4m3fn_scaled.safetensors", "wan", "default"], "color": "#322", "bgcolor": "#533"}, {"id": 178, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [-1280, 190], "size": [350, 126], "flags": {}, "order": 18, "mode": 4, "inputs": [{"name": "model", "type": "MODEL", "link": 270}, {"name": "clip", "type": "CLIP", "link": 272}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": []}, {"name": "CLIP", "type": "CLIP", "links": [274]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "models": [{"name": "Wan21_CausVid_bidirect2_T2V_1_3B_lora_rank32.safetensors", "url": "https://huggingface.co/Kijai/WanVideo_comfy/resolve/main/Wan21_CausVid_bidirect2_T2V_1_3B_lora_rank32.safetensors", "directory": "loras"}]}, "widgets_values": ["Wan21_CausVid_bidirect2_T2V_1_3B_lora_rank32.safetensors", 0.30000000000000004, 1], "color": "#322", "bgcolor": "#533"}, {"id": 37, "type": "UNETLoader", "pos": [-1280, 60], "size": [350, 82], "flags": {}, "order": 12, "mode": 4, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [270]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "UNETLoader", "models": [{"name": "wan2.1_vace_1.3B_fp16.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/diffusion_models/wan2.1_vace_1.3B_fp16.safetensors", "directory": "diffusion_models"}]}, "widgets_values": ["wan2.1_vace_1.3B_fp16.safetensors", "default"], "color": "#322", "bgcolor": "#533"}, {"id": 179, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [-840, 230], "size": [350, 126], "flags": {}, "order": 19, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 281}, {"name": "clip", "type": "CLIP", "link": 282}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [283]}, {"name": "CLIP", "type": "CLIP", "links": [284]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "models": [{"name": "Wan21_CausVid_14B_T2V_lora_rank32.safetensors", "url": "https://huggingface.co/Kijai/WanVideo_comfy/resolve/main/Wan21_CausVid_14B_T2V_lora_rank32.safetensors", "directory": "loras"}]}, "widgets_values": ["Wan21_CausVid_14B_T2V_lora_rank32.safetensors", 0.30000000000000004, 1], "color": "#322", "bgcolor": "#533"}, {"id": 140, "type": "UNETLoader", "pos": [-840, 100], "size": [350, 90], "flags": {}, "order": 13, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [281]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "UNETLoader", "models": [{"name": "wan2.1_vace_14B_fp16.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/diffusion_models/wan2.1_vace_14B_fp16.safetensors", "directory": "diffusion_models"}]}, "widgets_values": ["wan2.1_vace_14B_fp16.safetensors", "default"], "color": "#322", "bgcolor": "#533"}, {"id": 188, "type": "<PERSON>downNote", "pos": [-870, -180], "size": [410, 140], "flags": {}, "order": 14, "mode": 0, "inputs": [], "outputs": [], "title": "About CausVid LoRA", "properties": {}, "widgets_values": ["We have added CausVid LoRA by default to achieve acceleration. However, in some cases, the video may shake and become blurry. You might need to test different LoRA intensities to get the best results, which should be between 0.3 and 0.7. If you don't need it, you can use the bypass mode to disable it, and then restore the settings of `KSampler` to the default ones.\n\n\n我们默认添加了  CausVid LoRA 来实现加速，但有些情况下会出现视频抖动和模糊的情况，你可能需要测试不同的 LoRA 强度来获取最好的结果，0.3～0.7 之间。如果你不需要的话，可以使用 bypass 模式禁用它，然后恢复 `KSampler`的设置到默认的设置即可。"], "color": "#432", "bgcolor": "#653"}, {"id": 6, "type": "CLIPTextEncode", "pos": [-420, 70], "size": [400, 280], "flags": {}, "order": 24, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 284}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [96]}], "title": "CLIP Text Encode (Positive Prompt)", "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["A girl is dancing in a sea of flowers, slowly moving her hands, stretching her arms. There is a super close - up and a close - up of the upper body. The character is surrounded by other transparent glass flowers in the style of <PERSON><PERSON>, creating a beautiful, surreal, and emotionally expressive movie scene. It has a white and transparent feeling, is dreamy, and in close range. "], "color": "#232", "bgcolor": "#353"}, {"id": 183, "type": "<PERSON>downNote", "pos": [420, -200], "size": [310, 160], "flags": {}, "order": 15, "mode": 0, "inputs": [], "outputs": [], "title": "<PERSON><PERSON><PERSON><PERSON>", "properties": {}, "widgets_values": ["## Default (Without CausVid LoRA)\n\n- steps:20\n- cfg:6.0 \n\n## [For CausVid LoRA](https://www.reddit.com/r/StableDiffusion/comments/1knuafk/causvid_lora_massive_speedup_for_wan21_made_by/)\n\n- steps: 2-4\n- cfg: 1.0\n\n"], "color": "#432", "bgcolor": "#653"}], "links": [[76, 39, 0, 8, 1, "VAE"], [95, 48, 0, 3, 0, "MODEL"], [96, 6, 0, 49, 0, "CONDITIONING"], [97, 7, 0, 49, 1, "CONDITIONING"], [98, 49, 0, 3, 1, "CONDITIONING"], [99, 49, 1, 3, 2, "CONDITIONING"], [101, 39, 0, 49, 2, "VAE"], [115, 49, 3, 58, 1, "INT"], [116, 3, 0, 58, 0, "LATENT"], [117, 58, 0, 8, 0, "LATENT"], [129, 68, 0, 69, 0, "VIDEO"], [130, 8, 0, 70, 0, "IMAGE"], [139, 8, 0, 68, 0, "IMAGE"], [160, 49, 2, 3, 3, "LATENT"], [209, 131, 0, 49, 8, "INT"], [219, 150, 0, 145, 0, "IMAGE"], [221, 146, 0, 148, 0, "MASK"], [222, 148, 0, 149, 0, "IMAGE"], [224, 149, 0, 150, 1, "IMAGE"], [225, 145, 0, 151, 0, "IMAGE"], [226, 145, 0, 49, 3, "IMAGE"], [229, 152, 0, 154, 0, "MASK"], [230, 154, 0, 153, 0, "IMAGE"], [231, 154, 0, 155, 1, "IMAGE"], [232, 153, 0, 155, 0, "IMAGE"], [233, 155, 0, 156, 0, "IMAGE"], [234, 156, 0, 49, 4, "MASK"], [235, 155, 0, 157, 0, "IMAGE"], [257, 171, 0, 149, 1, "INT"], [259, 144, 0, 150, 0, "IMAGE"], [260, 147, 0, 145, 1, "IMAGE"], [261, 149, 0, 153, 1, "IMAGE"], [270, 37, 0, 178, 0, "MODEL"], [272, 38, 0, 178, 1, "CLIP"], [274, 178, 1, 7, 0, "CLIP"], [275, 185, 0, 146, 0, "INT"], [276, 185, 0, 152, 0, "INT"], [277, 185, 0, 49, 6, "INT"], [278, 186, 0, 146, 1, "INT"], [279, 186, 0, 152, 1, "INT"], [280, 186, 0, 49, 7, "INT"], [281, 140, 0, 179, 0, "MODEL"], [282, 38, 0, 179, 1, "CLIP"], [283, 179, 0, 48, 0, "MODEL"], [284, 179, 1, 6, 0, "CLIP"]], "groups": [{"id": 1, "title": "Load models here", "bounding": [-870, -20, 410, 620], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 2, "title": "Prompt", "bounding": [-440, -20, 450, 620], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 3, "title": "Sampling & Decoding", "bounding": [30, -20, 720, 620], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 4, "title": "Save Video(Mp4)", "bounding": [770, -20, 720, 1010], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 5, "title": "Save Video(WebP)", "bounding": [1530, -20, 700, 1010], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 9, "title": "Length - 1 should be divisible by 4", "bounding": [-1250, 620, 370, 200], "color": "#A88", "font_size": 24, "flags": {}}, {"id": 15, "title": "Batch Masks", "bounding": [70, 620, 680, 810], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 16, "title": "Batch Images", "bounding": [-510, 620, 560, 810], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 17, "title": "Video Size", "bounding": [-1250, 1010, 370, 310], "color": "#A88", "font_size": 24, "flags": {}}, {"id": 18, "title": "First Frame", "bounding": [-870, 620, 340, 390], "color": "#A88", "font_size": 24, "flags": {}}, {"id": 19, "title": "Last Frame", "bounding": [-870, 1020, 340, 410], "color": "#A88", "font_size": 24, "flags": {}}, {"id": 20, "title": "<PERSON><PERSON> Size = Length - 2", "bounding": [-1250, 830, 370, 170], "color": "#A88", "font_size": 24, "flags": {}}, {"id": 21, "title": "1.3B", "bounding": [-1310, -20, 420, 360], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 22, "title": "14B", "bounding": [-850, 30, 370, 330], "color": "#A88", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.5054470284993043, "offset": [1793.6487219589956, 397.7060029196524]}, "frontendVersion": "1.20.7", "node_versions": {"comfy-core": "0.3.34"}, "VHS_latentpreview": false, "VHS_latentpreviewrate": 0, "VHS_MetadataImage": true, "VHS_KeepIntermediate": true}, "version": 0.4}