.github/workflows/publish.yml
.gitignore
.gitmodules
LICENSE.txt
README.md
__init__.py
custom_wildcards/put_wildcards_here
disable.py
example_workflows/1-FaceDetailer.jpg
example_workflows/1-FaceDetailer.json
example_workflows/2-MaskDetailer.jpg
example_workflows/2-MaskDetailer.json
example_workflows/3-SEGSDetailer.jpg
example_workflows/3-SEGSDetailer.json
example_workflows/4-MakeTileSEGS-Upscale.jpg
example_workflows/4-MakeTileSEGS-Upscale.json
example_workflows/5-PreviewDetailerHookProvider.jpg
example_workflows/5-PreviewDetailerHookProvider.json
example_workflows/5-prompt-per-tile.jpg
example_workflows/5-prompt-per-tile.json
example_workflows/6-DetailerWildcard.jpg
example_workflows/6-DetailerWildcard.json
install.py
js/common.js
js/impact-image-util.js
js/impact-pack.js
js/impact-sam-editor.js
js/impact-segs-picker.js
js/mask-rect-area-advanced.js
js/mask-rect-area.js
latent.png
locales/ko/nodeDefs.json
modules/impact/additional_dependencies.py
modules/impact/animatediff_nodes.py
modules/impact/bridge_nodes.py
modules/impact/config.py
modules/impact/core.py
modules/impact/defs.py
modules/impact/detectors.py
modules/impact/hf_nodes.py
modules/impact/hook_nodes.py
modules/impact/hooks.py
modules/impact/impact_pack.py
modules/impact/impact_sampling.py
modules/impact/impact_server.py
modules/impact/legacy_nodes.py
modules/impact/logics.py
modules/impact/mmdet_nodes.py
modules/impact/onnx.py
modules/impact/pipe.py
modules/impact/segs_nodes.py
modules/impact/segs_upscaler.py
modules/impact/special_samplers.py
modules/impact/util_nodes.py
modules/impact/utils.py
modules/impact/wildcards.py
modules/thirdparty/noise_nodes.py
node_list.json
notebook/comfyui_colab_impact_pack.ipynb
pyproject.toml
requirements.txt
test/advanced-sampler.json
test/detailer-pipe-test-sdxl.json
test/detailer-pipe-test.json
test/impactwildcardprocessor_separate_tests.json
test/impactwildcardprocessor_yaml_tests.json
test/loop-test.json
test/masks.json
test/regional_prompt.json
troubleshooting/TROUBLESHOOTING.md
troubleshooting/black1.png
troubleshooting/black2.png
uninstall.py
wildcards/put_wildcards_here
wildcards/samples/flower.txt
wildcards/samples/jewel.txt