{"last_node_id": 5, "last_link_id": 5, "nodes": [{"id": 1, "type": "LoadImage", "pos": [30, 210], "size": [390, 320], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "shape": 3, "links": [1]}, {"name": "MASK", "type": "MASK", "shape": 3, "links": [2]}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["clipspace/clipspace-mask-609196.2000000011.png [input]", "image"]}, {"id": 5, "type": "PreviewImage", "pos": [1230, 210], "size": [210, 246], "flags": {}, "order": 3, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 5}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 3, "type": "workflow>Impact::MAKE_BASIC_PIPE", "pos": [20, 620], "size": [400, 200], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "basic_pipe", "type": "BASIC_PIPE", "shape": 3, "links": [3]}], "properties": {"Node name for S&R": "workflow/Impact::MAKE_BASIC_PIPE"}, "widgets_values": ["SD1.5/realcartoon3d_v13.safetensors", "(best quality:1.4), fox girl", "(worst quality:1.4), nsfw"]}, {"id": 2, "type": "MaskDetailerPipe", "pos": [530, 210], "size": [569.4000244140625, 850], "flags": {}, "order": 2, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1}, {"name": "mask", "type": "MASK", "link": 2}, {"name": "basic_pipe", "type": "BASIC_PIPE", "link": 3, "slot_index": 2}, {"name": "refiner_basic_pipe_opt", "type": "BASIC_PIPE", "shape": 7, "link": null}, {"name": "detailer_hook", "type": "DETAILER_HOOK", "shape": 7, "link": null}, {"name": "scheduler_func_opt", "type": "SCHEDULER_FUNC", "shape": 7, "link": null}], "outputs": [{"name": "image", "type": "IMAGE", "shape": 3, "links": [5], "slot_index": 0}, {"name": "cropped_refined", "type": "IMAGE", "shape": 6, "links": null}, {"name": "cropped_enhanced_alpha", "type": "IMAGE", "shape": 6, "links": [4], "slot_index": 2}, {"name": "basic_pipe", "type": "BASIC_PIPE", "shape": 3, "links": null}, {"name": "refiner_basic_pipe_opt", "type": "BASIC_PIPE", "shape": 3, "links": null}], "properties": {"Node name for S&R": "MaskDetailerPipe"}, "widgets_values": [512, true, 1024, true, 1003, "fixed", 20, 8, "euler", "normal", 0.75, 5, 3, 10, 0.2, 1, 1, false, 20, false, false], "color": "#322", "bgcolor": "#533"}, {"id": 4, "type": "PreviewImage", "pos": [1230, 560], "size": [210, 246], "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 4}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": []}], "links": [[1, 1, 0, 2, 0, "IMAGE"], [2, 1, 1, 2, 1, "MASK"], [3, 3, 0, 2, 2, "BASIC_PIPE"], [4, 2, 2, 4, 0, "IMAGE"], [5, 2, 0, 5, 0, "IMAGE"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 1, "offset": [80, -110]}, "groupNodes": {"Impact::MAKE_BASIC_PIPE": {"author": "Dr.Lt.<PERSON>", "category": "", "config": {"1": {"input": {"text": {"name": "Positive prompt"}}}, "2": {"input": {"text": {"name": "Negative prompt"}}}}, "datetime": 1708272471445, "external": [], "links": [[0, 1, 1, 0, 1, "CLIP"], [0, 1, 2, 0, 1, "CLIP"], [0, 0, 3, 0, 1, "MODEL"], [0, 1, 3, 1, 1, "CLIP"], [0, 2, 3, 2, 1, "VAE"], [1, 0, 3, 3, 3, "CONDITIONING"], [2, 0, 3, 4, 4, "CONDITIONING"]], "nodes": [{"flags": {}, "index": 0, "mode": 0, "order": 0, "outputs": [{"links": [], "name": "MODEL", "shape": 3, "slot_index": 0, "type": "MODEL", "localized_name": "MODEL"}, {"links": [], "name": "CLIP", "shape": 3, "slot_index": 1, "type": "CLIP", "localized_name": "CLIP"}, {"links": [], "name": "VAE", "shape": 3, "slot_index": 2, "type": "VAE", "localized_name": "VAE"}], "pos": [550, 360], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "size": {"0": 315, "1": 98}, "type": "CheckpointLoaderSimple", "widgets_values": ["SDXL/sd_xl_base_1.0_0.9vae.safetensors"], "inputs": []}, {"flags": {}, "index": 1, "inputs": [{"link": null, "name": "clip", "type": "CLIP", "localized_name": "clip"}], "mode": 0, "order": 1, "outputs": [{"links": [], "name": "CONDITIONING", "shape": 3, "slot_index": 0, "type": "CONDITIONING", "localized_name": "CONDITIONING"}], "pos": [940, 480], "properties": {"Node name for S&R": "CLIPTextEncode"}, "size": {"0": 263, "1": 99}, "title": "Positive", "type": "CLIPTextEncode", "widgets_values": [""]}, {"flags": {}, "index": 2, "inputs": [{"link": null, "name": "clip", "type": "CLIP", "localized_name": "clip"}], "mode": 0, "order": 2, "outputs": [{"links": [], "name": "CONDITIONING", "shape": 3, "slot_index": 0, "type": "CONDITIONING", "localized_name": "CONDITIONING"}], "pos": [940, 640], "properties": {"Node name for S&R": "CLIPTextEncode"}, "size": {"0": 263, "1": 99}, "title": "Negative", "type": "CLIPTextEncode", "widgets_values": [""]}, {"flags": {}, "index": 3, "inputs": [{"link": null, "name": "model", "type": "MODEL", "localized_name": "model"}, {"link": null, "name": "clip", "type": "CLIP", "localized_name": "clip"}, {"link": null, "name": "vae", "type": "VAE", "localized_name": "vae"}, {"link": null, "name": "positive", "type": "CONDITIONING", "localized_name": "positive"}, {"link": null, "name": "negative", "type": "CONDITIONING", "localized_name": "negative"}], "mode": 0, "order": 3, "outputs": [{"links": null, "name": "basic_pipe", "shape": 3, "slot_index": 0, "type": "BASIC_PIPE", "localized_name": "basic_pipe"}], "pos": [1320, 360], "properties": {"Node name for S&R": "ToBasicPipe"}, "size": {"0": 241.79998779296875, "1": 106}, "type": "ToBasicPipe"}], "packname": "Impact", "version": "1.0"}}, "controller_panel": {"controllers": {}, "hidden": true, "highlight": true, "version": 2, "default_order": []}, "node_versions": {"comfy-core": "0.3.14", "comfyui-impact-pack": "1ae7cae2df8cca06027edfa3a24512671239d6c4"}, "ue_links": [], "VHS_latentpreview": false, "VHS_latentpreviewrate": 0, "VHS_MetadataImage": true, "VHS_KeepIntermediate": true}, "version": 0.4}