{"last_node_id": 30, "last_link_id": 50, "nodes": [{"id": 3, "type": "K<PERSON><PERSON><PERSON>", "pos": [-160, -150], "size": [315, 474], "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 1}, {"name": "positive", "type": "CONDITIONING", "link": 4}, {"name": "negative", "type": "CONDITIONING", "link": 6}, {"name": "latent_image", "type": "LATENT", "link": 2}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [7], "slot_index": 0}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [2, "fixed", 20, 7, "dpmpp_2m", "karras", 1], "color": "#223", "bgcolor": "#335"}, {"id": 4, "type": "CheckpointLoaderSimple", "pos": [-580, -300], "size": [315, 98], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [1, 25], "slot_index": 0}, {"name": "CLIP", "type": "CLIP", "links": [3, 5, 26], "slot_index": 1}, {"name": "VAE", "type": "VAE", "links": [8, 27], "slot_index": 2}], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["SD1.5/noosphere_v42.safetensors"], "color": "#223", "bgcolor": "#335"}, {"id": 5, "type": "EmptyLatentImage", "pos": [-567, 312], "size": [315, 106], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [2], "slot_index": 0}], "properties": {"Node name for S&R": "EmptyLatentImage"}, "widgets_values": [512, 768, 1], "color": "#223", "bgcolor": "#335"}, {"id": 6, "type": "CLIPTextEncode", "pos": [-610, -150], "size": [422.84503173828125, 164.31304931640625], "flags": {}, "order": 2, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 3}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [4, 28], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["photo of a blonde girl and a dark haired man with beard, front view, detailed faces, high details, realistic, nature background, high saturation"], "color": "#232", "bgcolor": "#353"}, {"id": 7, "type": "CLIPTextEncode", "pos": [-611, 66], "size": [425.27801513671875, 180.6060791015625], "flags": {}, "order": 3, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 5}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [6, 29], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["text, watermark, nsfw"], "color": "#322", "bgcolor": "#533"}, {"id": 8, "type": "VAEDecode", "pos": [-115, -271], "size": [210, 46], "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 7}, {"name": "vae", "type": "VAE", "link": 8}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [21, 22, 30], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": [], "color": "#223", "bgcolor": "#335"}, {"id": 10, "type": "ImpactMakeTileSEGS", "pos": [840, -90], "size": [282.6341552734375, 218], "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 31}, {"name": "filter_in_segs_opt", "type": "SEGS", "shape": 7, "link": null}, {"name": "filter_out_segs_opt", "type": "SEGS", "shape": 7, "link": null}], "outputs": [{"name": "SEGS", "type": "SEGS", "shape": 3, "links": [14, 15], "slot_index": 0}], "properties": {"Node name for S&R": "ImpactMakeTileSEGS"}, "widgets_values": [704, 1.1, 4, 0, 0, "Reuse fast"], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 11, "type": "WD14Tagger|pysssss", "pos": [1901, -282], "size": [276.18115234375, 470], "flags": {"collapsed": false}, "order": 12, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 10}], "outputs": [{"name": "STRING", "type": "STRING", "shape": 6, "links": [39], "slot_index": 0}], "properties": {"Node name for S&R": "WD14Tagger|pysssss"}, "widgets_values": ["wd-v1-4-moat-tagger-v2", 0.35000000000000003, 0.85, true, false, ""], "color": "#332922", "bgcolor": "#593930"}, {"id": 12, "type": "Detailer<PERSON>or<PERSON>ach", "pos": [2881.51708984375, -286.8627624511719], "size": [310.9673767089844, 790], "flags": {}, "order": 17, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 32}, {"name": "segs", "type": "SEGS", "link": 12}, {"name": "model", "type": "MODEL", "link": 25}, {"name": "clip", "type": "CLIP", "link": 26}, {"name": "vae", "type": "VAE", "link": 27}, {"name": "positive", "type": "CONDITIONING", "link": 28}, {"name": "negative", "type": "CONDITIONING", "link": 29}, {"name": "detailer_hook", "type": "DETAILER_HOOK", "shape": 7, "link": null}, {"name": "wildcard", "type": "STRING", "widget": {"name": "wildcard"}, "link": 49}, {"name": "scheduler_func_opt", "type": "SCHEDULER_FUNC", "shape": 7, "link": null}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "shape": 3, "links": [17], "slot_index": 0}], "properties": {"Node name for S&R": "Detailer<PERSON>or<PERSON>ach"}, "widgets_values": [768, true, 1024, 20, "fixed", 20, 3.5, "dpmpp_2m_sde_gpu", "karras", 0.5, 16, true, true, "", 1, false, 16, false, false], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 13, "type": "WD14Tagger|pysssss", "pos": [1388, -240], "size": [290, 240], "flags": {"collapsed": false}, "order": 7, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 22}], "outputs": [{"name": "STRING", "type": "STRING", "shape": 6, "links": [34], "slot_index": 0}], "properties": {"Node name for S&R": "WD14Tagger|pysssss"}, "widgets_values": ["wd-v1-4-moat-tagger-v2", 0.35000000000000003, 0.85, true, false, ""], "color": "#332922", "bgcolor": "#593930"}, {"id": 14, "type": "SEGSToImageList", "pos": [830, 230], "size": [276.6341552734375, 46], "flags": {}, "order": 11, "mode": 0, "inputs": [{"name": "segs", "type": "SEGS", "link": 14}, {"name": "fallback_image_opt", "type": "IMAGE", "shape": 7, "link": 33}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "shape": 6, "links": [10, 18], "slot_index": 0}], "properties": {"Node name for S&R": "SEGSToImageList"}, "widgets_values": [], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 15, "type": "ImpactSEGSLabelAssign", "pos": [2409.8916015625, 36.29731369018555], "size": [283.6341552734375, 103.9290771484375], "flags": {}, "order": 16, "mode": 0, "inputs": [{"name": "segs", "type": "SEGS", "link": 15}, {"name": "labels", "type": "STRING", "widget": {"name": "labels"}, "link": 50}], "outputs": [{"name": "SEGS", "type": "SEGS", "shape": 3, "links": [12], "slot_index": 0}], "properties": {"Node name for S&R": "ImpactSEGSLabelAssign"}, "widgets_values": [""], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 16, "type": "PreviewImage", "pos": [3270, -287], "size": [842.0664672851562, 1217.6240234375], "flags": {}, "order": 18, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 17}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": [], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 17, "type": "PreviewImage", "pos": [788, 339], "size": [421.1688537597656, 448.1822509765625], "flags": {}, "order": 13, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 18}], "outputs": [], "title": "Preview Tiles", "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": [], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 19, "type": "PreviewImage", "pos": [173, -283], "size": [475.25579833984375, 668.4122924804688], "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 21}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": [], "color": "#223", "bgcolor": "#335"}, {"id": 21, "type": "ImageScaleBy", "pos": [840, -270], "size": [315, 82], "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 30}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "shape": 3, "links": [31, 32, 33], "slot_index": 0}], "properties": {"Node name for S&R": "ImageScaleBy"}, "widgets_values": ["bicubic", 2.5], "color": "#233", "bgcolor": "#355"}, {"id": 25, "type": "StringListToString", "pos": [1375, 131], "size": [315, 82], "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "string_list", "type": "STRING", "widget": {"name": "string_list"}, "link": 34}], "outputs": [{"name": "STRING", "type": "STRING", "shape": 3, "links": [47], "slot_index": 0}], "properties": {"Node name for S&R": "StringListToString"}, "widgets_values": ["", ""], "color": "#332922", "bgcolor": "#593930"}, {"id": 26, "type": "StringListToString", "pos": [1913, 259], "size": [268.8372497558594, 58], "flags": {}, "order": 14, "mode": 0, "inputs": [{"name": "string_list", "type": "STRING", "widget": {"name": "string_list"}, "link": 39}], "outputs": [{"name": "STRING", "type": "STRING", "shape": 3, "links": [48], "slot_index": 0}], "properties": {"Node name for S&R": "StringListToString"}, "widgets_values": ["\\n", ""], "color": "#332922", "bgcolor": "#593930"}, {"id": 30, "type": "WildcardPromptFromString", "pos": [2396.2451171875, -266.2974548339844], "size": [315, 198], "flags": {}, "order": 15, "mode": 0, "inputs": [{"name": "string", "type": "STRING", "widget": {"name": "string"}, "link": 48}, {"name": "restrict_to_tags", "type": "STRING", "widget": {"name": "restrict_to_tags"}, "link": 47}], "outputs": [{"name": "wildcard", "type": "STRING", "shape": 3, "links": [49], "slot_index": 0}, {"name": "segs_labels", "type": "STRING", "shape": 3, "links": [50], "slot_index": 1}], "properties": {"Node name for S&R": "WildcardPromptFromString"}, "widgets_values": ["", "\\n", "", ", realistic, high details, high saturation", "", "1girl, 1boy, 2girls, multiple girls, realistic"], "color": "#2a363b", "bgcolor": "#3f5159"}], "links": [[1, 4, 0, 3, 0, "MODEL"], [2, 5, 0, 3, 3, "LATENT"], [3, 4, 1, 6, 0, "CLIP"], [4, 6, 0, 3, 1, "CONDITIONING"], [5, 4, 1, 7, 0, "CLIP"], [6, 7, 0, 3, 2, "CONDITIONING"], [7, 3, 0, 8, 0, "LATENT"], [8, 4, 2, 8, 1, "VAE"], [10, 14, 0, 11, 0, "IMAGE"], [12, 15, 0, 12, 1, "SEGS"], [14, 10, 0, 14, 0, "SEGS"], [15, 10, 0, 15, 0, "SEGS"], [17, 12, 0, 16, 0, "IMAGE"], [18, 14, 0, 17, 0, "IMAGE"], [21, 8, 0, 19, 0, "IMAGE"], [22, 8, 0, 13, 0, "IMAGE"], [25, 4, 0, 12, 2, "MODEL"], [26, 4, 1, 12, 3, "CLIP"], [27, 4, 2, 12, 4, "VAE"], [28, 6, 0, 12, 5, "CONDITIONING"], [29, 7, 0, 12, 6, "CONDITIONING"], [30, 8, 0, 21, 0, "IMAGE"], [31, 21, 0, 10, 0, "IMAGE"], [32, 21, 0, 12, 0, "IMAGE"], [33, 21, 0, 14, 1, "IMAGE"], [34, 13, 0, 25, 0, "STRING"], [39, 11, 0, 26, 0, "STRING"], [47, 25, 0, 30, 1, "STRING"], [48, 26, 0, 30, 0, "STRING"], [49, 30, 0, 12, 8, "STRING"], [50, 30, 1, 15, 1, "STRING"]], "groups": [{"id": 1, "title": "Base Image", "bounding": [-620, -374, 1311, 872], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 2, "title": "Upscale and Create Tiles", "bounding": [745, -375, 515, 1202], "color": "#8AA", "font_size": 24, "flags": {}}, {"id": 3, "title": "Tag Base Image", "bounding": [1311, -374, 431, 668], "color": "#b06634", "font_size": 24, "flags": {}}, {"id": 4, "title": "Tag Tiles", "bounding": [1815, -378, 460, 750], "color": "#b06634", "font_size": 24, "flags": {}}, {"id": 5, "title": "Assign Prompts to <PERSON>iles", "bounding": [2367, -381, 380, 615], "color": "#8AA", "font_size": 24, "flags": {}}, {"id": 6, "title": "Add Details", "bounding": [2834, -382, 1359, 1377], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 1, "offset": [711, 400]}, "groupNodes": {}, "controller_panel": {"controllers": {}, "hidden": true, "highlight": true, "version": 2, "default_order": []}, "node_versions": {"comfy-core": "0.3.14", "comfyui-impact-pack": "1ae7cae2df8cca06027edfa3a24512671239d6c4", "comfyui-wd14-tagger": "1.0.0"}, "ue_links": [], "VHS_latentpreview": false, "VHS_latentpreviewrate": 0, "VHS_MetadataImage": true, "VHS_KeepIntermediate": true}, "version": 0.4}