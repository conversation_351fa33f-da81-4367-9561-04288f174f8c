{"FaceDetailer": {"description": "감지 모델(bbox, segm, sam) 모델을 이용해서 입력 이미지에서 자동으로 특정 객체를 감지하고, 감지 영역을 가이드 크기를 기반으로 확대해서 인페이트하는 방법으로 디테일을 강화합니다.\n사용자들이 자주 사용하는 얼굴 디테일 강화 워크플로를 단순화하기 위해 특화 시킨 노드이긴 하지만, 감지 모델에 따라서 다양한 자동 인페인트 용도로 사용 가능합니다.", "display_name": "얼굴 디테일러", "inputs": {"image": {"name": "이미지"}, "model": {"name": "모델", "tooltip": "만약 `ImpactDummyInput` 을 연결 하면, 인페인트 단계를 건너 뜁니다."}, "guide_size": {"name": "가이드 크기", "tooltip": "'가이드 크기 대상'으로 지정된 크기의 가장 짧은면을 이 크기까지 확대합니다."}, "guide_size_for": {"name": "가이드 크기 대상", "tooltip": "bbox: 감지된 사각 영역(bbox)\ncrop_region: 잘라낸 영역"}, "max_size": {"name": "최대 크기", "tooltip": "'가이드 크기'로 확대 할 때, 가장 긴 면의 길이를 이 크기로 제한합니다. 너무 크게 확대 되는 것을 막아줍니다."}, "seed": {"name": "시드"}, "steps": {"name": "스텝수"}, "sampler_name": {"name": "샘플러 이름"}, "scheduler": {"name": "스케쥴러"}, "positive": {"name": "긍정 조건"}, "negative": {"name": "부정 조건"}, "denoise": {"name": "노이즈 제거양"}, "feather": {"name": "가장자리 흐림", "tooltip": "확대 해서 인페인트 된 이미지를 원본 이미지에 붙여넣을 때, 이 수치로 마스크의 가장 자리를 흐리게 해서 붙여넣어 이음매의 위화감을 줄여줍니다."}, "noise_mask": {"name": "노이즈 마스크 사용", "tooltip": "인페인트 할 때, 마스크를 적용해서 마스크 영역만 인페인트합니다. 이 옵션을 적용하지 않으면, 잘라낸 이미지 전체가 재생성되어 노이즈 제거양이 클 때 위화감이 나타나게 됩니다."}, "force_inpaint": {"name": "인페인트 강제 적용", "tooltip": "가이드 크기와 상관 없이 인페인팅을 무조건 적용 합니다. 이 옵션이 꺼져있는 경우 가이드 크기보다 이미 큰 감지 영역은 인페인팅을 건너 뜁니다."}, "bbox_threshold": {"name": "bbox 감지 임계치", "tooltip": "사각 영역(bbox) 감지 모델의 최소 감지 임계치를 설정합니다. 임계치가 높을수록 확실한 객체만 감지하지만, 객체를 감지하지 못할 확률이 증가합니다."}, "bbox_dilation": {"name": "bbox 확장", "tooltip": "감지된 사각 영역(bbox)을 확장 합니다. 이 옵션은 감지된 영역보다 더 넓은 영역을 인페인트 할 경우 사용합니다.\n주의: sam 모델을 사용할 경우 잘라낸 영역내에서 bbox 확장을 하더라도, sam 감지 영역이 작으면 여전히 제한됩니다."}, "bbox_crop_factor": {"name": "bbox 자르기 배율", "tooltip": "감지된 사각 영역(bbox)의 몇배 크기의 영역을 잘라낼 것 인지를 설정합니다. 이 크기가 너무 작으면, 인페인트 할 이미지의 주변 정보가 부족해서 위화감이 강한 이미지가 생성됩니다. 이 크기가 너무 크면, 인페인팅에 너무 오랜 시간이 걸릴 수 있으며, 모델의 역량을 초과할 정도로 클 경우 올바르지 않은 이미지를 생성하게 됩니다."}, "sam_detection_hint": {"name": "sam 감지 힌트", "tooltip": "[실험기능] 오래된 실험 기능으로, sam 모델의 감지 힌트를 제공하는 방식입니다. cetner-1 (중앙점 1개) 외에는 사용하지 않을 것을 권장합니다."}, "sam_dilation": {"name": "sam 마스크 확장", "tooltip": "sam 모델로 감지된 실루엣 마스크를 확장합니다."}, "sam_threshold": {"name": "sam 감지 임계치", "tooltip": "sam 모델의 최소 감지 임계치를 설정합니다. 임계치가 높을수록 확실한 객체만 감지하지만, 객체를 감지하지 못할 확률이 증가합니다."}, "sam_bbox_expansion": {"name": "sam 영역 확장", "tooltip": "sam 의 감지 영역을 확장합니다. 감지 영역은 마스크를 포함하는 전체 사각 영역입니다.\n주의1:sam 마스크를 확장하더라도, 감지 영역을 벗어날 수 없습니다.\n주의2: bbox 확장을 하더라도, sam 감지 영역이 작으면 여전히 제한됩니다."}, "sam_mask_hint_threshold": {"name": "sam 마스크 힌트 임계치", "tooltip": "[실험기능] 오래된 실험 기능으로, mask-hint 모드에서만 사용되는 옵션으로, 마스크에서 이 크기 이상의 점 마스크를 sam의 힌트로 사용합니다."}, "sam_mask_hint_use_negative": {"name": "sam 마스크 힌트에 제외 힌트 사용", "tooltip": "[실험기능] 오래된 실험 기능으로, mask-hitn 모드에서만 사용되는 옵션으로, sam 마스크 힌트 임계치보다 작은 점 마스크를 sam의 제외 힌트로 사용합니다."}, "drop_size": {"name": "감지 최소 크기", "tooltip": "사각 영역(bbox) 감지기로 감지한 크기가 이 설정값 보다 작을 경우 무시합니다."}, "bbox_detector": {"name": "bbox 감지기", "tooltip": "디테일 개선 대상을 자동으로 감지해주는 사각 영역(bbox) 감지기 입력.\n이 감지기로 감지된 감지 정보가 기준 정보입니다."}, "wildcard": {"name": "와일드카드 프롬프트", "tooltip": "'와일드카드 인코더 (Impact)'와 유사한 기능을 수행하여, 와일드카드 기능과 로라 로딩 기능을 제공합니다. 또한, 감지된 영역별로 다른 프롬프트를 적용하는 기능들을 제공합니다.\n더 자세한 정보는 튜토리얼 페이지를 참고하세요.\n주의:이 입력을 비워두면, 이 입력은 무시됩니다."}, "cycle": {"name": "반복수", "tooltip": "설정된 값만큼 인페인팅을 반복 적용합니다. 인코딩/디코딩 없이 확대된 잠재 이미지 단계에서 반복됩니다."}, "sam_model_opt": {"name": "sam 모델", "tooltip": "이 모델을 제공할 경우 sam 모델을 감지 보조 모델로 사용합니다. bbox 감지기로 감지된 사각 영역에 sam 모델을 적용해서 정교한 실루엣 마스크를 생성합니다.\n주의: 이 입력이 연결될 경우 segm 감지기는 무시됩니다."}, "segm_detector_opt": {"name": "segm 감지기", "tooltip": "이 모델을 제공할 경우 segm 모델을 감지 보조 모델로 사용합니다. bbox 감지기로 감지된 사각 영역에 segm 감지기로 감지된 실루엣 마스크를 생성합니다.\n주의: 이 입력은 sam 모델이 연결될 경우 무시됩니다."}, "detailer_hook": {"name": "디테일러 후크", "tooltip": "이 노드의 실행 중간단계에서 여러가지 기능을 수행할 수 있는 후크를 연결합니다."}, "inpaint_model": {"name": "인페인트 모델 모드", "tooltip": "인페인트 전용 모델을 사용할 경우 이 옵션을 켜면, 인페인팅시에 '인페인트 모델 조건 설정'이 적용되어 수행됩니다."}, "noise_mask_feather": {"name": "노이즈 마스크 가장자리 흐림", "tooltip": "인페인트시에 적용되는 노이즈 마스크의 가장자리를 흐리게 합니다. 이 설정값이 0을 초과할 경우, 내부적으로 자동으로 '차등 확산' 노드를 적용합니다."}, "scheduler_func_opt": {"name": "스케쥴러 함수", "tooltip": "GITS 스케쥴러 처럼 기본 스케쥴러 리스트에서 선택할 수 없는 특수 스케쥴러를 사용할 수 있게 해줍니다. 이 입력이 연결되면, 기본 스케쥴러 선택은 무시됩니다."}, "tiled_encode": {"name": "타일 인코드 사용", "tooltip": "이 옵션을 켜면, 내부적으로 'VAE 인코드'를 사용할 경우, 기본 'VAE 인코드' 대신 'VAE 인코드 (타일)' 을 적용합니다."}, "tiled_decode": {"name": "타일 디코드 사용", "tooltip": "이 옵션을 켜면, 내부적으로 'VAE 다코드'를 사용할 경우, 기본 'VAE 디코드' 대신 'VAE 디코드 (타일)' 을 적용합니다."}}, "outputs": {"0": {"name": "개선 이미"}, "1": {"name": "잘라낸 이미지"}, "2": {"name": "잘라낸 투명 이미지"}, "3": {"name": "마스크"}, "4": {"name": "디테일러 파이프"}, "5": {"name": "컨트롤넷 이미지"}}}, "FaceDetailerPipe": {"description": "감지 모델(bbox, segm, sam) 모델을 이용해서 입력 이미지에서 자동으로 특정 객체를 감지하고, 감지 영역을 가이드 크기를 기반으로 확대해서 인페이트하는 방법으로 디테일을 강화합니다.\n사용자들이 자주 사용하는 얼굴 디테일 강화 워크플로를 단순화하기 위해 특화 시킨 노드이긴 하지만, 감지 모델에 따라서 다양한 자동 인페인트 용도로 사용 가능합니다.", "display_name": "얼굴 디테일러 (파이프)", "inputs": {"image": {"name": "이미지"}, "detailer_pipe": {"name": "디테일러 파이프", "tooltip": "만약 디테일러 파이프 내의 모델에 `ImpactDummyInput` 가 설정된 경우, 인페인트 단계를 건너 뜁니다."}, "guide_size": {"name": "가이드 크기", "tooltip": "'가이드 크기 대상'으로 지정된 크기의 가장 짧은면을 이 크기까지 확대합니다."}, "guide_size_for": {"name": "가이드 크기 대상", "tooltip": "bbox: 감지된 사각 영역(bbox)\ncrop_region: 잘라낸 영역"}, "max_size": {"name": "최대 크기", "tooltip": "'가이드 크기'로 확대 할 때, 가장 긴 면의 길이를 이 크기로 제한합니다. 너무 크게 확대 되는 것을 막아줍니다."}, "seed": {"name": "시드"}, "steps": {"name": "스텝수"}, "sampler_name": {"name": "샘플러 이름"}, "scheduler": {"name": "스케쥴러"}, "denoise": {"name": "노이즈 제거양"}, "feather": {"name": "가장자리 흐림", "tooltip": "확대 해서 인페인트 된 이미지를 원본 이미지에 붙여넣을 때, 이 수치로 마스크의 가장 자리를 흐리게 해서 붙여넣어 이음매의 위화감을 줄여줍니다."}, "noise_mask": {"name": "노이즈 마스크 사용", "tooltip": "인페인트 할 때, 마스크를 적용해서 마스크 영역만 인페인트합니다. 이 옵션을 적용하지 않으면, 잘라낸 이미지 전체가 재생성되어 노이즈 제거양이 클 때 위화감이 나타나게 됩니다."}, "force_inpaint": {"name": "인페인트 강제 적용", "tooltip": "가이드 크기와 상관 없이 인페인팅을 무조건 적용 합니다. 이 옵션이 꺼져있는 경우 가이드 크기보다 이미 큰 감지 영역은 인페인팅을 건너 뜁니다."}, "bbox_threshold": {"name": "bbox 감지 임계치", "tooltip": "사각 영역(bbox) 감지 모델의 최소 감지 임계치를 설정합니다. 임계치가 높을수록 확실한 객체만 감지하지만, 객체를 감지하지 못할 확률이 증가합니다."}, "bbox_dilation": {"name": "bbox 확장", "tooltip": "감지된 사각 영역(bbox)을 확장 합니다. 이 옵션은 감지된 영역보다 더 넓은 영역을 인페인트 할 경우 사용합니다.\n주의: sam 모델을 사용할 경우 잘라낸 영역내에서 bbox 확장을 하더라도, sam 감지 영역이 작으면 여전히 제한됩니다."}, "bbox_crop_factor": {"name": "bbox 자르기 배율", "tooltip": "감지된 사각 영역(bbox)의 몇배 크기의 영역을 잘라낼 것 인지를 설정합니다. 이 크기가 너무 작으면, 인페인트 할 이미지의 주변 정보가 부족해서 위화감이 강한 이미지가 생성됩니다. 이 크기가 너무 크면, 인페인팅에 너무 오랜 시간이 걸릴 수 있으며, 모델의 역량을 초과할 정도로 클 경우 올바르지 않은 이미지를 생성하게 됩니다."}, "sam_detection_hint": {"name": "sam 감지 힌트", "tooltip": "[실험기능] 오래된 실험 기능으로, sam 모델의 감지 힌트를 제공하는 방식입니다. cetner-1 (중앙점 1개) 외에는 사용하지 않을 것을 권장합니다."}, "sam_dilation": {"name": "sam 마스크 확장", "tooltip": "sam 모델로 감지된 실루엣 마스크를 확장합니다."}, "sam_threshold": {"name": "sam 감지 임계치", "tooltip": "sam 모델의 최소 감지 임계치를 설정합니다. 임계치가 높을수록 확실한 객체만 감지하지만, 객체를 감지하지 못할 확률이 증가합니다."}, "sam_bbox_expansion": {"name": "sam 영역 확장", "tooltip": "sam 의 감지 영역을 확장합니다. 감지 영역은 마스크를 포함하는 전체 사각 영역입니다.\n주의1:sam 마스크를 확장하더라도, 감지 영역을 벗어날 수 없습니다.\n주의2: bbox 확장을 하더라도, sam 감지 영역이 작으면 여전히 제한됩니다."}, "sam_mask_hint_threshold": {"name": "sam 마스크 힌트 임계치", "tooltip": "[실험기능] 오래된 실험 기능으로, mask-hint 모드에서만 사용되는 옵션으로, 마스크에서 이 크기 이상의 점 마스크를 sam의 힌트로 사용합니다."}, "sam_mask_hint_use_negative": {"name": "sam 마스크 힌트에 제외 힌트 사용", "tooltip": "[실험기능] 오래된 실험 기능으로, mask-hitn 모드에서만 사용되는 옵션으로, sam 마스크 힌트 임계치보다 작은 점 마스크를 sam의 제외 힌트로 사용합니다."}, "drop_size": {"name": "감지 최소 크기", "tooltip": "사각 영역(bbox) 감지기로 감지한 크기가 이 설정값 보다 작을 경우 무시합니다."}, "refiner_ratio": {"name": "라파이너 적용 비율", "tooltip": "SDXL 리파이너 모델을 사용할 경우 적용될 후반 스텝수 비율을 설정합니다."}, "cycle": {"name": "반복수", "tooltip": "설정된 값만큼 인페인팅을 반복 적용합니다. 인코딩/디코딩 없이 확대된 잠재 이미지 단계에서 반복됩니다."}, "inpaint_model": {"name": "인페인트 모델 모드", "tooltip": "인페인트 전용 모델을 사용할 경우 이 옵션을 켜면, 인페인팅시에 '인페인트 모델 조건 설정'이 적용되어 수행됩니다."}, "noise_mask_feather": {"name": "노이즈 마스크 가장자리 흐림", "tooltip": "인페인트시에 적용되는 노이즈 마스크의 가장자리를 흐리게 합니다. 이 설정값이 0을 초과할 경우, 내부적으로 자동으로 '차등 확산' 노드를 적용합니다."}, "scheduler_func_opt": {"name": "스케쥴러 함수", "tooltip": "GITS 스케쥴러 처럼 기본 스케쥴러 리스트에서 선택할 수 없는 특수 스케쥴러를 사용할 수 있게 해줍니다. 이 입력이 연결되면, 기본 스케쥴러 선택은 무시됩니다."}, "tiled_encode": {"name": "타일 인코드 사용", "tooltip": "이 옵션을 켜면, 내부적으로 'VAE 인코드'를 사용할 경우, 기본 'VAE 인코드' 대신 'VAE 인코드 (타일)' 을 적용합니다."}, "tiled_decode": {"name": "타일 디코드 사용", "tooltip": "이 옵션을 켜면, 내부적으로 'VAE 다코드'를 사용할 경우, 기본 'VAE 디코드' 대신 'VAE 디코드 (타일)' 을 적용합니다."}}, "outputs": {"0": {"name": "개선 이미"}, "1": {"name": "잘라낸 이미지"}, "2": {"name": "잘라낸 투명 이미지"}, "3": {"name": "마스크"}, "4": {"name": "디테일러 파이프"}, "5": {"name": "컨트롤넷 이미지"}}}, "DetailerForEach": {"description": "감지 영역 정보 묶음(SEGS)내의 각 영역들에 대해 가이드 크기를 기반으로 확대해서 인페이트하는 방법으로 디테일을 강화합니다.", "display_name": "디테일러 (SEGS)", "inputs": {"image": {"name": "이미지"}, "segs": {"name": "segs", "tooltip": "감지 영역 정보를 담고 있는 묶음.\n이 영역들을 대상으로 인페인트가 적용됩니다."}, "model": {"name": "모델", "tooltip": "만약 `ImpactDummyInput` 을 연결 하면, 인페인트 단계를 건너 뜁니다."}, "guide_size": {"name": "가이드 크기", "tooltip": "'가이드 크기 대상'으로 지정된 크기의 가장 짧은면을 이 크기까지 확대합니다."}, "guide_size_for": {"name": "가이드 크기 대상", "tooltip": "bbox: 감지된 사각 영역(bbox)\ncrop_region: 잘라낸 영역"}, "max_size": {"name": "최대 크기", "tooltip": "'가이드 크기'로 확대 할 때, 가장 긴 면의 길이를 이 크기로 제한합니다. 너무 크게 확대 되는 것을 막아줍니다."}, "seed": {"name": "시드"}, "steps": {"name": "스텝수"}, "sampler_name": {"name": "샘플러 이름"}, "scheduler": {"name": "스케쥴러"}, "positive": {"name": "긍정 조건"}, "negative": {"name": "부정 조건"}, "denoise": {"name": "노이즈 제거양"}, "feather": {"name": "가장자리 흐림", "tooltip": "확대 해서 인페인트 된 이미지를 원본 이미지에 붙여넣을 때, 이 수치로 마스크의 가장 자리를 흐리게 해서 붙여넣어 이음매의 위화감을 줄여줍니다."}, "noise_mask": {"name": "노이즈 마스크 사용", "tooltip": "인페인트 할 때, 마스크를 적용해서 마스크 영역만 인페인트합니다. 이 옵션을 적용하지 않으면, 잘라낸 이미지 전체가 재생성되어 노이즈 제거양이 클 때 위화감이 나타나게 됩니다."}, "force_inpaint": {"name": "인페인트 강제 적용", "tooltip": "가이드 크기와 상관 없이 인페인팅을 무조건 적용 합니다. 이 옵션이 꺼져있는 경우 가이드 크기보다 이미 큰 감지 영역은 인페인팅을 건너 뜁니다."}, "wildcard": {"name": "와일드카드 프롬프트", "tooltip": "'와일드카드 인코더 (Impact)'와 유사한 기능을 수행하여, 와일드카드 기능과 로라 로딩 기능을 제공합니다. 또한, 감지된 영역별로 다른 프롬프트를 적용하는 기능들을 제공합니다.\n더 자세한 정보는 튜토리얼 페이지를 참고하세요.\n주의:이 입력을 비워두면, 이 입력은 무시됩니다."}, "cycle": {"name": "반복수", "tooltip": "설정된 값만큼 인페인팅을 반복 적용합니다. 인코딩/디코딩 없이 확대된 잠재 이미지 단계에서 반복됩니다."}, "detailer_hook": {"name": "디테일러 후크", "tooltip": "이 노드의 실행 중간단계에서 여러가지 기능을 수행할 수 있는 후크를 연결합니다."}, "inpaint_model": {"name": "인페인트 모델 모드", "tooltip": "인페인트 전용 모델을 사용할 경우 이 옵션을 켜면, 인페인팅시에 '인페인트 모델 조건 설정'이 적용되어 수행됩니다."}, "noise_mask_feather": {"name": "노이즈 마스크 가장자리 흐림", "tooltip": "인페인트시에 적용되는 노이즈 마스크의 가장자리를 흐리게 합니다. 이 설정값이 0을 초과할 경우, 내부적으로 자동으로 '차등 확산' 노드를 적용합니다."}, "scheduler_func_opt": {"name": "스케쥴러 함수", "tooltip": "GITS 스케쥴러 처럼 기본 스케쥴러 리스트에서 선택할 수 없는 특수 스케쥴러를 사용할 수 있게 해줍니다. 이 입력이 연결되면, 기본 스케쥴러 선택은 무시됩니다."}, "tiled_encode": {"name": "타일 인코드 사용", "tooltip": "이 옵션을 켜면, 내부적으로 'VAE 인코드'를 사용할 경우, 기본 'VAE 인코드' 대신 'VAE 인코드 (타일)' 을 적용합니다."}, "tiled_decode": {"name": "타일 디코드 사용", "tooltip": "이 옵션을 켜면, 내부적으로 'VAE 다코드'를 사용할 경우, 기본 'VAE 디코드' 대신 'VAE 디코드 (타일)' 을 적용합니다."}}, "outputs": {"0": {"name": "개선 이미지"}}}, "DetailerForEachPipe": {"description": "감지 영역 정보 묶음(SEGS)내의 각 영역들에 대해 가이드 크기를 기반으로 확대해서 인페이트하는 방법으로 디테일을 강화합니다.", "display_name": "디테일러 (상세/SEGS/파이프)", "inputs": {"image": {"name": "이미지"}, "segs": {"name": "segs", "tooltip": "감지 영역 정보를 담고 있는 묶음.\n이 영역들을 대상으로 인페인트가 적용됩니다."}, "guide_size": {"name": "가이드 크기", "tooltip": "'가이드 크기 대상'으로 지정된 크기의 가장 짧은면을 이 크기까지 확대합니다."}, "guide_size_for": {"name": "가이드 크기 대상", "tooltip": "bbox: 감지된 사각 영역(bbox)\ncrop_region: 잘라낸 영역"}, "max_size": {"name": "최대 크기", "tooltip": "'가이드 크기'로 확대 할 때, 가장 긴 면의 길이를 이 크기로 제한합니다. 너무 크게 확대 되는 것을 막아줍니다."}, "seed": {"name": "시드"}, "steps": {"name": "스텝수"}, "sampler_name": {"name": "샘플러 이름"}, "scheduler": {"name": "스케쥴러"}, "denoise": {"name": "노이즈 제거양"}, "feather": {"name": "가장자리 흐림", "tooltip": "확대 해서 인페인트 된 이미지를 원본 이미지에 붙여넣을 때, 이 수치로 마스크의 가장 자리를 흐리게 해서 붙여넣어 이음매의 위화감을 줄여줍니다."}, "noise_mask": {"name": "노이즈 마스크 사용", "tooltip": "인페인트 할 때, 마스크를 적용해서 마스크 영역만 인페인트합니다. 이 옵션을 적용하지 않으면, 잘라낸 이미지 전체가 재생성되어 노이즈 제거양이 클 때 위화감이 나타나게 됩니다."}, "force_inpaint": {"name": "인페인트 강제 적용", "tooltip": "가이드 크기와 상관 없이 인페인팅을 무조건 적용 합니다. 이 옵션이 꺼져있는 경우 가이드 크기보다 이미 큰 감지 영역은 인페인팅을 건너 뜁니다."}, "basic_pipe": {"name": "기본 파이프", "tooltip": "만약 기본 파이프 내의 모델에 `ImpactDummyInput` 가 설정된 경우, 인페인트 단계를 건너 뜁니다."}, "refiner_ratio": {"name": "라파이너 적용 비율", "tooltip": "SDXL 리파이너 모델을 사용할 경우 적용될 후반 스텝수 비율을 설정합니다."}, "cycle": {"name": "반복수", "tooltip": "설정된 값만큼 인페인팅을 반복 적용합니다. 인코딩/디코딩 없이 확대된 잠재 이미지 단계에서 반복됩니다."}, "detailer_hook": {"name": "디테일러 후크", "tooltip": "이 노드의 실행 중간단계에서 여러가지 기능을 수행할 수 있는 후크를 연결합니다."}, "refiner_basic_pipe_opt": {"name": "리파이너 기본 파이프", "tooltip": "SDXL 리파이너 단계에 적용할 기본 파이프를 연결합니다."}, "inpaint_model": {"name": "인페인트 모델 모드", "tooltip": "인페인트 전용 모델을 사용할 경우 이 옵션을 켜면, 인페인팅시에 '인페인트 모델 조건 설정'이 적용되어 수행됩니다."}, "noise_mask_feather": {"name": "노이즈 마스크 가장자리 흐림", "tooltip": "인페인트시에 적용되는 노이즈 마스크의 가장자리를 흐리게 합니다. 이 설정값이 0을 초과할 경우, 내부적으로 자동으로 '차등 확산' 노드를 적용합니다."}, "scheduler_func_opt": {"name": "스케쥴러 함수", "tooltip": "GITS 스케쥴러 처럼 기본 스케쥴러 리스트에서 선택할 수 없는 특수 스케쥴러를 사용할 수 있게 해줍니다. 이 입력이 연결되면, 기본 스케쥴러 선택은 무시됩니다."}, "tiled_encode": {"name": "타일 인코드 사용", "tooltip": "이 옵션을 켜면, 내부적으로 'VAE 인코드'를 사용할 경우, 기본 'VAE 인코드' 대신 'VAE 인코드 (타일)' 을 적용합니다."}, "tiled_decode": {"name": "타일 디코드 사용", "tooltip": "이 옵션을 켜면, 내부적으로 'VAE 다코드'를 사용할 경우, 기본 'VAE 디코드' 대신 'VAE 디코드 (타일)' 을 적용합니다."}}, "outputs": {"0": {"name": "개선 이미지"}, "1": {"name": "segs"}, "2": {"name": "기본 파이프"}, "3": {"name": "컨트롤넷 이미지"}}}, "DetailerForEachDebug": {"description": "감지 영역 정보 묶음(SEGS)내의 각 영역들에 대해 가이드 크기를 기반으로 확대해서 인페이트하는 방법으로 디테일을 강화합니다.", "display_name": "디테일러 (상세/SEGS)", "inputs": {"image": {"name": "이미지"}, "segs": {"name": "segs", "tooltip": "감지 영역 정보를 담고 있는 묶음.\n이 영역들을 대상으로 인페인트가 적용됩니다."}, "model": {"name": "모델", "tooltip": "만약 `ImpactDummyInput` 을 연결 하면, 인페인트 단계를 건너 뜁니다."}, "guide_size": {"name": "가이드 크기", "tooltip": "'가이드 크기 대상'으로 지정된 크기의 가장 짧은면을 이 크기까지 확대합니다."}, "guide_size_for": {"name": "가이드 크기 대상", "tooltip": "bbox: 감지된 사각 영역(bbox)\ncrop_region: 잘라낸 영역"}, "max_size": {"name": "최대 크기", "tooltip": "'가이드 크기'로 확대 할 때, 가장 긴 면의 길이를 이 크기로 제한합니다. 너무 크게 확대 되는 것을 막아줍니다."}, "seed": {"name": "시드"}, "steps": {"name": "스텝수"}, "sampler_name": {"name": "샘플러 이름"}, "scheduler": {"name": "스케쥴러"}, "positive": {"name": "긍정 조건"}, "negative": {"name": "부정 조건"}, "denoise": {"name": "노이즈 제거양"}, "feather": {"name": "가장자리 흐림", "tooltip": "확대 해서 인페인트 된 이미지를 원본 이미지에 붙여넣을 때, 이 수치로 마스크의 가장 자리를 흐리게 해서 붙여넣어 이음매의 위화감을 줄여줍니다."}, "noise_mask": {"name": "노이즈 마스크 사용", "tooltip": "인페인트 할 때, 마스크를 적용해서 마스크 영역만 인페인트합니다. 이 옵션을 적용하지 않으면, 잘라낸 이미지 전체가 재생성되어 노이즈 제거양이 클 때 위화감이 나타나게 됩니다."}, "force_inpaint": {"name": "인페인트 강제 적용", "tooltip": "가이드 크기와 상관 없이 인페인팅을 무조건 적용 합니다. 이 옵션이 꺼져있는 경우 가이드 크기보다 이미 큰 감지 영역은 인페인팅을 건너 뜁니다."}, "wildcard": {"name": "와일드카드 프롬프트", "tooltip": "'와일드카드 인코더 (Impact)'와 유사한 기능을 수행하여, 와일드카드 기능과 로라 로딩 기능을 제공합니다. 또한, 감지된 영역별로 다른 프롬프트를 적용하는 기능들을 제공합니다.\n더 자세한 정보는 튜토리얼 페이지를 참고하세요.\n주의:이 입력을 비워두면, 이 입력은 무시됩니다."}, "cycle": {"name": "반복수", "tooltip": "설정된 값만큼 인페인팅을 반복 적용합니다. 인코딩/디코딩 없이 확대된 잠재 이미지 단계에서 반복됩니다."}, "detailer_hook": {"name": "디테일러 후크", "tooltip": "이 노드의 실행 중간단계에서 여러가지 기능을 수행할 수 있는 후크를 연결합니다."}, "inpaint_model": {"name": "인페인트 모델 모드", "tooltip": "인페인트 전용 모델을 사용할 경우 이 옵션을 켜면, 인페인팅시에 '인페인트 모델 조건 설정'이 적용되어 수행됩니다."}, "noise_mask_feather": {"name": "노이즈 마스크 가장자리 흐림", "tooltip": "인페인트시에 적용되는 노이즈 마스크의 가장자리를 흐리게 합니다. 이 설정값이 0을 초과할 경우, 내부적으로 자동으로 '차등 확산' 노드를 적용합니다."}, "scheduler_func_opt": {"name": "스케쥴러 함수", "tooltip": "GITS 스케쥴러 처럼 기본 스케쥴러 리스트에서 선택할 수 없는 특수 스케쥴러를 사용할 수 있게 해줍니다. 이 입력이 연결되면, 기본 스케쥴러 선택은 무시됩니다."}, "tiled_encode": {"name": "타일 인코드 사용", "tooltip": "이 옵션을 켜면, 내부적으로 'VAE 인코드'를 사용할 경우, 기본 'VAE 인코드' 대신 'VAE 인코드 (타일)' 을 적용합니다."}, "tiled_decode": {"name": "타일 디코드 사용", "tooltip": "이 옵션을 켜면, 내부적으로 'VAE 다코드'를 사용할 경우, 기본 'VAE 디코드' 대신 'VAE 디코드 (타일)' 을 적용합니다."}}, "outputs": {"0": {"name": "개선 이미지"}, "1": {"name": "잘라낸 이미지"}, "2": {"name": "잘라낸 개선 이미지"}, "3": {"name": "잘라낸 투명 개선 이미지"}, "4": {"name": "컨트롤넷 이미지"}}}, "DetailerForEachDebugPipe": {"description": "감지 영역 정보 묶음(SEGS)내의 각 영역들에 대해 가이드 크기를 기반으로 확대해서 인페이트하는 방법으로 디테일을 강화합니다.", "display_name": "디테일러 (상세/SEGS/파이프)", "inputs": {"image": {"name": "이미지"}, "segs": {"name": "segs", "tooltip": "감지 영역 정보를 담고 있는 묶음.\n이 영역들을 대상으로 인페인트가 적용됩니다."}, "guide_size": {"name": "가이드 크기", "tooltip": "'가이드 크기 대상'으로 지정된 크기의 가장 짧은면을 이 크기까지 확대합니다."}, "guide_size_for": {"name": "가이드 크기 대상", "tooltip": "bbox: 감지된 사각 영역(bbox)\ncrop_region: 잘라낸 영역"}, "max_size": {"name": "최대 크기", "tooltip": "'가이드 크기'로 확대 할 때, 가장 긴 면의 길이를 이 크기로 제한합니다. 너무 크게 확대 되는 것을 막아줍니다."}, "seed": {"name": "시드"}, "steps": {"name": "스텝수"}, "sampler_name": {"name": "샘플러 이름"}, "scheduler": {"name": "스케쥴러"}, "denoise": {"name": "노이즈 제거양"}, "feather": {"name": "가장자리 흐림", "tooltip": "확대 해서 인페인트 된 이미지를 원본 이미지에 붙여넣을 때, 이 수치로 마스크의 가장 자리를 흐리게 해서 붙여넣어 이음매의 위화감을 줄여줍니다."}, "noise_mask": {"name": "노이즈 마스크 사용", "tooltip": "인페인트 할 때, 마스크를 적용해서 마스크 영역만 인페인트합니다. 이 옵션을 적용하지 않으면, 잘라낸 이미지 전체가 재생성되어 노이즈 제거양이 클 때 위화감이 나타나게 됩니다."}, "force_inpaint": {"name": "인페인트 강제 적용", "tooltip": "가이드 크기와 상관 없이 인페인팅을 무조건 적용 합니다. 이 옵션이 꺼져있는 경우 가이드 크기보다 이미 큰 감지 영역은 인페인팅을 건너 뜁니다."}, "basic_pipe": {"name": "기본 파이프", "tooltip": "만약 기본 파이프 내의 모델에 `ImpactDummyInput` 가 설정된 경우, 인페인트 단계를 건너 뜁니다."}, "refiner_ratio": {"name": "라파이너 적용 비율", "tooltip": "SDXL 리파이너 모델을 사용할 경우 적용될 후반 스텝수 비율을 설정합니다."}, "cycle": {"name": "반복수", "tooltip": "설정된 값만큼 인페인팅을 반복 적용합니다. 인코딩/디코딩 없이 확대된 잠재 이미지 단계에서 반복됩니다."}, "detailer_hook": {"name": "디테일러 후크", "tooltip": "이 노드의 실행 중간단계에서 여러가지 기능을 수행할 수 있는 후크를 연결합니다."}, "refiner_basic_pipe_opt": {"name": "리파이너 기본 파이프", "tooltip": "SDXL 리파이너 단계에 적용할 기본 파이프를 연결합니다."}, "inpaint_model": {"name": "인페인트 모델 모드", "tooltip": "인페인트 전용 모델을 사용할 경우 이 옵션을 켜면, 인페인팅시에 '인페인트 모델 조건 설정'이 적용되어 수행됩니다."}, "noise_mask_feather": {"name": "노이즈 마스크 가장자리 흐림", "tooltip": "인페인트시에 적용되는 노이즈 마스크의 가장자리를 흐리게 합니다. 이 설정값이 0을 초과할 경우, 내부적으로 자동으로 '차등 확산' 노드를 적용합니다."}, "scheduler_func_opt": {"name": "스케쥴러 함수", "tooltip": "GITS 스케쥴러 처럼 기본 스케쥴러 리스트에서 선택할 수 없는 특수 스케쥴러를 사용할 수 있게 해줍니다. 이 입력이 연결되면, 기본 스케쥴러 선택은 무시됩니다."}, "tiled_encode": {"name": "타일 인코드 사용", "tooltip": "이 옵션을 켜면, 내부적으로 'VAE 인코드'를 사용할 경우, 기본 'VAE 인코드' 대신 'VAE 인코드 (타일)' 을 적용합니다."}, "tiled_decode": {"name": "타일 디코드 사용", "tooltip": "이 옵션을 켜면, 내부적으로 'VAE 다코드'를 사용할 경우, 기본 'VAE 디코드' 대신 'VAE 디코드 (타일)' 을 적용합니다."}}, "outputs": {"0": {"name": "개선 이미지"}, "1": {"name": "개선 SEGS"}, "2": {"name": "기본 파이프"}, "3": {"name": "잘라낸 이미지"}, "4": {"name": "잘라낸 개선 이미지"}, "5": {"name": "잘라낸 투명 개선 이미지"}, "6": {"name": "컨트롤넷 이미지"}}}, "DetailerForEachPipeForAnimateDiff": {"description": "감지 영역 정보 묶음(SEGS)내의 각 영역들에 대해 가이드 크기를 기반으로 확대해서 인페이트하는 방법으로 디테일을 강화합니다.\n이 노드는 AnimateDiff와 같은 동영상의 디테일 개선을 위한 특수 디테일러 노드로써, SEGS가 담고 있는 마스크가 여러 프레임에 걸친 배치 마스크가 되는 경우를 처리할 수 있습니다.", "display_name": "디테일러 (AnimateDiff/파이프)", "inputs": {"image_frames": {"name": "이미지 프레임 묶음"}, "segs": {"name": "segs", "tooltip": "감지 영역 정보를 담고 있는 묶음.\n이 영역들을 대상으로 인페인트가 적용됩니다."}, "guide_size": {"name": "가이드 크기", "tooltip": "'가이드 크기 대상'으로 지정된 크기의 가장 짧은면을 이 크기까지 확대합니다."}, "guide_size_for": {"name": "가이드 크기 대상", "tooltip": "bbox: 감지된 사각 영역(bbox)\ncrop_region: 잘라낸 영역"}, "max_size": {"name": "최대 크기", "tooltip": "'가이드 크기'로 확대 할 때, 가장 긴 면의 길이를 이 크기로 제한합니다. 너무 크게 확대 되는 것을 막아줍니다."}, "seed": {"name": "시드"}, "steps": {"name": "스텝수"}, "sampler_name": {"name": "샘플러 이름"}, "scheduler": {"name": "스케쥴러"}, "denoise": {"name": "노이즈 제거양"}, "feather": {"name": "가장자리 흐림", "tooltip": "확대 해서 인페인트 된 이미지를 원본 이미지에 붙여넣을 때, 이 수치로 마스크의 가장 자리를 흐리게 해서 붙여넣어 이음매의 위화감을 줄여줍니다."}, "basic_pipe": {"name": "기본 파이프", "tooltip": "만약 기본 파이프 내의 모델에 `ImpactDummyInput` 가 설정된 경우, 인페인트 단계를 건너 뜁니다."}, "refiner_ratio": {"name": "라파이너 적용 비율", "tooltip": "SDXL 리파이너 모델을 사용할 경우 적용될 후반 스텝수 비율을 설정합니다."}, "detailer_hook": {"name": "디테일러 후크", "tooltip": "이 노드의 실행 중간단계에서 여러가지 기능을 수행할 수 있는 후크를 연결합니다."}, "refiner_basic_pipe_opt": {"name": "리파이너 기본 파이프", "tooltip": "SDXL 리파이너 단계에 적용할 기본 파이프를 연결합니다."}, "noise_mask_feather": {"name": "노이즈 마스크 가장자리 흐림", "tooltip": "인페인트시에 적용되는 노이즈 마스크의 가장자리를 흐리게 합니다. 이 설정값이 0을 초과할 경우, 내부적으로 자동으로 '차등 확산' 노드를 적용합니다."}, "scheduler_func_opt": {"name": "스케쥴러 함수", "tooltip": "GITS 스케쥴러 처럼 기본 스케쥴러 리스트에서 선택할 수 없는 특수 스케쥴러를 사용할 수 있게 해줍니다. 이 입력이 연결되면, 기본 스케쥴러 선택은 무시됩니다."}}, "outputs": {"0": {"name": "개선 SEGS"}, "1": {"name": "개선 이미지"}, "2": {"name": "기본 파이프"}, "3": {"name": "컨트롤넷 이미지"}}}, "SEGSDetailerForAnimateDiff": {"description": "감지 영역 정보 묶음(SEGS)내의 각 영역들에 대해 가이드 크기를 기반으로 확대해서 인페이트하는 방법으로 디테일을 강화합니다.\n이 노드는 원본 이미지가 아닌 SEGS를 대상으로 적용되는 노드로 원본 이미지에 적용하려면 'SEGS 붙여넣기' 노드를 사용하세요.\n이 노드는 AnimateDiff와 같은 동영상의 디테일 개선을 위한 특수 디테일러 노드로써, SEGS가 담고 있는 마스크가 여러 프레임에 걸친 배치 마스크가 되는 경우를 처리할 수 있습니다.", "display_name": "SEGS 디테일러 (AnimateDiff/파이프)", "inputs": {"image_frames": {"name": "이미지 프레임 묶음"}, "segs": {"name": "segs", "tooltip": "감지 영역 정보를 담고 있는 묶음.\n이 영역들을 대상으로 인페인트가 적용됩니다."}, "guide_size": {"name": "가이드 크기", "tooltip": "'가이드 크기 대상'으로 지정된 크기의 가장 짧은면을 이 크기까지 확대합니다."}, "guide_size_for": {"name": "가이드 크기 대상", "tooltip": "bbox: 감지된 사각 영역(bbox)\ncrop_region: 잘라낸 영역"}, "max_size": {"name": "최대 크기", "tooltip": "'가이드 크기'로 확대 할 때, 가장 긴 면의 길이를 이 크기로 제한합니다. 너무 크게 확대 되는 것을 막아줍니다."}, "seed": {"name": "시드"}, "steps": {"name": "스텝수"}, "sampler_name": {"name": "샘플러 이름"}, "scheduler": {"name": "스케쥴러"}, "denoise": {"name": "노이즈 제거양"}, "basic_pipe": {"name": "기본 파이프", "tooltip": "만약 기본 파이프 내의 모델에 `ImpactDummyInput` 가 설정된 경우, 인페인트 단계를 건너 뜁니다."}, "refiner_ratio": {"name": "라파이너 적용 비율", "tooltip": "SDXL 리파이너 모델을 사용할 경우 적용될 후반 스텝수 비율을 설정합니다."}, "refiner_basic_pipe_opt": {"name": "리파이너 기본 파이프", "tooltip": "SDXL 리파이너 단계에 적용할 기본 파이프를 연결합니다."}, "noise_mask_feather": {"name": "노이즈 마스크 가장자리 흐림", "tooltip": "인페인트시에 적용되는 노이즈 마스크의 가장자리를 흐리게 합니다. 이 설정값이 0을 초과할 경우, 내부적으로 자동으로 '차등 확산' 노드를 적용합니다."}, "scheduler_func_opt": {"name": "스케쥴러 함수", "tooltip": "GITS 스케쥴러 처럼 기본 스케쥴러 리스트에서 선택할 수 없는 특수 스케쥴러를 사용할 수 있게 해줍니다. 이 입력이 연결되면, 기본 스케쥴러 선택은 무시됩니다."}}, "outputs": {"0": {"name": "개선 SEGS"}, "1": {"name": "개선 이미지"}}}, "SEGSDetailer": {"description": "감지 영역 정보 묶음(SEGS)내의 각 영역들에 대해 가이드 크기를 기반으로 확대해서 인페이트하는 방법으로 디테일을 강화합니다.\n이 노드는 원본 이미지가 아닌 SEGS를 대상으로 적용되는 노드로 원본 이미지에 적용하려면 'SEGS 붙여넣기' 노드를 사용하세요.", "display_name": "SEGS 디테일러 (파이프)", "inputs": {"image": {"name": "이미지"}, "segs": {"name": "segs", "tooltip": "감지 영역 정보를 담고 있는 묶음.\n이 영역들을 대상으로 인페인트가 적용됩니다."}, "guide_size": {"name": "가이드 크기", "tooltip": "'가이드 크기 대상'으로 지정된 크기의 가장 짧은면을 이 크기까지 확대합니다."}, "guide_size_for": {"name": "가이드 크기 대상", "tooltip": "bbox: 감지된 사각 영역(bbox)\ncrop_region: 잘라낸 영역"}, "max_size": {"name": "최대 크기", "tooltip": "'가이드 크기'로 확대 할 때, 가장 긴 면의 길이를 이 크기로 제한합니다. 너무 크게 확대 되는 것을 막아줍니다."}, "seed": {"name": "시드"}, "steps": {"name": "스텝수"}, "sampler_name": {"name": "샘플러 이름"}, "scheduler": {"name": "스케쥴러"}, "denoise": {"name": "노이즈 제거양"}, "noise_mask": {"name": "노이즈 마스크 사용", "tooltip": "인페인트 할 때, 마스크를 적용해서 마스크 영역만 인페인트합니다. 이 옵션을 적용하지 않으면, 잘라낸 이미지 전체가 재생성되어 노이즈 제거양이 클 때 위화감이 나타나게 됩니다."}, "force_inpaint": {"name": "인페인트 강제 적용", "tooltip": "가이드 크기와 상관 없이 인페인팅을 무조건 적용 합니다. 이 옵션이 꺼져있는 경우 가이드 크기보다 이미 큰 감지 영역은 인페인팅을 건너 뜁니다."}, "basic_pipe": {"name": "기본 파이프", "tooltip": "만약 기본 파이프 내의 모델에 `ImpactDummyInput` 가 설정된 경우, 인페인트 단계를 건너 뜁니다."}, "refiner_ratio": {"name": "라파이너 적용 비율", "tooltip": "SDXL 리파이너 모델을 사용할 경우 적용될 후반 스텝수 비율을 설정합니다."}, "batch_size": {"name": "배치 갯수", "tooltip": "대상 SEGS 에 대해서 배치 갯수만큼 여러개의 후보를 생성합니다. 여러개를 생성할 경우 '고르기 (SEGS)'와 함께 사용하세요."}, "cycle": {"name": "반복수", "tooltip": "설정된 값만큼 인페인팅을 반복 적용합니다. 인코딩/디코딩 없이 확대된 잠재 이미지 단계에서 반복됩니다."}, "refiner_basic_pipe_opt": {"name": "리파이너 기본 파이프", "tooltip": "SDXL 리파이너 단계에 적용할 기본 파이프를 연결합니다."}, "inpaint_model": {"name": "인페인트 모델 모드", "tooltip": "인페인트 전용 모델을 사용할 경우 이 옵션을 켜면, 인페인팅시에 '인페인트 모델 조건 설정'이 적용되어 수행됩니다."}, "noise_mask_feather": {"name": "노이즈 마스크 가장자리 흐림", "tooltip": "인페인트시에 적용되는 노이즈 마스크의 가장자리를 흐리게 합니다. 이 설정값이 0을 초과할 경우, 내부적으로 자동으로 '차등 확산' 노드를 적용합니다."}, "scheduler_func_opt": {"name": "스케쥴러 함수", "tooltip": "GITS 스케쥴러 처럼 기본 스케쥴러 리스트에서 선택할 수 없는 특수 스케쥴러를 사용할 수 있게 해줍니다. 이 입력이 연결되면, 기본 스케쥴러 선택은 무시됩니다."}}, "outputs": {"0": {"name": "개선 SEGS"}, "1": {"name": "컨트롤넷 이미지"}}}, "MaskDetailerPipe": {"description": "이 디테일러 노드는 마스크로 설정된 영역을 확대해서 가이드 크기를 기반으로 확대해서 인페인트하는 방법으로 디테일을 강화합니다.", "display_name": "마스크 디테일러 (파이프)", "inputs": {"image": {"name": "이미지"}, "mask": {"name": "마스크", "tooltip": "디테일을 강화하고 싶은 대상 영역이 설정된 마스크. 분리된 마스크 영역은 개별적으로 디테일 강화가 이루어집니다."}, "basic_pipe": {"name": "기본 파이프", "tooltip": "만약 기본 파이프 내의 모델에 `ImpactDummyInput` 가 설정된 경우, 인페인트 단계를 건너 뜁니다."}, "guide_size": {"name": "가이드 크기", "tooltip": "'가이드 크기 대상'으로 지정된 크기의 가장 짧은면을 이 크기까지 확대합니다."}, "guide_size_for": {"name": "가이드 크기 대상", "tooltip": "bbox: 감지된 사각 영역(bbox)\ncrop_region: 잘라낸 영역"}, "max_size": {"name": "최대 크기", "tooltip": "'가이드 크기'로 확대 할 때, 가장 긴 면의 길이를 이 크기로 제한합니다. 너무 크게 확대 되는 것을 막아줍니다."}, "mask_mode": {"name": "마스크 모드", "tooltip": "마스크로 설정된 영역만을 인페인트 할지, 잘라낸 영역 전체를 인페인트 할 것인지를 설정합니다."}, "seed": {"name": "시드"}, "steps": {"name": "스텝수"}, "sampler_name": {"name": "샘플러 이름"}, "scheduler": {"name": "스케쥴러"}, "denoise": {"name": "노이즈 제거양"}, "feather": {"name": "가장자리 흐림", "tooltip": "확대 해서 인페인트 된 이미지를 원본 이미지에 붙여넣을 때, 이 수치로 마스크의 가장 자리를 흐리게 해서 붙여넣어 이음매의 위화감을 줄여줍니다."}, "crop_factor": {"name": "자르기 배율", "tooltip": "각 마스크로 설정된 영역에 대해서 몇배 크기를 잘라내어서 인페인트에 사용할지를 설정합니다. 이 크기가 너무 작으면, 인페인트 할 이미지의 주변 정보가 부족해서 위화감이 강한 이미지가 생성됩니다. 이 크기가 너무 크면, 인페인팅에 너무 오랜 시간이 걸릴 수 있으며, 모델의 역량을 초과할 정도로 클 경우 올바르지 않은 이미지를 생성하게 됩니다."}, "drop_size": {"name": "감지 최소 크기", "tooltip": "사각 영역(bbox) 감지기로 감지한 크기가 이 설정값 보다 작을 경우 무시합니다."}, "refiner_ratio": {"name": "라파이너 적용 비율", "tooltip": "SDXL 리파이너 모델을 사용할 경우 적용될 후반 스텝수 비율을 설정합니다."}, "batch_size": {"name": "배치 갯수", "tooltip": "대상 SEGS 에 대해서 배치 갯수만큼 여러개의 후보를 생성합니다. 여러개를 생성할 경우 '고르기 (SEGS)'와 함께 사용하세요."}, "cycle": {"name": "반복수", "tooltip": "설정된 값만큼 인페인팅을 반복 적용합니다. 인코딩/디코딩 없이 확대된 잠재 이미지 단계에서 반복됩니다."}, "refiner_basic_pipe_opt": {"name": "리파이너 기본 파이프", "tooltip": "SDXL 리파이너 단계에 적용할 기본 파이프를 연결합니다."}, "detailer_hook": {"name": "디테일러 후크", "tooltip": "이 노드의 실행 중간단계에서 여러가지 기능을 수행할 수 있는 후크를 연결합니다."}, "inpaint_model": {"name": "인페인트 모델 모드", "tooltip": "인페인트 전용 모델을 사용할 경우 이 옵션을 켜면, 인페인팅시에 '인페인트 모델 조건 설정'이 적용되어 수행됩니다."}, "noise_mask_feather": {"name": "노이즈 마스크 가장자리 흐림", "tooltip": "인페인트시에 적용되는 노이즈 마스크의 가장자리를 흐리게 합니다. 이 설정값이 0을 초과할 경우, 내부적으로 자동으로 '차등 확산' 노드를 적용합니다."}, "bbox_fill": {"name": "bbox 채우기", "tooltip": "각 마스크 조각들을 해당 마스크를 포함하는 가장 작은 사각 영역의 마스크로 간주합니다."}, "contour_fill": {"name": "윤곽 내부 채우기", "tooltip": "윤곽선 형태의 마스크 조각들의 경우 마스크 내부가 모두 채워진 것으로 간주합니다."}, "scheduler_func_opt": {"name": "스케쥴러 함수", "tooltip": "GITS 스케쥴러 처럼 기본 스케쥴러 리스트에서 선택할 수 없는 특수 스케쥴러를 사용할 수 있게 해줍니다. 이 입력이 연결되면, 기본 스케쥴러 선택은 무시됩니다."}}, "outputs": {"0": {"name": "개선 이미지"}, "1": {"name": "잘라낸 개선 이미지"}, "2": {"name": "잘라낸 투명 개선 이미지"}, "3": {"name": "기본 파이프"}, "4": {"name": "리파이너 기본 파이프"}}}, "SEGSPaste": {"description": "SEGS 디테일러를 통해 개선된 SEGS를 원본 이미지에 붙여넣는 기능을 제공하기 위한 노드입니다.", "display_name": "SEGS 붙여넣기", "inputs": {"image": {"name": "원본 이미지"}, "segs": {"name": "segs"}, "feather": {"name": "가장자리 흐림", "tooltip": "개선된 SEGS의 이미지를 원본 이미지에 붙여넣을 때, 이 수치로 마스크의 가장 자리를 흐리게 해서 붙여넣어 이음매의 위화감을 줄여줍니다."}, "alpha": {"name": "투명도", "tooltip": "원본에 붙여넣는 이미지에 투명도를 설정합니다."}, "ref_image_opt": {"name": "참조 이미지", "tooltip": "디테일러를 통과시키거나 'SEGS에 기본 이미지 설정'을 한 경우가 아니라면, SEGS는 이미지가 없이 감지 영역 정보만 있습니다. 이 때 감지영역이 참조할 원본 이미지를 설정합니다."}}, "outputs": {"0": {"name": "개선 SEGS"}}}, "ImpactSEGSPicker": {"description": "입력된 SEGS 중에서 선택된 SEGS만을 고를 수 있는 있는 기능을 제공합니다.", "display_name": "고르기 (SEGS)", "inputs": {"picks": {"name": "선택 목록", "tooltip": "출력할 SEGS 번호 목록을 나열합니다. 'pick' 버튼을 눌러서 선택하세요."}, "segs": {"name": "segs"}, "fallback_image_opt": {"name": "참조 이미지", "tooltip": "디테일러를 통과시키거나 'SEGS에 기본 이미지 설정'을 한 경우가 아니라면, SEGS는 이미지가 없이 감지 영역 정보만 있습니다. 이 때 감지영역이 참조할 원본 이미지를 설정합니다."}}, "outputs": {"0": {"name": "선택된 SEGS"}}}, "SetDefaultImageForSEGS": {"description": "디테일러를 통과시킨 경우가 아니라면, SEGS는 이미지가 없이 감지 영역 정보만 있습니다. 이 노드는 SEGS에 기본 이미지를 설정해 줍니다.", "display_name": "SEGS에 기본 이미지 설정", "inputs": {"segs": {"name": "segs"}, "image": {"name": "이미지"}, "override": {"name": "덮어쓰기", "tooltip": "이미 설정된 이미지가 있는 경우 덮어쓸지 여부를 설정합니다."}}, "outputs": {"0": {"name": "segs"}}}, "ImpactWildcardProcessor": {"description": "이 노드는 와일드카드 구문으로 작성된 텍스트 프롬프트를 처리하고, 처리된 텍스트 프롬프트를 출력합니다.\n\nTIP: 워크플로가 실행되기 전에 '와일드카드 텍스트'의 처리 결과가 '채워진(populated) 텍스트'에 표시되며, 이 값은 워크플로와 함께 저장됩니다. 입력으로 변환된 시드를 사용하려면 '와일드카드 텍스트' 대신 '채워진(populated) 텍스트'에 직접 프롬프트를 작성하고, 모드를 '고정(fixed)'로 설정하세요.", "display_name": "와일드카드 처리기 (Impact)", "inputs": {"wildcard_text": {"name": "와일드카드 텍스트", "tooltip": "와일드카드 문법으로 작성된 텍스트 프롬프트를 입력하세요."}, "populated_text": {"name": "채워진 텍스트", "tooltip": "이 노드에 실행 중에 전달되는 실제 값은 여기 표시된 값입니다. 동작은 모드에 따라 약간 다를 수 있으며, '채워진 텍스트'에서도 와일드카드 구문을 사용할 수 있습니다."}, "mode": {"name": "모드", "tooltip": "채우기(populate): 워크플로를 실행하기 전에 '와일드카드 텍스트'에서 처리된 프롬프트로 '채워진 텍스트'의 기존 값을 덮어씁니다. 이 모드에서는 '채워진 텍스트'를 수정할 수 없습니다.\n\n고정(fixed): '와일드카드 텍스트'를 무시하고 '채워진 텍스트'의 값을 그대로 유지합니다. 이 모드에서는 '채워진 텍스트'를 수정할 수 있습니다.\n\n재현(reproduce): 이 모드는 한 번만 '고정(fixed)' 모드로 작동하여 재현한 후, 이후에는 '채우기(populate)' 모드로 전환됩니다."}, "seed": {"name": "시드", "tooltip": "와일드카드의 무작위 선택에 사용할 시드 입니다"}, "Select to add Wildcard": {"name": "추가할 와일드카드 선택"}}, "outputs": {"0": {"name": "처리된 텍스트"}}}, "ImpactWildcardEncode": {"description": "이 노드는 와일드카드 구문으로 작성된 텍스트 프롬프트를 처리하고 이를 조건으로 출력합니다. 또한 LoRA 구문을 지원하며, 적용된 LoRA는 모델 출력에 반영됩니다.\n\nTIP1: 워크플로가 실행되기 전에 '와일드카드 텍스트'의 처리 결과가 '채워진 텍스트'에 표시되며, 이 값은 워크플로와 함께 저장됩니다. 입력으로 변환된 시드를 사용하려면 '와일드카드 텍스트' 대신 '채워진 텍스트'에 직접 프롬프트를 작성하고, 모드를 '고정(fixed)'로 설정하세요.\nTIP2: 'Inspire Pack'이 설치되어 있으면 LBW(로라 블록 웨이트) 구문도 적용할 수 있습니다.", "display_name": "와일드카드 처리기 (Impact)", "inputs": {"wildcard_text": {"name": "와일드카드 텍스트", "tooltip": "와일드카드 문법으로 작성된 텍스트 프롬프트를 입력하세요."}, "populated_text": {"name": "채워진 텍스트", "tooltip": "이 노드에 실행 중에 전달되는 실제 값은 여기 표시된 값입니다. 동작은 모드에 따라 약간 다를 수 있으며, '채워진 텍스트'에서도 와일드카드 구문을 사용할 수 있습니다."}, "mode": {"name": "모드", "tooltip": "채우기(populate): 워크플로를 실행하기 전에 '와일드카드 텍스트'에서 처리된 프롬프트로 '채워진 텍스트'의 기존 값을 덮어씁니다. 이 모드에서는 '채워진 텍스트'를 수정할 수 없습니다.\n\n고정(fixed): '와일드카드 텍스트'를 무시하고 '채워진 텍스트'의 값을 그대로 유지합니다. 이 모드에서는 '채워진 텍스트'를 수정할 수 있습니다.\n\n재현(reproduce): 이 모드는 한 번만 '고정(fixed)' 모드로 작동하여 재현한 후, 이후에는 '채우기(populate)' 모드로 전환됩니다."}, "Select to add LoRA": {"name": "추가할 LoRA 선택"}, "Select to add Wildcard": {"name": "추가할 와일드카드 선택"}, "seed": {"name": "시드", "tooltip": "와일드카드의 무작위 선택에 사용할 시드 입니다"}}, "outputs": {"0": {"name": "model", "tooltip": "LoRA 적용 문법이 사용된 경우, LoRA 가 적용된 model이 출력됩니다."}, "1": {"name": "clip", "tooltip": "LoRA 적용 문법이 사용된 경우, LoRA 가 적용된 clip이 출력됩니다."}, "2": {"name": "조건"}, "3": {"name": "채워진 텍스트"}}}}