{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {"id": "aaaaaaaaaa"}, "source": ["Git clone the repo and install the requirements. (ignore the pip errors about protobuf)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "bbbbbbbbbb"}, "outputs": [], "source": ["#@title Environment Setup\n", "\n", "from pathlib import Path\n", "\n", "OPTIONS = {}\n", "\n", "WORKSPACE = 'ComfyUI'\n", "USE_GOOGLE_DRIVE = True  #@param {type:\"boolean\"}\n", "UPDATE_COMFY_UI = True  #@param {type:\"boolean\"}\n", "\n", "OPTIONS['USE_GOOGLE_DRIVE'] = USE_GOOGLE_DRIVE\n", "OPTIONS['UPDATE_COMFY_UI'] = UPDATE_COMFY_UI\n", "\n", "if OPTIONS['USE_GOOGLE_DRIVE']:\n", "    !echo \"Mounting Google Drive...\"\n", "    %cd /\n", "    \n", "    from google.colab import drive\n", "    drive.mount('/content/drive')\n", "\n", "    WORKSPACE = \"/content/drive/MyDrive/ComfyUI\"\n", "    \n", "    %cd /content/drive/MyDrive\n", "\n", "![ ! -d $WORKSPACE ] && echo \"-= Initial setup ComfyUI (Original)=-\" && git clone https://github.com/comfyanonymous/ComfyUI\n", "%cd $WORKSPACE\n", "\n", "if OPTIONS['UPDATE_COMFY_UI']:\n", "  !echo \"-= Updating ComfyUI =-\"\n", "  !git pull\n", "  !rm \"/content/drive/MyDrive/ComfyUI/custom_nodes/comfyui-impact-pack.py\"\n", "\n", "%cd custom_nodes\n", "!git clone https://github.com/ltdrdata/ComfyUI-Impact-Pack\n", "%cd $WORKSPACE\n", "\n", "!echo -= Install dependencies =-\n", "!pip -q install xformers -r requirements.txt\n"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {"id": "kkkkkkkkkkkkkk"}, "source": ["### Run ComfyUI with localtunnel (Recommended Way)\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "jjjjjjjjjjjjj", "outputId": "83be9411-d939-4813-e6c1-80e75bf8e80d"}, "outputs": [], "source": ["!npm install -g localtunnel\n", "\n", "import subprocess\n", "import threading\n", "import time\n", "import socket\n", "def iframe_thread(port):\n", "  while True:\n", "      time.sleep(0.5)\n", "      sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)\n", "      result = sock.connect_ex(('127.0.0.1', port))\n", "      if result == 0:\n", "        break\n", "      sock.close()\n", "  print(\"\\nComfyUI finished loading, trying to launch localtunnel (if it gets stuck here localtunnel is having issues)\")\n", "  p = subprocess.Popen([\"lt\", \"--port\", \"{}\".format(port)], stdout=subprocess.PIPE)\n", "  for line in p.stdout:\n", "    print(line.decode(), end='')\n", "\n", "\n", "threading.Thread(target=iframe_thread, daemon=True, args=(8188,)).start()\n", "\n", "!python main.py --dont-print-server"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {"id": "gggggggggg"}, "source": ["### Run ComfyUI with colab iframe (use only in case the previous way with localtunnel doesn't work)\n", "\n", "You should see the ui appear in an iframe. If you get a 403 error, it's your firefox settings or an extension that's messing things up.\n", "\n", "If you want to open it in another window use the link.\n", "\n", "Note that some UI features like live image previews won't work because the colab iframe blocks websockets."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "hhhhhhhhhh"}, "outputs": [], "source": ["import threading\n", "import time\n", "import socket\n", "def iframe_thread(port):\n", "  while True:\n", "      time.sleep(0.5)\n", "      sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)\n", "      result = sock.connect_ex(('127.0.0.1', port))\n", "      if result == 0:\n", "        break\n", "      sock.close()\n", "  from google.colab import output\n", "  output.serve_kernel_port_as_iframe(port, height=1024)\n", "  print(\"to open it in a window you can open this link here:\")\n", "  output.serve_kernel_port_as_window(port)\n", "\n", "threading.Thread(target=iframe_thread, daemon=True, args=(8188,)).start()\n", "\n", "!python main.py --dont-print-server"]}], "metadata": {"accelerator": "GPU", "colab": {"provenance": []}, "gpuClass": "standard", "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}