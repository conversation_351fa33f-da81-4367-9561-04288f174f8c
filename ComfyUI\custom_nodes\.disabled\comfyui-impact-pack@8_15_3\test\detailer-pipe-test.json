{"last_node_id": 87, "last_link_id": 214, "nodes": [{"id": 7, "type": "CLIPTextEncode", "pos": [413, 389], "size": {"0": 425.27801513671875, "1": 180.6060791015625}, "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 5, "label": "clip"}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [6], "slot_index": 0, "label": "CONDITIONING"}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["text, watermark, worst quality:1.4, low quality:1.4"]}, {"id": 6, "type": "CLIPTextEncode", "pos": [415, 186], "size": {"0": 422.84503173828125, "1": 164.31304931640625}, "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 3, "label": "clip"}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [4], "slot_index": 0, "label": "CONDITIONING"}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["photorealistic:1.4, best quality:1.4, 2 girls on table "]}, {"id": 5, "type": "EmptyLatentImage", "pos": [473, 609], "size": {"0": 315, "1": 106}, "flags": {}, "order": 0, "mode": 0, "outputs": [{"name": "LATENT", "type": "LATENT", "links": [2], "slot_index": 0, "label": "LATENT"}], "properties": {"Node name for S&R": "EmptyLatentImage"}, "widgets_values": [1024, 768, 1]}, {"id": 8, "type": "VAEDecode", "pos": [1209, 188], "size": {"0": 210, "1": 46}, "flags": {}, "order": 12, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 7, "label": "samples"}, {"name": "vae", "type": "VAE", "link": 8, "label": "vae"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [10], "slot_index": 0, "label": "IMAGE"}], "properties": {"Node name for S&R": "VAEDecode"}}, {"id": 30, "type": "PreviewImage", "pos": [2532, -7], "size": {"0": 575.2411499023438, "1": 561.0116577148438}, "flags": {}, "order": 16, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 179, "label": "images"}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 24, "type": "SAMLoader", "pos": [861, 1300], "size": {"0": 315, "1": 82}, "flags": {}, "order": 1, "mode": 0, "outputs": [{"name": "SAM_MODEL", "type": "SAM_MODEL", "links": [19, 33], "shape": 3, "slot_index": 0, "label": "SAM_MODEL"}], "properties": {"Node name for S&R": "SAMLoader"}, "widgets_values": ["sam_vit_b_01ec64.pth", "AUTO"]}, {"id": 32, "type": "BasicPipeToDetailerPipe", "pos": [1396, 1143], "size": {"0": 400, "1": 200}, "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "basic_pipe", "type": "BASIC_PIPE", "link": 34, "label": "basic_pipe"}, {"name": "bbox_detector", "type": "BBOX_DETECTOR", "link": 202, "slot_index": 1, "label": "bbox_detector"}, {"name": "sam_model_opt", "type": "SAM_MODEL", "link": 33, "slot_index": 2, "label": "sam_model_opt"}, {"name": "segm_detector_opt", "type": "SEGM_DETECTOR", "link": 213, "label": "segm_detector_opt"}, {"name": "detailer_hook", "type": "DETAILER_HOOK", "link": null, "label": "detailer_hook"}], "outputs": [{"name": "detailer_pipe", "type": "DETAILER_PIPE", "links": [36], "shape": 3, "slot_index": 0, "label": "detailer_pipe"}], "properties": {"Node name for S&R": "BasicPipeToDetailerPipe"}, "widgets_values": ["photorealistic:1.4, best quality:1.4, detailed eyes, \n__face_loras__ [faint smile|surprise|laugh]", "Select the LoRA to add to the text"]}, {"id": 36, "type": "MaskToImage", "pos": [2650, 1230], "size": {"0": 210, "1": 26}, "flags": {}, "order": 20, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 182, "label": "mask"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [59], "shape": 3, "slot_index": 0, "label": "IMAGE"}], "properties": {"Node name for S&R": "MaskToImage"}}, {"id": 52, "type": "BboxDetectorSEGS", "pos": [4948, 677], "size": {"0": 315, "1": 150}, "flags": {}, "order": 33, "mode": 0, "inputs": [{"name": "bbox_detector", "type": "BBOX_DETECTOR", "link": 85, "label": "bbox_detector"}, {"name": "image", "type": "IMAGE", "link": 188, "label": "image"}], "outputs": [{"name": "SEGS", "type": "SEGS", "links": [87, 160], "shape": 3, "slot_index": 0, "label": "SEGS"}], "properties": {"Node name for S&R": "BboxDetectorSEGS"}, "widgets_values": [0.5, 10, 3, 10]}, {"id": 46, "type": "DetailerPipeToBasicPipe", "pos": [4753, 1188], "size": {"0": 304.79998779296875, "1": 26}, "flags": {}, "order": 31, "mode": 0, "inputs": [{"name": "detailer_pipe", "type": "DETAILER_PIPE", "link": 77, "label": "detailer_pipe"}], "outputs": [{"name": "basic_pipe", "type": "BASIC_PIPE", "links": [155, 196], "shape": 3, "slot_index": 0, "label": "basic_pipe"}], "properties": {"Node name for S&R": "DetailerPipeToBasicPipe"}}, {"id": 60, "type": "PreviewImage", "pos": [6270, 2420], "size": {"0": 600, "1": 670}, "flags": {}, "order": 39, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 166, "label": "images"}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 57, "type": "PreviewImage", "pos": [5997, 1424], "size": {"0": 840, "1": 640}, "flags": {}, "order": 46, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 144, "label": "images"}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 54, "type": "PreviewImage", "pos": [6486, 705], "size": {"0": 740, "1": 580}, "flags": {}, "order": 51, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 197, "label": "images"}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 64, "type": "PreviewImage", "pos": [6800, -300], "size": {"0": 570, "1": 590}, "flags": {}, "order": 47, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 156, "label": "images"}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 42, "type": "PreviewImage", "pos": [4070, 636], "size": {"0": 210, "1": 246}, "flags": {}, "order": 26, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 187, "label": "images"}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 43, "type": "MaskToImage", "pos": [4081, 949], "size": {"0": 176.39999389648438, "1": 26}, "flags": {}, "order": 28, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 190, "label": "mask"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [75], "shape": 3, "slot_index": 0, "label": "IMAGE"}], "properties": {"Node name for S&R": "MaskToImage"}}, {"id": 44, "type": "PreviewImage", "pos": [4072, 1029], "size": {"0": 210, "1": 246}, "flags": {}, "order": 30, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 75, "label": "images"}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 37, "type": "PreviewImage", "pos": [2890, 1250], "size": {"0": 210, "1": 246}, "flags": {}, "order": 22, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 59, "label": "images"}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 22, "type": "BasicPipeToDetailerPipe", "pos": [1396, 866], "size": {"0": 400, "1": 200}, "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "basic_pipe", "type": "BASIC_PIPE", "link": 17, "label": "basic_pipe"}, {"name": "bbox_detector", "type": "BBOX_DETECTOR", "link": 201, "slot_index": 1, "label": "bbox_detector"}, {"name": "sam_model_opt", "type": "SAM_MODEL", "link": 19, "slot_index": 2, "label": "sam_model_opt"}, {"name": "segm_detector_opt", "type": "SEGM_DETECTOR", "link": 212, "label": "segm_detector_opt"}, {"name": "detailer_hook", "type": "DETAILER_HOOK", "link": null, "label": "detailer_hook"}], "outputs": [{"name": "detailer_pipe", "type": "DETAILER_PIPE", "links": [], "shape": 3, "slot_index": 0, "label": "detailer_pipe"}], "properties": {"Node name for S&R": "BasicPipeToDetailerPipe"}, "widgets_values": ["photorealistic:1.4, best quality:1.4, detailed eyes, \n[<lora:android_18_v110>|<lora:yorha_noDOT_2_type_b>|<lora:sailor_venus_v2>|<lora:<PERSON><PERSON><PERSON><PERSON>>] [faint smile|surprise|laugh]", "Select the LoRA to add to the text"]}, {"id": 75, "type": "PreviewImage", "pos": [2600, 1330], "size": {"0": 210, "1": 246}, "flags": {}, "order": 19, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 181, "label": "images"}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 10, "type": "PreviewBridge", "pos": [1462, 175], "size": {"0": 315, "1": 290}, "flags": {}, "order": 13, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 10, "label": "images"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [169, 183], "shape": 3, "slot_index": 0, "label": "IMAGE"}, {"name": "MASK", "type": "MASK", "links": null, "shape": 3, "label": "MASK"}], "properties": {"Node name for S&R": "PreviewBridge"}, "widgets_values": ["#placeholder"]}, {"id": 41, "type": "PreviewImage", "pos": [4301, 119], "size": {"0": 492.20916748046875, "1": 448.6293029785156}, "flags": {}, "order": 25, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 186, "label": "images"}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 78, "type": "PreviewImage", "pos": [4075, 1364], "size": {"0": 210, "1": 246}, "flags": {}, "order": 27, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 189, "label": "images"}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 3, "type": "K<PERSON><PERSON><PERSON>", "pos": [863, 183], "size": {"0": 315, "1": 474}, "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 1, "label": "model"}, {"name": "positive", "type": "CONDITIONING", "link": 4, "label": "positive"}, {"name": "negative", "type": "CONDITIONING", "link": 6, "label": "negative"}, {"name": "latent_image", "type": "LATENT", "link": 2, "label": "latent_image"}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [7], "slot_index": 0, "label": "LATENT"}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [885412539640489, "fixed", 15, 8, "euler", "normal", 1]}, {"id": 45, "type": "EditDetailerPipe", "pos": [4338, 950], "size": {"0": 284.0971374511719, "1": 316.5133361816406}, "flags": {}, "order": 29, "mode": 0, "inputs": [{"name": "detailer_pipe", "type": "DETAILER_PIPE", "link": 191, "label": "detailer_pipe"}, {"name": "model", "type": "MODEL", "link": null, "label": "model"}, {"name": "clip", "type": "CLIP", "link": null, "label": "clip"}, {"name": "vae", "type": "VAE", "link": null, "label": "vae"}, {"name": "positive", "type": "CONDITIONING", "link": null, "label": "positive"}, {"name": "negative", "type": "CONDITIONING", "link": null, "label": "negative"}, {"name": "bbox_detector", "type": "BBOX_DETECTOR", "link": null, "label": "bbox_detector"}, {"name": "sam_model", "type": "SAM_MODEL", "link": null, "label": "sam_model"}, {"name": "segm_detector_opt", "type": "SEGM_DETECTOR", "link": null, "label": "segm_detector_opt"}, {"name": "detailer_hook", "type": "DETAILER_HOOK", "link": null, "label": "detailer_hook"}], "outputs": [{"name": "detailer_pipe", "type": "DETAILER_PIPE", "links": [77, 82], "shape": 3, "slot_index": 0, "label": "detailer_pipe"}], "properties": {"Node name for S&R": "EditDetailerPipe"}, "widgets_values": ["", "Select the LoRA to add to the text"]}, {"id": 65, "type": "PreviewImage", "pos": [6430, -300], "size": {"0": 330, "1": 250}, "flags": {}, "order": 48, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 157, "label": "images"}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 53, "type": "MaskToSEGS", "pos": [5558, 989], "size": {"0": 315, "1": 130}, "flags": {}, "order": 38, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 88, "label": "mask"}], "outputs": [{"name": "SEGS", "type": "SEGS", "links": [138, 154, 195], "shape": 3, "slot_index": 0, "label": "SEGS"}], "properties": {"Node name for S&R": "MaskToSEGS"}, "widgets_values": [false, 3, false, 10]}, {"id": 81, "type": "DetailerForEachPipe", "pos": [6092, 708], "size": {"0": 329.5368957519531, "1": 598}, "flags": {}, "order": 45, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 194, "label": "image"}, {"name": "segs", "type": "SEGS", "link": 195, "label": "segs"}, {"name": "basic_pipe", "type": "BASIC_PIPE", "link": 196, "label": "basic_pipe"}, {"name": "detailer_hook", "type": "DETAILER_HOOK", "link": null, "label": "detailer_hook"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [197], "shape": 3, "slot_index": 0, "label": "IMAGE"}], "properties": {"Node name for S&R": "DetailerForEachPipe"}, "widgets_values": [256, true, 768, 44457634171318, "fixed", 20, 8, "euler", "normal", 0.5, 5, true, false, ""]}, {"id": 72, "type": "DetailerForEachDebugPipe", "pos": [5938, -58], "size": {"0": 330, "1": 618}, "flags": {}, "order": 44, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 153, "label": "image"}, {"name": "segs", "type": "SEGS", "link": 154, "label": "segs"}, {"name": "basic_pipe", "type": "BASIC_PIPE", "link": 155, "label": "basic_pipe"}, {"name": "detailer_hook", "type": "DETAILER_HOOK", "link": null, "label": "detailer_hook"}], "outputs": [{"name": "image", "type": "IMAGE", "links": [156], "shape": 3, "slot_index": 0, "label": "image"}, {"name": "cropped", "type": "IMAGE", "links": [157], "shape": 6, "slot_index": 1, "label": "cropped"}, {"name": "cropped_refined", "type": "IMAGE", "links": [158], "shape": 6, "slot_index": 2, "label": "cropped_refined"}, {"name": "cropped_refined_alpha", "type": "IMAGE", "links": [200], "shape": 6, "slot_index": 3, "label": "cropped_refined_alpha"}], "properties": {"Node name for S&R": "DetailerForEachDebugPipe"}, "widgets_values": [256, true, 768, 0, "fixed", 20, 8, "euler", "normal", 0.5, 5, true, false, ""]}, {"id": 66, "type": "PreviewImage", "pos": [6430, 30], "size": {"0": 330, "1": 260}, "flags": {}, "order": 49, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 158, "label": "images"}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 82, "type": "PreviewImage", "pos": [6435, 355], "size": {"0": 319.**********, "1": 285.4361572265625}, "flags": {}, "order": 50, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 200, "label": "images"}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 83, "type": "UltralyticsDetectorProvider", "pos": [860, 1160], "size": {"0": 315, "1": 78}, "flags": {}, "order": 2, "mode": 0, "outputs": [{"name": "BBOX_DETECTOR", "type": "BBOX_DETECTOR", "links": [201, 202], "shape": 3, "slot_index": 0, "label": "BBOX_DETECTOR"}, {"name": "SEGM_DETECTOR", "type": "SEGM_DETECTOR", "links": null, "shape": 3, "slot_index": 1, "label": "SEGM_DETECTOR"}], "properties": {"Node name for S&R": "UltralyticsDetectorProvider"}, "widgets_values": ["bbox/face_yolov8m.pt"]}, {"id": 69, "type": "Detailer<PERSON>or<PERSON>ach", "pos": [5610, 1425], "size": {"0": 315, "1": 678}, "flags": {}, "order": 43, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 137, "label": "image"}, {"name": "segs", "type": "SEGS", "link": 138, "label": "segs"}, {"name": "model", "type": "MODEL", "link": 139, "label": "model"}, {"name": "clip", "type": "CLIP", "link": 140, "label": "clip"}, {"name": "vae", "type": "VAE", "link": 141, "label": "vae"}, {"name": "positive", "type": "CONDITIONING", "link": 142, "label": "positive"}, {"name": "negative", "type": "CONDITIONING", "link": 143, "label": "negative"}, {"name": "detailer_hook", "type": "DETAILER_HOOK", "link": null, "label": "detailer_hook"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [144], "shape": 3, "slot_index": 0, "label": "IMAGE"}], "properties": {"Node name for S&R": "Detailer<PERSON>or<PERSON>ach"}, "widgets_values": [256, true, 768, 0, "fixed", 20, 8, "euler", "normal", 0.5, 5, true, false, ""]}, {"id": 50, "type": "FromDetailerPipe", "pos": [4730, 1460], "size": {"0": 342.5999755859375, "1": 186}, "flags": {}, "order": 32, "mode": 0, "inputs": [{"name": "detailer_pipe", "type": "DETAILER_PIPE", "link": 82, "label": "detailer_pipe"}], "outputs": [{"name": "model", "type": "MODEL", "links": [139, 161], "shape": 3, "slot_index": 0, "label": "model"}, {"name": "clip", "type": "CLIP", "links": [140, 162], "shape": 3, "slot_index": 1, "label": "clip"}, {"name": "vae", "type": "VAE", "links": [141, 163], "shape": 3, "slot_index": 2, "label": "vae"}, {"name": "positive", "type": "CONDITIONING", "links": [142, 164], "shape": 3, "slot_index": 3, "label": "positive"}, {"name": "negative", "type": "CONDITIONING", "links": [143, 165], "shape": 3, "slot_index": 4, "label": "negative"}, {"name": "bbox_detector", "type": "BBOX_DETECTOR", "links": [85], "shape": 3, "slot_index": 5, "label": "bbox_detector"}, {"name": "sam_model_opt", "type": "SAM_MODEL", "links": [83], "shape": 3, "slot_index": 6, "label": "sam_model_opt"}, {"name": "segm_detector_opt", "type": "SEGM_DETECTOR", "links": [204], "shape": 3, "slot_index": 7, "label": "segm_detector_opt"}, {"name": "detailer_hook", "type": "DETAILER_HOOK", "links": null, "shape": 3, "label": "detailer_hook"}], "properties": {"Node name for S&R": "FromDetailerPipe"}}, {"id": 51, "type": "SAMDetectorCombined", "pos": [5125, 894], "size": {"0": 315, "1": 218}, "flags": {}, "order": 35, "mode": 0, "inputs": [{"name": "sam_model", "type": "SAM_MODEL", "link": 83, "label": "sam_model"}, {"name": "segs", "type": "SEGS", "link": 87, "label": "segs"}, {"name": "image", "type": "IMAGE", "link": 205, "label": "image"}], "outputs": [{"name": "MASK", "type": "MASK", "links": [88], "shape": 3, "slot_index": 0, "label": "MASK"}], "properties": {"Node name for S&R": "SAMDetectorCombined"}, "widgets_values": ["center-1", 0, 0.93, 0, 0.7, "False"]}, {"id": 85, "type": "SEGSToImageList", "pos": [5569.134812187498, 1289.240372597656], "size": {"0": 304.79998779296875, "1": 46}, "flags": {}, "order": 37, "mode": 0, "inputs": [{"name": "segs", "type": "SEGS", "link": 207, "label": "segs"}, {"name": "fallback_image_opt", "type": "IMAGE", "link": 208, "label": "fallback_image_opt"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [209], "shape": 6, "slot_index": 0, "label": "IMAGE"}], "properties": {"Node name for S&R": "SEGSToImageList"}}, {"id": 86, "type": "PreviewImage", "pos": [6910, 1420], "size": {"0": 409.85064697265625, "1": 614.9011840820312}, "flags": {}, "order": 42, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 209, "label": "images"}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 39, "type": "ToDetailerPipe", "pos": [3167, 631], "size": {"0": 400, "1": 260}, "flags": {}, "order": 23, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 61, "label": "model"}, {"name": "clip", "type": "CLIP", "link": 62, "label": "clip"}, {"name": "vae", "type": "VAE", "link": 65, "label": "vae"}, {"name": "positive", "type": "CONDITIONING", "link": 66, "label": "positive"}, {"name": "negative", "type": "CONDITIONING", "link": 67, "label": "negative"}, {"name": "bbox_detector", "type": "BBOX_DETECTOR", "link": 68, "label": "bbox_detector"}, {"name": "sam_model_opt", "type": "SAM_MODEL", "link": 69, "label": "sam_model_opt"}, {"name": "segm_detector_opt", "type": "SEGM_DETECTOR", "link": 203, "label": "segm_detector_opt"}, {"name": "detailer_hook", "type": "DETAILER_HOOK", "link": null, "label": "detailer_hook"}], "outputs": [{"name": "detailer_pipe", "type": "DETAILER_PIPE", "links": [210], "shape": 3, "slot_index": 0, "label": "detailer_pipe"}], "properties": {"Node name for S&R": "ToDetailerPipe"}, "widgets_values": ["", "Select the LoRA to add to the text"]}, {"id": 76, "type": "FaceDetailerPipe", "pos": [3648, 641], "size": {"0": 347.608154296875, "1": 1060.470947265625}, "flags": {}, "order": 24, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 184, "label": "image"}, {"name": "detailer_pipe", "type": "DETAILER_PIPE", "link": 210, "label": "detailer_pipe"}], "outputs": [{"name": "image", "type": "IMAGE", "links": [186, 188], "shape": 3, "slot_index": 0, "label": "image"}, {"name": "cropped_refined", "type": "IMAGE", "links": [187], "shape": 6, "slot_index": 1, "label": "cropped_refined"}, {"name": "cropped_enhanced_alpha", "type": "IMAGE", "links": [189], "shape": 6, "slot_index": 2, "label": "cropped_enhanced_alpha"}, {"name": "mask", "type": "MASK", "links": [190], "shape": 3, "slot_index": 3, "label": "mask"}, {"name": "detailer_pipe", "type": "DETAILER_PIPE", "links": [191], "shape": 3, "slot_index": 4, "label": "detailer_pipe"}], "properties": {"Node name for S&R": "FaceDetailerPipe"}, "widgets_values": [256, true, 768, 284739423125169, "fixed", 20, 8, "euler", "normal", 0.5, 5, true, false, 0.5, 10, 3, "center-1", 0, 0.93, 0, 0.7, "False", 10]}, {"id": 49, "type": "Reroute", "pos": [4967, 568], "size": [75, 26], "flags": {}, "order": 17, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 211, "label": ""}], "outputs": [{"name": "", "type": "IMAGE", "links": [137, 153, 159, 194], "slot_index": 0, "label": ""}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 27, "type": "PreviewImage", "pos": [2590, 920], "size": {"0": 210, "1": 246}, "flags": {}, "order": 18, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 180, "label": "images"}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 74, "type": "FaceDetailer", "pos": [2050, 580], "size": {"0": 372.5969543457031, "1": 1103.0477294921875}, "flags": {}, "order": 14, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 169, "label": "image"}, {"name": "model", "type": "MODEL", "link": 170, "label": "model"}, {"name": "clip", "type": "CLIP", "link": 171, "label": "clip"}, {"name": "vae", "type": "VAE", "link": 172, "label": "vae"}, {"name": "positive", "type": "CONDITIONING", "link": 175, "label": "positive"}, {"name": "negative", "type": "CONDITIONING", "link": 176, "slot_index": 5, "label": "negative"}, {"name": "bbox_detector", "type": "BBOX_DETECTOR", "link": 177, "label": "bbox_detector"}, {"name": "sam_model_opt", "type": "SAM_MODEL", "link": 178, "label": "sam_model_opt"}, {"name": "segm_detector_opt", "type": "SEGM_DETECTOR", "link": 214, "label": "segm_detector_opt"}, {"name": "detailer_hook", "type": "DETAILER_HOOK", "link": null, "label": "detailer_hook"}], "outputs": [{"name": "image", "type": "IMAGE", "links": [179, 211], "shape": 3, "slot_index": 0, "label": "image"}, {"name": "cropped_refined", "type": "IMAGE", "links": [180], "shape": 6, "slot_index": 1, "label": "cropped_refined"}, {"name": "cropped_enhanced_alpha", "type": "IMAGE", "links": [181], "shape": 6, "slot_index": 2, "label": "cropped_enhanced_alpha"}, {"name": "mask", "type": "MASK", "links": [182], "shape": 3, "slot_index": 3, "label": "mask"}, {"name": "detailer_pipe", "type": "DETAILER_PIPE", "links": [193], "shape": 3, "slot_index": 4, "label": "detailer_pipe"}], "properties": {"Node name for S&R": "FaceDetailer"}, "widgets_values": [256, true, 768, 872368928997833, "fixed", 20, 8, "euler", "normal", 0.5, 5, true, false, 0.5, 10, 3, "center-1", 0, 0.93, 0, 0.7, "False", 10, ""]}, {"id": 38, "type": "FromDetailerPipe", "pos": [2740, 630], "size": {"0": 342.5999755859375, "1": 186}, "flags": {}, "order": 21, "mode": 0, "inputs": [{"name": "detailer_pipe", "type": "DETAILER_PIPE", "link": 193, "label": "detailer_pipe"}], "outputs": [{"name": "model", "type": "MODEL", "links": [61], "shape": 3, "slot_index": 0, "label": "model"}, {"name": "clip", "type": "CLIP", "links": [62], "shape": 3, "slot_index": 1, "label": "clip"}, {"name": "vae", "type": "VAE", "links": [65], "shape": 3, "slot_index": 2, "label": "vae"}, {"name": "positive", "type": "CONDITIONING", "links": [66], "shape": 3, "slot_index": 3, "label": "positive"}, {"name": "negative", "type": "CONDITIONING", "links": [67], "shape": 3, "slot_index": 4, "label": "negative"}, {"name": "bbox_detector", "type": "BBOX_DETECTOR", "links": [68], "shape": 3, "slot_index": 5, "label": "bbox_detector"}, {"name": "sam_model_opt", "type": "SAM_MODEL", "links": [69], "shape": 3, "slot_index": 6, "label": "sam_model_opt"}, {"name": "segm_detector_opt", "type": "SEGM_DETECTOR", "links": [203], "shape": 3, "slot_index": 7, "label": "segm_detector_opt"}, {"name": "detailer_hook", "type": "DETAILER_HOOK", "links": null, "shape": 3, "label": "detailer_hook"}], "properties": {"Node name for S&R": "FromDetailerPipe"}}, {"id": 87, "type": "UltralyticsDetectorProvider", "pos": [862, 1445], "size": {"0": 315, "1": 78}, "flags": {}, "order": 3, "mode": 0, "outputs": [{"name": "BBOX_DETECTOR", "type": "BBOX_DETECTOR", "links": [], "shape": 3, "slot_index": 0, "label": "BBOX_DETECTOR"}, {"name": "SEGM_DETECTOR", "type": "SEGM_DETECTOR", "links": [212, 213], "shape": 3, "slot_index": 1, "label": "SEGM_DETECTOR"}], "properties": {"Node name for S&R": "UltralyticsDetectorProvider"}, "widgets_values": ["segm/person_yolov8m-seg.pt"]}, {"id": 77, "type": "Reroute", "pos": [3500, 170], "size": [75, 26], "flags": {}, "order": 15, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 183, "label": ""}], "outputs": [{"name": "", "type": "IMAGE", "links": [184, 205, 206, 208], "slot_index": 0, "label": ""}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 84, "type": "SegmDetectorSEGS", "pos": [5130, 1240], "size": {"0": 315, "1": 150}, "flags": {}, "order": 34, "mode": 0, "inputs": [{"name": "segm_detector", "type": "SEGM_DETECTOR", "link": 204, "label": "segm_detector"}, {"name": "image", "type": "IMAGE", "link": 206, "label": "image"}], "outputs": [{"name": "SEGS", "type": "SEGS", "links": [207], "shape": 3, "slot_index": 0, "label": "SEGS"}], "properties": {"Node name for S&R": "SegmDetectorSEGS"}, "widgets_values": [0.5, 10, 3, 1]}, {"id": 34, "type": "FromDetailerPipe", "pos": [1737, -34], "size": {"0": 342.5999755859375, "1": 186}, "flags": {}, "order": 11, "mode": 0, "inputs": [{"name": "detailer_pipe", "type": "DETAILER_PIPE", "link": 36, "label": "detailer_pipe"}], "outputs": [{"name": "model", "type": "MODEL", "links": [170], "shape": 3, "slot_index": 0, "label": "model"}, {"name": "clip", "type": "CLIP", "links": [171], "shape": 3, "slot_index": 1, "label": "clip"}, {"name": "vae", "type": "VAE", "links": [172], "shape": 3, "slot_index": 2, "label": "vae"}, {"name": "positive", "type": "CONDITIONING", "links": [175], "shape": 3, "slot_index": 3, "label": "positive"}, {"name": "negative", "type": "CONDITIONING", "links": [176], "shape": 3, "slot_index": 4, "label": "negative"}, {"name": "bbox_detector", "type": "BBOX_DETECTOR", "links": [177], "shape": 3, "slot_index": 5, "label": "bbox_detector"}, {"name": "sam_model_opt", "type": "SAM_MODEL", "links": [178], "shape": 3, "slot_index": 6, "label": "sam_model_opt"}, {"name": "segm_detector_opt", "type": "SEGM_DETECTOR", "links": [214], "shape": 3, "slot_index": 7, "label": "segm_detector_opt"}, {"name": "detailer_hook", "type": "DETAILER_HOOK", "links": null, "shape": 3, "label": "detailer_hook"}], "properties": {"Node name for S&R": "FromDetailerPipe"}}, {"id": 73, "type": "DetailerForEachDebug", "pos": [5603, 2282], "size": {"0": 315, "1": 678}, "flags": {}, "order": 36, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 159, "label": "image"}, {"name": "segs", "type": "SEGS", "link": 160, "label": "segs"}, {"name": "model", "type": "MODEL", "link": 161, "label": "model"}, {"name": "clip", "type": "CLIP", "link": 162, "label": "clip"}, {"name": "vae", "type": "VAE", "link": 163, "label": "vae"}, {"name": "positive", "type": "CONDITIONING", "link": 164, "label": "positive"}, {"name": "negative", "type": "CONDITIONING", "link": 165, "label": "negative"}, {"name": "detailer_hook", "type": "DETAILER_HOOK", "link": null, "label": "detailer_hook"}], "outputs": [{"name": "image", "type": "IMAGE", "links": [166], "shape": 3, "slot_index": 0, "label": "image"}, {"name": "cropped", "type": "IMAGE", "links": [167], "shape": 6, "slot_index": 1, "label": "cropped"}, {"name": "cropped_refined", "type": "IMAGE", "links": [168], "shape": 6, "slot_index": 2, "label": "cropped_refined"}, {"name": "cropped_refined_alpha", "type": "IMAGE", "links": null, "shape": 6, "label": "cropped_refined_alpha"}], "properties": {"Node name for S&R": "DetailerForEachDebug"}, "widgets_values": [256, true, 768, 225176759887640, "fixed", 20, 8, "euler", "normal", 0.5, 5, true, false, ""]}, {"id": 61, "type": "PreviewImage", "pos": [6000, 2450], "size": {"0": 210, "1": 246}, "flags": {}, "order": 40, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 167, "label": "images"}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 62, "type": "PreviewImage", "pos": [5990, 2780], "size": {"0": 210, "1": 246}, "flags": {}, "order": 41, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 168, "label": "images"}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 4, "type": "CheckpointLoaderSimple", "pos": [26, 474], "size": {"0": 315, "1": 98}, "flags": {}, "order": 4, "mode": 0, "outputs": [{"name": "MODEL", "type": "MODEL", "links": [1], "slot_index": 0, "label": "MODEL"}, {"name": "CLIP", "type": "CLIP", "links": [3, 5], "slot_index": 1, "label": "CLIP"}, {"name": "VAE", "type": "VAE", "links": [8], "slot_index": 2, "label": "VAE"}], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["SD1.5/V07_v07.safetensors"]}, {"id": 19, "type": "## make-basic_pipe [2c8c61]", "pos": [502, 860], "size": {"0": 400, "1": 200}, "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "vae_opt", "type": "VAE", "link": null, "label": "vae_opt"}], "outputs": [{"name": "BASIC_PIPE", "type": "BASIC_PIPE", "links": [17, 34], "shape": 3, "slot_index": 0, "label": "BASIC_PIPE"}], "title": "## make-basic_pipe", "properties": {"Node name for S&R": "## make-basic_pipe [2c8c61]"}, "widgets_values": ["SD1.5/V07_v07.safetensors", "", "text, watermark, worst quality:1.4, low quality:1.4"]}], "links": [[1, 4, 0, 3, 0, "MODEL"], [2, 5, 0, 3, 3, "LATENT"], [3, 4, 1, 6, 0, "CLIP"], [4, 6, 0, 3, 1, "CONDITIONING"], [5, 4, 1, 7, 0, "CLIP"], [6, 7, 0, 3, 2, "CONDITIONING"], [7, 3, 0, 8, 0, "LATENT"], [8, 4, 2, 8, 1, "VAE"], [10, 8, 0, 10, 0, "IMAGE"], [17, 19, 0, 22, 0, "BASIC_PIPE"], [19, 24, 0, 22, 2, "SAM_MODEL"], [33, 24, 0, 32, 2, "SAM_MODEL"], [34, 19, 0, 32, 0, "BASIC_PIPE"], [36, 32, 0, 34, 0, "DETAILER_PIPE"], [59, 36, 0, 37, 0, "IMAGE"], [61, 38, 0, 39, 0, "MODEL"], [62, 38, 1, 39, 1, "CLIP"], [65, 38, 2, 39, 2, "VAE"], [66, 38, 3, 39, 3, "CONDITIONING"], [67, 38, 4, 39, 4, "CONDITIONING"], [68, 38, 5, 39, 5, "BBOX_DETECTOR"], [69, 38, 6, 39, 6, "SAM_MODEL"], [75, 43, 0, 44, 0, "IMAGE"], [77, 45, 0, 46, 0, "DETAILER_PIPE"], [82, 45, 0, 50, 0, "DETAILER_PIPE"], [83, 50, 6, 51, 0, "SAM_MODEL"], [85, 50, 5, 52, 0, "BBOX_DETECTOR"], [87, 52, 0, 51, 1, "SEGS"], [88, 51, 0, 53, 0, "MASK"], [137, 49, 0, 69, 0, "IMAGE"], [138, 53, 0, 69, 1, "SEGS"], [139, 50, 0, 69, 2, "MODEL"], [140, 50, 1, 69, 3, "CLIP"], [141, 50, 2, 69, 4, "VAE"], [142, 50, 3, 69, 5, "CONDITIONING"], [143, 50, 4, 69, 6, "CONDITIONING"], [144, 69, 0, 57, 0, "IMAGE"], [153, 49, 0, 72, 0, "IMAGE"], [154, 53, 0, 72, 1, "SEGS"], [155, 46, 0, 72, 2, "BASIC_PIPE"], [156, 72, 0, 64, 0, "IMAGE"], [157, 72, 1, 65, 0, "IMAGE"], [158, 72, 2, 66, 0, "IMAGE"], [159, 49, 0, 73, 0, "IMAGE"], [160, 52, 0, 73, 1, "SEGS"], [161, 50, 0, 73, 2, "MODEL"], [162, 50, 1, 73, 3, "CLIP"], [163, 50, 2, 73, 4, "VAE"], [164, 50, 3, 73, 5, "CONDITIONING"], [165, 50, 4, 73, 6, "CONDITIONING"], [166, 73, 0, 60, 0, "IMAGE"], [167, 73, 1, 61, 0, "IMAGE"], [168, 73, 2, 62, 0, "IMAGE"], [169, 10, 0, 74, 0, "IMAGE"], [170, 34, 0, 74, 1, "MODEL"], [171, 34, 1, 74, 2, "CLIP"], [172, 34, 2, 74, 3, "VAE"], [175, 34, 3, 74, 4, "CONDITIONING"], [176, 34, 4, 74, 5, "CONDITIONING"], [177, 34, 5, 74, 6, "BBOX_DETECTOR"], [178, 34, 6, 74, 7, "SAM_MODEL"], [179, 74, 0, 30, 0, "IMAGE"], [180, 74, 1, 27, 0, "IMAGE"], [181, 74, 2, 75, 0, "IMAGE"], [182, 74, 3, 36, 0, "MASK"], [183, 10, 0, 77, 0, "*"], [184, 77, 0, 76, 0, "IMAGE"], [186, 76, 0, 41, 0, "IMAGE"], [187, 76, 1, 42, 0, "IMAGE"], [188, 76, 0, 52, 1, "IMAGE"], [189, 76, 2, 78, 0, "IMAGE"], [190, 76, 3, 43, 0, "MASK"], [191, 76, 4, 45, 0, "DETAILER_PIPE"], [193, 74, 4, 38, 0, "DETAILER_PIPE"], [194, 49, 0, 81, 0, "IMAGE"], [195, 53, 0, 81, 1, "SEGS"], [196, 46, 0, 81, 2, "BASIC_PIPE"], [197, 81, 0, 54, 0, "IMAGE"], [200, 72, 3, 82, 0, "IMAGE"], [201, 83, 0, 22, 1, "BBOX_DETECTOR"], [202, 83, 0, 32, 1, "BBOX_DETECTOR"], [203, 38, 7, 39, 7, "SEGM_DETECTOR"], [204, 50, 7, 84, 0, "SEGM_DETECTOR"], [205, 77, 0, 51, 2, "IMAGE"], [206, 77, 0, 84, 1, "IMAGE"], [207, 84, 0, 85, 0, "SEGS"], [208, 77, 0, 85, 1, "IMAGE"], [209, 85, 0, 86, 0, "IMAGE"], [210, 39, 0, 76, 1, "DETAILER_PIPE"], [211, 74, 0, 49, 0, "*"], [212, 87, 1, 22, 3, "SEGM_DETECTOR"], [213, 87, 1, 32, 3, "SEGM_DETECTOR"], [214, 34, 7, 74, 8, "SEGM_DETECTOR"]], "groups": [], "config": {}, "extra": {}, "version": 0.4}