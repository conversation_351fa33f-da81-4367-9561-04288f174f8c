{"last_node_id": 87, "last_link_id": 21, "nodes": [{"id": 5, "type": "ShowText|pysssss", "pos": [-12595.3798828125, -4455.8984375], "size": [315, 76], "flags": {}, "order": 47, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 2, "widget": {"name": "text"}}], "outputs": [{"name": "STRING", "type": "STRING", "links": null, "shape": 6}], "properties": {"Node name for S&R": "ShowText|pysssss"}, "widgets_values": ["", "0 1 3 2"]}, {"id": 6, "type": "ImpactWildcardProcessor", "pos": [-13061.4169921875, -5009.56689453125], "size": [400, 222], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [3], "slot_index": 0}], "properties": {"Node name for S&R": "ImpactWildcardProcessor"}, "widgets_values": ["{1-3$$0|1|2|3}", "0 2", "populate", 501646603955617, "randomize", "Select the Wildcard to add to the text"]}, {"id": 9, "type": "ShowText|pysssss", "pos": [-12614.6201171875, -5330.05126953125], "size": [315, 76], "flags": {}, "order": 37, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 4, "widget": {"name": "text"}}], "outputs": [{"name": "STRING", "type": "STRING", "links": null, "shape": 6}], "properties": {"Node name for S&R": "ShowText|pysssss"}, "widgets_values": ["", "3"]}, {"id": 11, "type": "ShowText|pysssss", "pos": [-12604.265625, -4732.607421875], "size": [315, 76], "flags": {}, "order": 46, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 5, "widget": {"name": "text"}}], "outputs": [{"name": "STRING", "type": "STRING", "links": null, "shape": 6}], "properties": {"Node name for S&R": "ShowText|pysssss"}, "widgets_values": ["", "1 3 2"]}, {"id": 7, "type": "ShowText|pysssss", "pos": [-12602.5078125, -5013.27099609375], "size": [315, 76], "flags": {}, "order": 36, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 3, "widget": {"name": "text"}}], "outputs": [{"name": "STRING", "type": "STRING", "links": null, "shape": 6}], "properties": {"Node name for S&R": "ShowText|pysssss"}, "widgets_values": ["", "0 2"]}, {"id": 8, "type": "ImpactWildcardProcessor", "pos": [-13065.423828125, -5326.34716796875], "size": [400, 222], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [4], "slot_index": 0}], "properties": {"Node name for S&R": "ImpactWildcardProcessor"}, "widgets_values": ["{-3$$0|1|2|3}", "3", "populate", 248114349904436, "randomize", "Select the Wildcard to add to the text"]}, {"id": 23, "type": "ShowText|pysssss", "pos": [-11348.609375, -4470.376953125], "size": [315, 76], "flags": {}, "order": 41, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 6, "widget": {"name": "text"}}], "outputs": [{"name": "STRING", "type": "STRING", "links": null, "shape": 6}], "properties": {"Node name for S&R": "ShowText|pysssss"}, "widgets_values": ["", "2"]}, {"id": 25, "type": "ShowText|pysssss", "pos": [-11367.849609375, -5344.52978515625], "size": [315, 76], "flags": {}, "order": 38, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 7, "widget": {"name": "text"}}], "outputs": [{"name": "STRING", "type": "STRING", "links": null, "shape": 6}], "properties": {"Node name for S&R": "ShowText|pysssss"}, "widgets_values": ["", "2, 0"]}, {"id": 26, "type": "ShowText|pysssss", "pos": [-11357.4951171875, -4747.0859375], "size": [315, 76], "flags": {}, "order": 40, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 8, "widget": {"name": "text"}}], "outputs": [{"name": "STRING", "type": "STRING", "links": null, "shape": 6}], "properties": {"Node name for S&R": "ShowText|pysssss"}, "widgets_values": ["", "3, 0, 1, 2"]}, {"id": 27, "type": "ShowText|pysssss", "pos": [-11355.7373046875, -5027.74951171875], "size": [315, 76], "flags": {}, "order": 39, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 9, "widget": {"name": "text"}}], "outputs": [{"name": "STRING", "type": "STRING", "links": null, "shape": 6}], "properties": {"Node name for S&R": "ShowText|pysssss"}, "widgets_values": ["", "0, 1, 2"]}, {"id": 29, "type": "ImpactWildcardProcessor", "pos": [-11818.6533203125, -5340.82568359375], "size": [400, 222], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [7], "slot_index": 0}], "properties": {"Node name for S&R": "ImpactWildcardProcessor"}, "widgets_values": ["{-3$$, $$0|1|2|3}", "2, 0", "populate", ***************, "randomize", "Select the Wildcard to add to the text"]}, {"id": 24, "type": "ImpactWildcardProcessor", "pos": [-11814.646484375, -5024.04541015625], "size": [400, 222], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [9], "slot_index": 0}], "properties": {"Node name for S&R": "ImpactWildcardProcessor"}, "widgets_values": ["{1-3$$, $$0|1|2|3}", "0, 1, 2", "populate", 937484056347845, "randomize", "Select the Wildcard to add to the text"]}, {"id": 28, "type": "ImpactWildcardProcessor", "pos": [-11819.107421875, -4747.43505859375], "size": [400, 222], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [8], "slot_index": 0}], "properties": {"Node name for S&R": "ImpactWildcardProcessor"}, "widgets_values": ["{1-9999999$$, $$0|1|2|3}", "3, 0, 1, 2", "populate", ***************, "randomize", "Select the Wildcard to add to the text"]}, {"id": 22, "type": "ImpactWildcardProcessor", "pos": [-11818.48046875, -4471.24951171875], "size": [400, 222], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [6], "slot_index": 0}], "properties": {"Node name for S&R": "ImpactWildcardProcessor"}, "widgets_values": ["{1-$$, $$0|1|2|3}", "2", "populate", 125928663019713, "randomize", "Select the Wildcard to add to the text"]}, {"id": 48, "type": "ShowText|pysssss", "pos": [-12539.638671875, -2811.31591796875], "size": [315, 76], "flags": {}, "order": 45, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 10, "widget": {"name": "text"}}], "outputs": [{"name": "STRING", "type": "STRING", "links": null, "shape": 6}], "properties": {"Node name for S&R": "ShowText|pysssss"}, "widgets_values": ["", "0 2, 0, 1 0, 1, 2 __test_wildcard/empty_item/*__ __test_wildcard/empty_item/*__"]}, {"id": 50, "type": "ShowText|pysssss", "pos": [-12558.87890625, -3685.46875], "size": [315, 76], "flags": {}, "order": 42, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 11, "widget": {"name": "text"}}], "outputs": [{"name": "STRING", "type": "STRING", "links": null, "shape": 6}], "properties": {"Node name for S&R": "ShowText|pysssss"}, "widgets_values": ["", "0 __test_wildcard/empty_item/*__ 0, 1, 2"]}, {"id": 51, "type": "ShowText|pysssss", "pos": [-12548.5244140625, -3088.02490234375], "size": [315, 76], "flags": {}, "order": 44, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 12, "widget": {"name": "text"}}], "outputs": [{"name": "STRING", "type": "STRING", "links": null, "shape": 6}], "properties": {"Node name for S&R": "ShowText|pysssss"}, "widgets_values": ["", "0 0 __test_wildcard/empty_item/*__ 1 0 0 1 0 __test_wildcard/empty_item/*__"]}, {"id": 52, "type": "ShowText|pysssss", "pos": [-12546.7666015625, -3368.6884765625], "size": [315, 76], "flags": {}, "order": 43, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 13, "widget": {"name": "text"}}], "outputs": [{"name": "STRING", "type": "STRING", "links": null, "shape": 6}], "properties": {"Node name for S&R": "ShowText|pysssss"}, "widgets_values": ["", "__test_wildcard/empty_item/*__ __test_wildcard/empty_item/*__ 0"]}, {"id": 55, "type": "ShowText|pysssss", "pos": [-11292.8681640625, -2825.79443359375], "size": [315, 76], "flags": {}, "order": 48, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 14, "widget": {"name": "text"}}], "outputs": [{"name": "STRING", "type": "STRING", "links": null, "shape": 6}], "properties": {"Node name for S&R": "ShowText|pysssss"}, "widgets_values": ["", "1 2 0separator0separator0separator0, 1, 2"]}, {"id": 56, "type": "ShowText|pysssss", "pos": [-11312.1083984375, -3699.947265625], "size": [315, 76], "flags": {}, "order": 51, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 15, "widget": {"name": "text"}}], "outputs": [{"name": "STRING", "type": "STRING", "links": null, "shape": 6}], "properties": {"Node name for S&R": "ShowText|pysssss"}, "widgets_values": ["", "0, 1, 2separator__test_wildcard/empty_item/*__separator0"]}, {"id": 57, "type": "ShowText|pysssss", "pos": [-11301.75390625, -3102.50341796875], "size": [315, 76], "flags": {}, "order": 49, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 16, "widget": {"name": "text"}}], "outputs": [{"name": "STRING", "type": "STRING", "links": null, "shape": 6}], "properties": {"Node name for S&R": "ShowText|pysssss"}, "widgets_values": ["", "2, 1separator0, 1, 2separator__test_wildcard/empty_item/*__separator__test_wildcard/empty_item/*__separator0 1 2separator0, 1separator0separator0"]}, {"id": 58, "type": "ShowText|pysssss", "pos": [-11299.99609375, -3383.1669921875], "size": [315, 76], "flags": {}, "order": 50, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 17, "widget": {"name": "text"}}], "outputs": [{"name": "STRING", "type": "STRING", "links": null, "shape": 6}], "properties": {"Node name for S&R": "ShowText|pysssss"}, "widgets_values": ["", "__test_wildcard/empty_item/*__separator0"]}, {"id": 60, "type": "<PERSON>downNote", "pos": [-11900.6484375, -3872.76025390625], "size": [536.965087890625, 103.656005859375], "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["# separator: range$$sep$$"], "color": "#432", "bgcolor": "#653"}, {"id": 59, "type": "<PERSON>downNote", "pos": [-13181.8271484375, -3886.89794921875], "size": [536.965087890625, 103.656005859375], "flags": {}, "order": 7, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["# separator: range$$"], "color": "#432", "bgcolor": "#653"}, {"id": 35, "type": "<PERSON>downNote", "pos": [-13237.568359375, -5531.48046875], "size": [536.965087890625, 103.656005859375], "flags": {}, "order": 8, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["# separator: range$$"], "color": "#432", "bgcolor": "#653"}, {"id": 36, "type": "<PERSON>downNote", "pos": [-11956.3896484375, -5517.3427734375], "size": [536.965087890625, 103.656005859375], "flags": {}, "order": 9, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["# separator: range$$sep$$"], "color": "#432", "bgcolor": "#653"}, {"id": 54, "type": "ImpactWildcardProcessor", "pos": [-13009.6826171875, -3681.7646484375], "size": [400, 222], "flags": {}, "order": 10, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [11], "slot_index": 0}], "properties": {"Node name for S&R": "ImpactWildcardProcessor"}, "widgets_values": ["{-3$$__test_wildcard/single_text_only/*__|__test_wildcard/empty_item/*__|__test_wildcard/text_only/*__|__test_wildcard/raw_wildcard/*__|__test_wildcard/raw_wildcard/*__|__test_wildcard/reference_wildcard/*__|__test_wildcard/reference_single_text_only_wildcard/*__|__test_wildcard/reference_empty_item/*__}", "0 __test_wildcard/empty_item/*__ 0, 1, 2", "populate", 198720804736378, "randomize", "Select the Wildcard to add to the text"]}, {"id": 49, "type": "ImpactWildcardProcessor", "pos": [-13005.67578125, -3364.984375], "size": [400, 222], "flags": {}, "order": 11, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [13], "slot_index": 0}], "properties": {"Node name for S&R": "ImpactWildcardProcessor"}, "widgets_values": ["{1-3$$__test_wildcard/single_text_only/*__|__test_wildcard/empty_item/*__|__test_wildcard/text_only/*__|__test_wildcard/raw_wildcard/*__|__test_wildcard/raw_wildcard/*__|__test_wildcard/reference_wildcard/*__|__test_wildcard/reference_single_text_only_wildcard/*__|__test_wildcard/reference_empty_item/*__}", "__test_wildcard/empty_item/*__ __test_wildcard/empty_item/*__ 0", "populate", 608971834515154, "randomize", "Select the Wildcard to add to the text"]}, {"id": 53, "type": "ImpactWildcardProcessor", "pos": [-13010.13671875, -3088.3740234375], "size": [400, 222], "flags": {}, "order": 12, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [12], "slot_index": 0}], "properties": {"Node name for S&R": "ImpactWildcardProcessor"}, "widgets_values": ["{1-9999999$$__test_wildcard/single_text_only/*__|__test_wildcard/empty_item/*__|__test_wildcard/text_only/*__|__test_wildcard/raw_wildcard/*__|__test_wildcard/raw_wildcard/*__|__test_wildcard/reference_wildcard/*__|__test_wildcard/reference_single_text_only_wildcard/*__|__test_wildcard/reference_empty_item/*__}", "0 0 __test_wildcard/empty_item/*__ 1 0 0 1 0 __test_wildcard/empty_item/*__", "populate", 849105155936244, "randomize", "Select the Wildcard to add to the text"]}, {"id": 47, "type": "ImpactWildcardProcessor", "pos": [-13009.509765625, -2812.1884765625], "size": [400, 222], "flags": {}, "order": 13, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [10], "slot_index": 0}], "properties": {"Node name for S&R": "ImpactWildcardProcessor"}, "widgets_values": ["{1-$$__test_wildcard/single_text_only/*__|__test_wildcard/empty_item/*__|__test_wildcard/text_only/*__|__test_wildcard/raw_wildcard/*__|__test_wildcard/raw_wildcard/*__|__test_wildcard/reference_wildcard/*__|__test_wildcard/reference_single_text_only_wildcard/*__|__test_wildcard/reference_empty_item/*__}", "0 2, 0, 1 0, 1, 2 __test_wildcard/empty_item/*__ __test_wildcard/empty_item/*__", "populate", 1084307274687290, "randomize", "Select the Wildcard to add to the text"]}, {"id": 43, "type": "<PERSON>downNote", "pos": [-13416.291015625, -5316.21484375], "size": [284.1663513183594, 190.96800231933594], "flags": {}, "order": 14, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["# range: -n$$\nexpected  \nseparator = \" \"  \nprobability: len(result)  \n 1: 33%, 2: 33%, 3: 33%\n"], "color": "#432", "bgcolor": "#653"}, {"id": 37, "type": "<PERSON>downNote", "pos": [-12123.849609375, -5326.48681640625], "size": [284.1663513183594, 190.96800231933594], "flags": {}, "order": 15, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["# range: -n$$\nexpected  \nseparator = sep  \nprobability: len(result)  \n 1: 33%, 2: 33%, 3: 33%\n"], "color": "#432", "bgcolor": "#653"}, {"id": 44, "type": "<PERSON>downNote", "pos": [-13406.4375, -4992.236328125], "size": [284.1663513183594, 190.96800231933594], "flags": {}, "order": 16, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["# range: n1-n2$$\nexpected  \nseparator = \" \"  \nprobability: len(result)  \n 1: 33%, 2: 33%, 3: 33%\n"], "color": "#432", "bgcolor": "#653"}, {"id": 68, "type": "<PERSON>downNote", "pos": [-13360.5498046875, -3671.63232421875], "size": [284.1663513183594, 190.96800231933594], "flags": {}, "order": 17, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["# range: -n$$\nexpected  \nseparator = \" \"  \nprobability: len(result)  \n 1: 33%, 2: 33%, 3: 33%\n"], "color": "#432", "bgcolor": "#653"}, {"id": 69, "type": "<PERSON>downNote", "pos": [-12068.1083984375, -3681.904296875], "size": [284.1663513183594, 190.96800231933594], "flags": {}, "order": 18, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["# range: -n$$\nexpected  \nseparator = sep  \nprobability: len(result)  \n 1: 33%, 2: 33%, 3: 33%\n"], "color": "#432", "bgcolor": "#653"}, {"id": 66, "type": "<PERSON>downNote", "pos": [-13350.6962890625, -3347.65380859375], "size": [284.1663513183594, 190.96800231933594], "flags": {}, "order": 19, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["# range: n1-n2$$\nexpected  \nseparator = \" \"  \nprobability: len(result)  \n 1: 33%, 2: 33%, 3: 33%\n"], "color": "#432", "bgcolor": "#653"}, {"id": 38, "type": "<PERSON>downNote", "pos": [-12113.99609375, -5002.50830078125], "size": [284.1663513183594, 190.96800231933594], "flags": {}, "order": 20, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["# range: n1-n2$$\nexpected  \nseparator = sep  \nprobability: len(result)  \n 1: 33%, 2: 33%, 3: 33%\n"], "color": "#432", "bgcolor": "#653"}, {"id": 42, "type": "<PERSON>downNote", "pos": [-13408.37109375, -4732.05224609375], "size": [284.8597717285156, 218.01190185546875], "flags": {}, "order": 21, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["# range:  n1-(itemcount <<<< n2)$$\n## expected  \nseparator = \" \"  \nprobability: len(result)  \n 1: 25%, 2: 25%, 3: 25%. 4: 25%  \n\n"], "color": "#432", "bgcolor": "#653"}, {"id": 65, "type": "<PERSON>downNote", "pos": [-13352.6298828125, -3087.4697265625], "size": [284.8597717285156, 218.01190185546875], "flags": {}, "order": 22, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["# range:  n1-(itemcount <<<< n2)$$\n## expected  \nseparator = \" \"  \nprobability: len(result)  \n 1: 25%, 2: 25%, 3: 25%. 4: 25%  \n\n"], "color": "#432", "bgcolor": "#653"}, {"id": 67, "type": "<PERSON>downNote", "pos": [-13354.5634765625, -2812.72412109375], "size": [281.3926086425781, 206.91697692871094], "flags": {}, "order": 23, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["# range:  n1-$$\n## expected  \nseparator = \" \"  \nprobability: len(result)  \n 1: 25%, 2: 25%, 3: 25%. 4: 25%  \n\n"], "color": "#432", "bgcolor": "#653"}, {"id": 72, "type": "<PERSON>downNote", "pos": [-12062.1220703125, -2822.9970703125], "size": [281.3926086425781, 206.91697692871094], "flags": {}, "order": 24, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["# range:  n1-$$\n## expected  \nseparator = sep  \nprobability: len(result)  \n 1: 25%, 2: 25%, 3: 25%. 4: 25%  \n\n"], "color": "#432", "bgcolor": "#653"}, {"id": 71, "type": "<PERSON>downNote", "pos": [-12060.1884765625, -3097.74169921875], "size": [284.8597717285156, 218.01190185546875], "flags": {}, "order": 25, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["# range:  n1-(itemcount <<<< n2)$$\n## expected  \nseparator = sep  \nprobability: len(result)  \n 1: 25%, 2: 25%, 3: 25%. 4: 25%  \n\n"], "color": "#432", "bgcolor": "#653"}, {"id": 70, "type": "<PERSON>downNote", "pos": [-12058.2548828125, -3357.92578125], "size": [284.1663513183594, 190.96800231933594], "flags": {}, "order": 26, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["# range: n1-n2$$\nexpected  \nseparator = sep  \nprobability: len(result)  \n 1: 33%, 2: 33%, 3: 33%\n"], "color": "#432", "bgcolor": "#653"}, {"id": 41, "type": "<PERSON>downNote", "pos": [-12117.86328125, -4467.57958984375], "size": [281.3926086425781, 206.91697692871094], "flags": {}, "order": 27, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["# range:  n1-$$\n## expected  \nseparator = sep  \nprobability: len(result)  \n 1: 25%, 2: 25%, 3: 25%. 4: 25%  \n\n"], "color": "#432", "bgcolor": "#653"}, {"id": 40, "type": "<PERSON>downNote", "pos": [-12115.9296875, -4742.32421875], "size": [284.8597717285156, 218.01190185546875], "flags": {}, "order": 28, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["# range:  n1-(itemcount <<<< n2)$$\n## expected  \nseparator = sep  \nprobability: len(result)  \n 1: 25%, 2: 25%, 3: 25%. 4: 25%  \n\n"], "color": "#432", "bgcolor": "#653"}, {"id": 10, "type": "ImpactWildcardProcessor", "pos": [-13065.8779296875, -4732.95654296875], "size": [400, 222], "flags": {}, "order": 29, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [5], "slot_index": 0}], "properties": {"Node name for S&R": "ImpactWildcardProcessor"}, "widgets_values": ["{1-9999999$$0|1|2|3}", "1 3 2", "populate", 924132932593852, "randomize", "Select the Wildcard to add to the text"]}, {"id": 45, "type": "<PERSON>downNote", "pos": [-13410.3046875, -4457.306640625], "size": [281.3926086425781, 206.91697692871094], "flags": {}, "order": 30, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["# range:  n1-$$\n## expected  \nseparator = \" \"  \nprobability: len(result)  \n 1: 25%, 2: 25%, 3: 25%. 4: 25%  \n\n"], "color": "#432", "bgcolor": "#653"}, {"id": 4, "type": "ImpactWildcardProcessor", "pos": [-13065.2509765625, -4456.77099609375], "size": [400, 222], "flags": {}, "order": 31, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [2], "slot_index": 0}], "properties": {"Node name for S&R": "ImpactWildcardProcessor"}, "widgets_values": ["{1-$$0|1|2|3}", "0 1 3 2", "populate", 164209619769734, "randomize", "Select the Wildcard to add to the text"]}, {"id": 64, "type": "ImpactWildcardProcessor", "pos": [-11762.7392578125, -2826.6669921875], "size": [400, 222], "flags": {}, "order": 32, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [14], "slot_index": 0}], "properties": {"Node name for S&R": "ImpactWildcardProcessor"}, "widgets_values": ["{1-$$separator$$__test_wildcard/single_text_only/*__|__test_wildcard/empty_item/*__|__test_wildcard/text_only/*__|__test_wildcard/raw_wildcard/*__|__test_wildcard/raw_wildcard/*__|__test_wildcard/reference_wildcard/*__|__test_wildcard/reference_single_text_only_wildcard/*__|__test_wildcard/reference_empty_item/*__}", "1 2 0separator0separator0separator0, 1, 2", "populate", 1004675672912818, "randomize", "Select the Wildcard to add to the text"]}, {"id": 63, "type": "ImpactWildcardProcessor", "pos": [-11763.3662109375, -3102.8525390625], "size": [400, 222], "flags": {}, "order": 33, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [16], "slot_index": 0}], "properties": {"Node name for S&R": "ImpactWildcardProcessor"}, "widgets_values": ["{1-9999999$$separator$$__test_wildcard/single_text_only/*__|__test_wildcard/empty_item/*__|__test_wildcard/text_only/*__|__test_wildcard/raw_wildcard/*__|__test_wildcard/raw_wildcard/*__|__test_wildcard/reference_wildcard/*__|__test_wildcard/reference_single_text_only_wildcard/*__|__test_wildcard/reference_empty_item/*__}", "2, 1separator0, 1, 2separator__test_wildcard/empty_item/*__separator__test_wildcard/empty_item/*__separator0 1 2separator0, 1separator0separator0", "populate", 991625019002598, "randomize", "Select the Wildcard to add to the text"]}, {"id": 62, "type": "ImpactWildcardProcessor", "pos": [-11758.9052734375, -3379.462890625], "size": [400, 222], "flags": {}, "order": 34, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [17], "slot_index": 0}], "properties": {"Node name for S&R": "ImpactWildcardProcessor"}, "widgets_values": ["{1-3$$separator$$__test_wildcard/single_text_only/*__|__test_wildcard/empty_item/*__|__test_wildcard/text_only/*__|__test_wildcard/raw_wildcard/*__|__test_wildcard/raw_wildcard/*__|__test_wildcard/reference_wildcard/*__|__test_wildcard/reference_single_text_only_wildcard/*__|__test_wildcard/reference_empty_item/*__}", "__test_wildcard/empty_item/*__separator0", "populate", 315424983318226, "randomize", "Select the Wildcard to add to the text"]}, {"id": 61, "type": "ImpactWildcardProcessor", "pos": [-11762.912109375, -3696.2431640625], "size": [400, 222], "flags": {}, "order": 35, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [15], "slot_index": 0}], "properties": {"Node name for S&R": "ImpactWildcardProcessor"}, "widgets_values": ["{-3$$separator$$__test_wildcard/single_text_only/*__|__test_wildcard/empty_item/*__|__test_wildcard/text_only/*__|__test_wildcard/raw_wildcard/*__|__test_wildcard/raw_wildcard/*__|__test_wildcard/reference_wildcard/*__|__test_wildcard/reference_single_text_only_wildcard/*__|__test_wildcard/reference_empty_item/*__}", "0, 1, 2separator__test_wildcard/empty_item/*__separator0", "populate", 980974218142586, "randomize", "Select the Wildcard to add to the text"]}], "links": [[2, 4, 0, 5, 0, "STRING"], [3, 6, 0, 7, 0, "STRING"], [4, 8, 0, 9, 0, "STRING"], [5, 10, 0, 11, 0, "STRING"], [6, 22, 0, 23, 0, "STRING"], [7, 29, 0, 25, 0, "STRING"], [8, 28, 0, 26, 0, "STRING"], [9, 24, 0, 27, 0, "STRING"], [10, 47, 0, 48, 0, "STRING"], [11, 54, 0, 50, 0, "STRING"], [12, 53, 0, 51, 0, "STRING"], [13, 49, 0, 52, 0, "STRING"], [14, 64, 0, 55, 0, "STRING"], [15, 61, 0, 56, 0, "STRING"], [16, 63, 0, 57, 0, "STRING"], [17, 62, 0, 58, 0, "STRING"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 0.8991465919831495, "offset": [14117.986929566414, 3548.5409155177076]}, "node_versions": {"ComfyUI-Custom-Scripts": "bc8922deff73f59311c05cef27b9d4caaf43e87b", "ComfyUI-Impact-Pack": "ebcb6f91abf4c8de1ab3636260959177615566ad"}}, "version": 0.4}