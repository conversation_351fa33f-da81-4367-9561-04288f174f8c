{"last_node_id": 161, "last_link_id": 43, "nodes": [{"id": 9, "type": "ShowText|pysssss", "pos": [-12730.7314453125, -6579.96533203125], "size": [315, 76], "flags": {}, "order": 85, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 4, "widget": {"name": "text"}}], "outputs": [{"name": "STRING", "type": "STRING", "links": null, "shape": 6}], "properties": {"Node name for S&R": "ShowText|pysssss"}, "widgets_values": ["", "0"]}, {"id": 25, "type": "ShowText|pysssss", "pos": [-11483.9609375, -6594.44384765625], "size": [315, 76], "flags": {}, "order": 86, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 7, "widget": {"name": "text"}}], "outputs": [{"name": "STRING", "type": "STRING", "links": null, "shape": 6}], "properties": {"Node name for S&R": "ShowText|pysssss"}, "widgets_values": ["", "0"]}, {"id": 48, "type": "ShowText|pysssss", "pos": [-12539.638671875, -2811.31591796875], "size": [315, 76], "flags": {}, "order": 71, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 10, "widget": {"name": "text"}}], "outputs": [{"name": "STRING", "type": "STRING", "links": null, "shape": 6}], "properties": {"Node name for S&R": "ShowText|pysssss"}, "widgets_values": ["", "0 0"]}, {"id": 50, "type": "ShowText|pysssss", "pos": [-12558.87890625, -3685.46875], "size": [315, 76], "flags": {}, "order": 74, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 11, "widget": {"name": "text"}}], "outputs": [{"name": "STRING", "type": "STRING", "links": null, "shape": 6}], "properties": {"Node name for S&R": "ShowText|pysssss"}, "widgets_values": ["", "0 __test_wildcard/empty_item/*__ 0"]}, {"id": 51, "type": "ShowText|pysssss", "pos": [-12548.5244140625, -3088.02490234375], "size": [315, 76], "flags": {}, "order": 72, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 12, "widget": {"name": "text"}}], "outputs": [{"name": "STRING", "type": "STRING", "links": null, "shape": 6}], "properties": {"Node name for S&R": "ShowText|pysssss"}, "widgets_values": ["", "0 0"]}, {"id": 52, "type": "ShowText|pysssss", "pos": [-12546.7666015625, -3368.6884765625], "size": [315, 76], "flags": {}, "order": 73, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 13, "widget": {"name": "text"}}], "outputs": [{"name": "STRING", "type": "STRING", "links": null, "shape": 6}], "properties": {"Node name for S&R": "ShowText|pysssss"}, "widgets_values": ["", "0 __test_wildcard/empty_item/*__"]}, {"id": 56, "type": "ShowText|pysssss", "pos": [-11312.1083984375, -3699.947265625], "size": [315, 76], "flags": {}, "order": 75, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 15, "widget": {"name": "text"}}], "outputs": [{"name": "STRING", "type": "STRING", "links": null, "shape": 6}], "properties": {"Node name for S&R": "ShowText|pysssss"}, "widgets_values": ["", "0, 1, 2separator0"]}, {"id": 57, "type": "ShowText|pysssss", "pos": [-11301.75390625, -3102.50341796875], "size": [315, 76], "flags": {}, "order": 77, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 16, "widget": {"name": "text"}}], "outputs": [{"name": "STRING", "type": "STRING", "links": null, "shape": 6}], "properties": {"Node name for S&R": "ShowText|pysssss"}, "widgets_values": ["", "bseparator0separator0 2"]}, {"id": 58, "type": "ShowText|pysssss", "pos": [-11299.99609375, -3383.1669921875], "size": [315, 76], "flags": {}, "order": 76, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 17, "widget": {"name": "text"}}], "outputs": [{"name": "STRING", "type": "STRING", "links": null, "shape": 6}], "properties": {"Node name for S&R": "ShowText|pysssss"}, "widgets_values": ["", "0"]}, {"id": 60, "type": "<PERSON>downNote", "pos": [-11900.6484375, -3872.76025390625], "size": [536.965087890625, 103.656005859375], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["# separator: range$$sep$$"], "color": "#432", "bgcolor": "#653"}, {"id": 36, "type": "<PERSON>downNote", "pos": [-12072.5009765625, -6767.2568359375], "size": [536.965087890625, 103.656005859375], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["# separator: range$$sep$$"], "color": "#432", "bgcolor": "#653"}, {"id": 59, "type": "<PERSON>downNote", "pos": [-13181.8271484375, -3886.89794921875], "size": [536.965087890625, 103.656005859375], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["# separator: range$$"], "color": "#432", "bgcolor": "#653"}, {"id": 91, "type": "ShowText|pysssss", "pos": [-12719.66796875, -6245.978515625], "size": [315, 76], "flags": {}, "order": 84, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 22, "widget": {"name": "text"}}], "outputs": [{"name": "STRING", "type": "STRING", "links": null, "shape": 6}], "properties": {"Node name for S&R": "ShowText|pysssss"}, "widgets_values": [""]}, {"id": 92, "type": "ShowText|pysssss", "pos": [-11472.8974609375, -6260.45703125], "size": [315, 76], "flags": {}, "order": 87, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 23, "widget": {"name": "text"}}], "outputs": [{"name": "STRING", "type": "STRING", "links": null, "shape": 6}], "properties": {"Node name for S&R": "ShowText|pysssss"}, "widgets_values": [""]}, {"id": 97, "type": "ShowText|pysssss", "pos": [-12766.0146484375, -5921.5537109375], "size": [315, 76], "flags": {}, "order": 83, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 24, "widget": {"name": "text"}}], "outputs": [{"name": "STRING", "type": "STRING", "links": null, "shape": 6}], "properties": {"Node name for S&R": "ShowText|pysssss"}, "widgets_values": ["", "0, 1, 2"]}, {"id": 98, "type": "ShowText|pysssss", "pos": [-11519.244140625, -5936.0322265625], "size": [315, 76], "flags": {}, "order": 88, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 25, "widget": {"name": "text"}}], "outputs": [{"name": "STRING", "type": "STRING", "links": null, "shape": 6}], "properties": {"Node name for S&R": "ShowText|pysssss"}, "widgets_values": ["", "0separator0, 1, 2separator0, 1"]}, {"id": 103, "type": "ShowText|pysssss", "pos": [-12766.0166015625, -5608.7158203125], "size": [315, 76], "flags": {}, "order": 82, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 26, "widget": {"name": "text"}}], "outputs": [{"name": "STRING", "type": "STRING", "links": null, "shape": 6}], "properties": {"Node name for S&R": "ShowText|pysssss"}, "widgets_values": ["", "2, 1 0 1 0 2"]}, {"id": 104, "type": "ShowText|pysssss", "pos": [-11519.24609375, -5623.1943359375], "size": [315, 76], "flags": {}, "order": 89, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 27, "widget": {"name": "text"}}], "outputs": [{"name": "STRING", "type": "STRING", "links": null, "shape": 6}], "properties": {"Node name for S&R": "ShowText|pysssss"}, "widgets_values": ["", "0, 1separator1 0 2separator0 2"]}, {"id": 35, "type": "<PERSON>downNote", "pos": [-13353.6796875, -6781.39453125], "size": [536.965087890625, 103.656005859375], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["# separator: range$$"], "color": "#432", "bgcolor": "#653"}, {"id": 109, "type": "ShowText|pysssss", "pos": [-12747.9560546875, -5278.75244140625], "size": [315, 76], "flags": {}, "order": 81, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 28, "widget": {"name": "text"}}], "outputs": [{"name": "STRING", "type": "STRING", "links": null, "shape": 6}], "properties": {"Node name for S&R": "ShowText|pysssss"}, "widgets_values": ["", "0, 1, 2"]}, {"id": 110, "type": "ShowText|pysssss", "pos": [-11501.185546875, -5293.23095703125], "size": [315, 76], "flags": {}, "order": 90, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 29, "widget": {"name": "text"}}], "outputs": [{"name": "STRING", "type": "STRING", "links": null, "shape": 6}], "properties": {"Node name for S&R": "ShowText|pysssss"}, "widgets_values": ["", "0, 1, 2"]}, {"id": 115, "type": "ShowText|pysssss", "pos": [-12716.0927734375, -4941.001953125], "size": [315, 76], "flags": {}, "order": 80, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 30, "widget": {"name": "text"}}], "outputs": [{"name": "STRING", "type": "STRING", "links": null, "shape": 6}], "properties": {"Node name for S&R": "ShowText|pysssss"}, "widgets_values": ["", "0"]}, {"id": 116, "type": "ShowText|pysssss", "pos": [-11469.322265625, -4955.48046875], "size": [315, 76], "flags": {}, "order": 91, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 31, "widget": {"name": "text"}}], "outputs": [{"name": "STRING", "type": "STRING", "links": null, "shape": 6}], "properties": {"Node name for S&R": "ShowText|pysssss"}, "widgets_values": ["", "0"]}, {"id": 121, "type": "ShowText|pysssss", "pos": [-12728.8369140625, -4574.576171875], "size": [315, 76], "flags": {}, "order": 79, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 32, "widget": {"name": "text"}}], "outputs": [{"name": "STRING", "type": "STRING", "links": null, "shape": 6}], "properties": {"Node name for S&R": "ShowText|pysssss"}, "widgets_values": ["", "__test_wildcard/empty_item/*__"]}, {"id": 122, "type": "ShowText|pysssss", "pos": [-11482.06640625, -4589.0546875], "size": [315, 76], "flags": {}, "order": 92, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 33, "widget": {"name": "text"}}], "outputs": [{"name": "STRING", "type": "STRING", "links": null, "shape": 6}], "properties": {"Node name for S&R": "ShowText|pysssss"}, "widgets_values": ["", "__test_wildcard/empty_item/*__"]}, {"id": 126, "type": "<PERSON>downNote", "pos": [-12238.06640625, -4571.01171875], "size": [284.1663513183594, 190.96800231933594], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["# source: $$\\_\\_test\\_\\_wildcard/reference_empty_item/*\\_\\_\nexpected  \nseparator = sep  \nprobability: len(result)  \n \"\": 100%, no throw error\n"], "color": "#432", "bgcolor": "#653"}, {"id": 96, "type": "<PERSON>downNote", "pos": [-12228.8974609375, -6242.4140625], "size": [284.1663513183594, 190.96800231933594], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["# source: $$\\_\\_test\\_\\_wildcard/empty_text/*\\_\\_\nexpected  \nseparator = sep  \nprobability: len(result)  \n \"\": 100%, no throw error\n"], "color": "#432", "bgcolor": "#653"}, {"id": 37, "type": "<PERSON>downNote", "pos": [-12239.9609375, -6576.40087890625], "size": [284.1663513183594, 190.96800231933594], "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["# source: $$\\_\\_test\\_\\_wildcard/single_text_only/*\\_\\_\nexpected  \nseparator = sep  \nprobability: len(result)  \n 1: 100%, no throw error\n"], "color": "#432", "bgcolor": "#653"}, {"id": 43, "type": "<PERSON>downNote", "pos": [-13532.40234375, -6566.12890625], "size": [284.1663513183594, 190.96800231933594], "flags": {}, "order": 7, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["# source: $$\\_\\_test\\_wildcard/single_text_only/*\\_\\_\nexpected  \nseparator = \" \"  \nprobability: len(result)  \n 1: 100%, no throw error\n"], "color": "#432", "bgcolor": "#653"}, {"id": 125, "type": "<PERSON>downNote", "pos": [-13530.5078125, -4560.73974609375], "size": [284.1663513183594, 190.96800231933594], "flags": {}, "order": 8, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["# source: $$\\_\\_test\\_wildcard/reference_empty_item/*\\_\\_\nexpected  \nseparator = \" \"  \nprobability: len(result)  \n \"\": 100%, no throw error\n"], "color": "#432", "bgcolor": "#653"}, {"id": 47, "type": "ImpactWildcardProcessor", "pos": [-13009.509765625, -2812.1884765625], "size": [400, 222], "flags": {}, "order": 9, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [10], "slot_index": 0}], "properties": {"Node name for S&R": "ImpactWildcardProcessor"}, "widgets_values": ["{1-$$__test_wildcard/include_all/*__}", "0 0", "populate", 898121972509601, "randomize", "Select the Wildcard to add to the text"]}, {"id": 53, "type": "ImpactWildcardProcessor", "pos": [-13010.13671875, -3088.3740234375], "size": [400, 222], "flags": {}, "order": 10, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [12], "slot_index": 0}], "properties": {"Node name for S&R": "ImpactWildcardProcessor"}, "widgets_values": ["{1-9999999$$__test_wildcard/include_all/*__}", "0 0", "populate", ***************, "randomize", "Select the Wildcard to add to the text"]}, {"id": 49, "type": "ImpactWildcardProcessor", "pos": [-13005.67578125, -3364.984375], "size": [400, 222], "flags": {}, "order": 11, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [13], "slot_index": 0}], "properties": {"Node name for S&R": "ImpactWildcardProcessor"}, "widgets_values": ["{1-3$$__test_wildcard/include_all/*__}", "0 __test_wildcard/empty_item/*__", "populate", 575439885343933, "randomize", "Select the Wildcard to add to the text"]}, {"id": 54, "type": "ImpactWildcardProcessor", "pos": [-13009.6826171875, -3681.7646484375], "size": [400, 222], "flags": {}, "order": 12, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [11], "slot_index": 0}], "properties": {"Node name for S&R": "ImpactWildcardProcessor"}, "widgets_values": ["{-3$$__test_wildcard/include_all/*__}", "0 __test_wildcard/empty_item/*__ 0", "populate", ***************, "randomize", "Select the Wildcard to add to the text"]}, {"id": 61, "type": "ImpactWildcardProcessor", "pos": [-11762.912109375, -3696.2431640625], "size": [400, 222], "flags": {}, "order": 13, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [15], "slot_index": 0}], "properties": {"Node name for S&R": "ImpactWildcardProcessor"}, "widgets_values": ["{-3$$separator$$__test_wildcard/include_all/*__}", "0, 1, 2separator0", "populate", 866104734173401, "randomize", "Select the Wildcard to add to the text"]}, {"id": 62, "type": "ImpactWildcardProcessor", "pos": [-11758.9052734375, -3379.462890625], "size": [400, 222], "flags": {}, "order": 14, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [17], "slot_index": 0}], "properties": {"Node name for S&R": "ImpactWildcardProcessor"}, "widgets_values": ["{1-3$$separator$$__test_wildcard/include_all/*__}", "0", "populate", 906296272550597, "randomize", "Select the Wildcard to add to the text"]}, {"id": 63, "type": "ImpactWildcardProcessor", "pos": [-11763.3662109375, -3102.8525390625], "size": [400, 222], "flags": {}, "order": 15, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [16], "slot_index": 0}], "properties": {"Node name for S&R": "ImpactWildcardProcessor"}, "widgets_values": ["{1-9999999$$separator$$__test_wildcard/include_all/*__}", "bseparator0separator0 2", "populate", 619593260395734, "randomize", "Select the Wildcard to add to the text"]}, {"id": 64, "type": "ImpactWildcardProcessor", "pos": [-11762.7392578125, -2826.6669921875], "size": [400, 222], "flags": {}, "order": 16, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [14], "slot_index": 0}], "properties": {"Node name for S&R": "ImpactWildcardProcessor"}, "widgets_values": ["{1-$$separator$$__test_wildcard/include_all/*__}", "0separator0separatorbseparator0separator__test_wildcard/empty_item/*__separator1, 0", "populate", 1093508569142815, "randomize", "Select the Wildcard to add to the text"]}, {"id": 55, "type": "ShowText|pysssss", "pos": [-11292.8681640625, -2825.79443359375], "size": [330.10296630859375, 263.9482116699219], "flags": {}, "order": 78, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 14, "widget": {"name": "text"}}], "outputs": [{"name": "STRING", "type": "STRING", "links": null, "shape": 6}], "properties": {"Node name for S&R": "ShowText|pysssss"}, "widgets_values": ["", "0separator0separatorbseparator0separator__test_wildcard/empty_item/*__separator1, 0"]}, {"id": 72, "type": "<PERSON>downNote", "pos": [-12062.1220703125, -2822.9970703125], "size": [281.3926086425781, 206.91697692871094], "flags": {}, "order": 17, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["# range:  n1-$$\n## expected  \nseparator = sep  \nprobability: len(result)  \n all pattern\n\n## actual\nnot supported  \nprobability: len(result)  \n 1: 100%, 2: 0%, 3: 0%. 4: 0%  "], "color": "#432", "bgcolor": "#653"}, {"id": 67, "type": "<PERSON>downNote", "pos": [-13354.5634765625, -2812.72412109375], "size": [281.3926086425781, 206.91697692871094], "flags": {}, "order": 18, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["# range:  n1-$$\n## expected  \nseparator = \" \"  \nprobability: len(result)  \n all pattern\n \n## actual\nnot supported  \nprobability: len(result)  \n 1: 100%, 2: 0%, 3: 0%. 4: 0%  "], "color": "#432", "bgcolor": "#653"}, {"id": 65, "type": "<PERSON>downNote", "pos": [-13352.6298828125, -3087.4697265625], "size": [284.8597717285156, 218.01190185546875], "flags": {}, "order": 19, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["# range:  n1-(itemcount <<<< n2)$$\n## expected  \nseparator = \" \"  \nprobability: len(result)  \n all pattern\n \n## actual\nprobability: len(result)  \n 1: 0%, 2: 0%, 3: 0%... max count: 99% \n"], "color": "#432", "bgcolor": "#653"}, {"id": 71, "type": "<PERSON>downNote", "pos": [-12064.3828125, -3106.1318359375], "size": [284.8597717285156, 218.01190185546875], "flags": {}, "order": 20, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["# range:  n1-(itemcount <<<< n2)$$\n## expected  \nseparator = sep  \nprobability: len(result)  \n all pattern\n\n## actual\nprobability: len(result)  \n 1: 0%, 2: 0%, 3: 0%... max count: 99% \n"], "color": "#432", "bgcolor": "#653"}, {"id": 70, "type": "<PERSON>downNote", "pos": [-12058.2548828125, -3357.92578125], "size": [284.1663513183594, 190.96800231933594], "flags": {}, "order": 21, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["# range: n1-n2$$\nexpected  \nseparator = sep  \nprobability: len(result)  \n 1: 33%, 2: 33%, 3: 33%\n"], "color": "#432", "bgcolor": "#653"}, {"id": 66, "type": "<PERSON>downNote", "pos": [-13350.6962890625, -3347.65380859375], "size": [284.1663513183594, 190.96800231933594], "flags": {}, "order": 22, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["# range: n1-n2$$\nexpected  \nseparator = \" \"  \nprobability: len(result)  \n 1: 33%, 2: 33%, 3: 33%\n"], "color": "#432", "bgcolor": "#653"}, {"id": 68, "type": "<PERSON>downNote", "pos": [-13360.5498046875, -3671.63232421875], "size": [284.1663513183594, 190.96800231933594], "flags": {}, "order": 23, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["# range: -n$$\nexpected  \nseparator = \" \"  \nprobability: len(result)  \n 1: 33%, 2: 33%, 3: 33%\n"], "color": "#432", "bgcolor": "#653"}, {"id": 69, "type": "<PERSON>downNote", "pos": [-12068.1083984375, -3681.904296875], "size": [284.1663513183594, 190.96800231933594], "flags": {}, "order": 24, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["# range: -n$$\nexpected  \nseparator = sep  \nprobability: len(result)  \n 1: 33%, 2: 33%, 3: 33%\n"], "color": "#432", "bgcolor": "#653"}, {"id": 123, "type": "ImpactWildcardProcessor", "pos": [-13179.640625, -4570.8720703125], "size": [400, 222], "flags": {}, "order": 25, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [32], "slot_index": 0}], "properties": {"Node name for S&R": "ImpactWildcardProcessor"}, "widgets_values": ["{1-3$$__test_wildcard/reference_empty_item/*__}", "__test_wildcard/empty_item/*__", "populate", 54896724395668, "randomize", "Select the Wildcard to add to the text"]}, {"id": 117, "type": "ImpactWildcardProcessor", "pos": [-13166.896484375, -4937.2978515625], "size": [400, 222], "flags": {}, "order": 26, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [30], "slot_index": 0}], "properties": {"Node name for S&R": "ImpactWildcardProcessor"}, "widgets_values": ["{1-3$$__test_wildcard/reference_single_text_only_wildcard/*__}", "0", "populate", 391688626288181, "randomize", "Select the Wildcard to add to the text"]}, {"id": 111, "type": "ImpactWildcardProcessor", "pos": [-13198.759765625, -5275.04833984375], "size": [400, 222], "flags": {}, "order": 27, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [28], "slot_index": 0}], "properties": {"Node name for S&R": "ImpactWildcardProcessor"}, "widgets_values": ["{1-3$$__test_wildcard/reference_wildcard/*__}", "0, 1, 2", "populate", 296622801038825, "randomize", "Select the Wildcard to add to the text"]}, {"id": 105, "type": "ImpactWildcardProcessor", "pos": [-13216.8203125, -5605.01171875], "size": [400, 222], "flags": {}, "order": 28, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [26], "slot_index": 0}], "properties": {"Node name for S&R": "ImpactWildcardProcessor"}, "widgets_values": ["{1-3$$__test_wildcard/raw_wildcard/*__}", "2, 1 0 1 0 2", "populate", 533570818504916, "randomize", "Select the Wildcard to add to the text"]}, {"id": 99, "type": "ImpactWildcardProcessor", "pos": [-13216.818359375, -5917.849609375], "size": [400, 222], "flags": {}, "order": 29, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [24], "slot_index": 0}], "properties": {"Node name for S&R": "ImpactWildcardProcessor"}, "widgets_values": ["{1-3$$__test_wildcard/text_only/*__}", "0, 1, 2", "populate", 115645098449181, "randomize", "Select the Wildcard to add to the text"]}, {"id": 93, "type": "ImpactWildcardProcessor", "pos": [-13170.4716796875, -6242.2744140625], "size": [400, 222], "flags": {}, "order": 30, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [22], "slot_index": 0}], "properties": {"Node name for S&R": "ImpactWildcardProcessor"}, "widgets_values": ["{1-3$$__test_wildcard/empty_text/*__}", "", "populate", 232076456423613, "randomize", "Select the Wildcard to add to the text"]}, {"id": 8, "type": "ImpactWildcardProcessor", "pos": [-13181.53515625, -6576.26123046875], "size": [400, 222], "flags": {}, "order": 31, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [4], "slot_index": 0}], "properties": {"Node name for S&R": "ImpactWildcardProcessor"}, "widgets_values": ["{1-3$$__test_wildcard/single_text_only/*__}", "0", "populate", 296246500565067, "randomize", "Select the Wildcard to add to the text"]}, {"id": 29, "type": "ImpactWildcardProcessor", "pos": [-11934.7646484375, -6590.73974609375], "size": [400, 222], "flags": {}, "order": 32, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [7], "slot_index": 0}], "properties": {"Node name for S&R": "ImpactWildcardProcessor"}, "widgets_values": ["{1-3$$separator$$__test_wildcard/single_text_only/*__}", "0", "populate", 760939738881648, "randomize", "Select the Wildcard to add to the text"]}, {"id": 94, "type": "ImpactWildcardProcessor", "pos": [-11923.701171875, -6256.7529296875], "size": [400, 222], "flags": {}, "order": 33, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [23], "slot_index": 0}], "properties": {"Node name for S&R": "ImpactWildcardProcessor"}, "widgets_values": ["{1-3$$separator$$__test_wildcard/empty_text/*__}", "", "populate", 536551955956330, "randomize", "Select the Wildcard to add to the text"]}, {"id": 100, "type": "ImpactWildcardProcessor", "pos": [-11970.0478515625, -5932.328125], "size": [400, 222], "flags": {}, "order": 34, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [25], "slot_index": 0}], "properties": {"Node name for S&R": "ImpactWildcardProcessor"}, "widgets_values": ["{1-3$$separator$$__test_wildcard/text_only/*__}", "0separator0, 1, 2separator0, 1", "populate", 907373058546822, "randomize", "Select the Wildcard to add to the text"]}, {"id": 106, "type": "ImpactWildcardProcessor", "pos": [-11970.0498046875, -5619.490234375], "size": [400, 222], "flags": {}, "order": 35, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [27], "slot_index": 0}], "properties": {"Node name for S&R": "ImpactWildcardProcessor"}, "widgets_values": ["{1-3$$separator$$__test_wildcard/raw_wildcard/*__}", "0, 1separator1 0 2separator0 2", "populate", 901647112171264, "randomize", "Select the Wildcard to add to the text"]}, {"id": 112, "type": "ImpactWildcardProcessor", "pos": [-11951.9892578125, -5289.52685546875], "size": [400, 222], "flags": {}, "order": 36, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [29], "slot_index": 0}], "properties": {"Node name for S&R": "ImpactWildcardProcessor"}, "widgets_values": ["{1-3$$separator$$__test_wildcard/reference_wildcard/*__}", "0, 1, 2", "populate", 871387361943030, "randomize", "Select the Wildcard to add to the text"]}, {"id": 118, "type": "ImpactWildcardProcessor", "pos": [-11920.1259765625, -4951.7763671875], "size": [400, 222], "flags": {}, "order": 37, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [31], "slot_index": 0}], "properties": {"Node name for S&R": "ImpactWildcardProcessor"}, "widgets_values": ["{1-3$$separator$$__test_wildcard/reference_single_text_only_wildcard/*__}", "0", "populate", ***************, "randomize", "Select the Wildcard to add to the text"]}, {"id": 124, "type": "ImpactWildcardProcessor", "pos": [-11932.8701171875, -4585.3505859375], "size": [400, 222], "flags": {}, "order": 38, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [33], "slot_index": 0}], "properties": {"Node name for S&R": "ImpactWildcardProcessor"}, "widgets_values": ["{1-3$$separator$$__test_wildcard/reference_empty_item/*__}", "__test_wildcard/empty_item/*__", "populate", ***************, "randomize", "Select the Wildcard to add to the text"]}, {"id": 128, "type": "ShowText|pysssss", "pos": [-10112.162109375, -6622.95751953125], "size": [315, 76], "flags": {}, "order": 93, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 34, "widget": {"name": "text"}}], "outputs": [{"name": "STRING", "type": "STRING", "links": null, "shape": 6}], "properties": {"Node name for S&R": "ShowText|pysssss"}, "widgets_values": ["", "0"]}, {"id": 129, "type": "ShowText|pysssss", "pos": [-8865.3916015625, -6637.43603515625], "size": [315, 76], "flags": {}, "order": 94, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 35, "widget": {"name": "text"}}], "outputs": [{"name": "STRING", "type": "STRING", "links": null, "shape": 6}], "properties": {"Node name for S&R": "ShowText|pysssss"}, "widgets_values": ["", "0separator1"]}, {"id": 130, "type": "<PERSON>downNote", "pos": [-9453.931640625, -6810.2490234375], "size": [536.965087890625, 103.656005859375], "flags": {}, "order": 39, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["# separator: range$$sep$$"], "color": "#432", "bgcolor": "#653"}, {"id": 131, "type": "<PERSON>downNote", "pos": [-10735.1103515625, -6824.38671875], "size": [536.965087890625, 103.656005859375], "flags": {}, "order": 40, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["# separator: range$$"], "color": "#432", "bgcolor": "#653"}, {"id": 134, "type": "ImpactWildcardProcessor", "pos": [-10562.9658203125, -6619.25341796875], "size": [400, 222], "flags": {}, "order": 41, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [34], "slot_index": 0}], "properties": {"Node name for S&R": "ImpactWildcardProcessor"}, "widgets_values": ["{1-3$$__test_wildcard/numberonly/type_int/*__}", "0", "populate", ***************, "randomize", "Select the Wildcard to add to the text"]}, {"id": 135, "type": "ImpactWildcardProcessor", "pos": [-9316.1953125, -6633.73193359375], "size": [400, 222], "flags": {}, "order": 42, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [35], "slot_index": 0}], "properties": {"Node name for S&R": "ImpactWildcardProcessor"}, "widgets_values": ["{1-3$$separator$$__test_wildcard/numberonly/type_int/*__}", "0separator1", "populate", 753049590556052, "randomize", "Select the Wildcard to add to the text"]}, {"id": 136, "type": "ShowText|pysssss", "pos": [-10099.29296875, -6271.14697265625], "size": [315, 76], "flags": {}, "order": 96, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 36, "widget": {"name": "text"}}], "outputs": [{"name": "STRING", "type": "STRING", "links": null, "shape": 6}], "properties": {"Node name for S&R": "ShowText|pysssss"}, "widgets_values": ["", "0"]}, {"id": 137, "type": "ShowText|pysssss", "pos": [-8852.521484375, -6285.62548828125], "size": [315, 76], "flags": {}, "order": 95, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 37, "widget": {"name": "text"}}], "outputs": [{"name": "STRING", "type": "STRING", "links": null, "shape": 6}], "properties": {"Node name for S&R": "ShowText|pysssss"}, "widgets_values": ["", "0"]}, {"id": 138, "type": "<PERSON>downNote", "pos": [-10900.9638671875, -6257.310546875], "size": [284.1663513183594, 190.96800231933594], "flags": {}, "order": 43, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["# source: $$\\_\\_test\\_wildcard/numberonly/type_int2/*\\_\\_\nexpected  \nseparator = \" \"  \nprobability: len(result)  \n 1: 100%, no throw error\n"], "color": "#432", "bgcolor": "#653"}, {"id": 139, "type": "<PERSON>downNote", "pos": [-9608.5224609375, -6267.58251953125], "size": [284.1663513183594, 190.96800231933594], "flags": {}, "order": 44, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["# source: $$\\_\\_test\\_wildcard/numberonly/type_int2/*\\_\\_\nexpected  \nseparator = sep  \nprobability: len(result)  \n 1: 100%, no throw error\n"], "color": "#432", "bgcolor": "#653"}, {"id": 141, "type": "ImpactWildcardProcessor", "pos": [-9303.326171875, -6281.92138671875], "size": [400, 222], "flags": {}, "order": 45, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [37], "slot_index": 0}], "properties": {"Node name for S&R": "ImpactWildcardProcessor"}, "widgets_values": ["{1-3$$separator$$__test_wildcard/numberonly/type_int2/*__}", "0", "populate", ***************, "randomize", "Select the Wildcard to add to the text"]}, {"id": 140, "type": "ImpactWildcardProcessor", "pos": [-10550.0966796875, -6267.44287109375], "size": [400, 222], "flags": {}, "order": 46, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [36], "slot_index": 0}], "properties": {"Node name for S&R": "ImpactWildcardProcessor"}, "widgets_values": ["{1-3$$__test_wildcard/numberonly/type_int2/*__}", "0", "populate", 949173770490, "randomize", "Select the Wildcard to add to the text"]}, {"id": 142, "type": "ShowText|pysssss", "pos": [-10094.38671875, -5643.3115234375], "size": [315, 76], "flags": {}, "order": 100, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 38, "widget": {"name": "text"}}], "outputs": [{"name": "STRING", "type": "STRING", "links": null, "shape": 6}], "properties": {"Node name for S&R": "ShowText|pysssss"}, "widgets_values": ["", "0 1"]}, {"id": 143, "type": "ShowText|pysssss", "pos": [-8847.6171875, -5657.7900390625], "size": [315, 76], "flags": {}, "order": 97, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 39, "widget": {"name": "text"}}], "outputs": [{"name": "STRING", "type": "STRING", "links": null, "shape": 6}], "properties": {"Node name for S&R": "ShowText|pysssss"}, "widgets_values": ["", "1separator0"]}, {"id": 148, "type": "ShowText|pysssss", "pos": [-10081.517578125, -5291.5009765625], "size": [315, 76], "flags": {}, "order": 99, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 40, "widget": {"name": "text"}}], "outputs": [{"name": "STRING", "type": "STRING", "links": null, "shape": 6}], "properties": {"Node name for S&R": "ShowText|pysssss"}, "widgets_values": ["", "1"]}, {"id": 149, "type": "ShowText|pysssss", "pos": [-8834.7470703125, -5305.9794921875], "size": [315, 76], "flags": {}, "order": 98, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 41, "widget": {"name": "text"}}], "outputs": [{"name": "STRING", "type": "STRING", "links": null, "shape": 6}], "properties": {"Node name for S&R": "ShowText|pysssss"}, "widgets_values": ["", "1"]}, {"id": 147, "type": "ImpactWildcardProcessor", "pos": [-9298.4208984375, -5654.0859375], "size": [400, 222], "flags": {}, "order": 47, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [39], "slot_index": 0}], "properties": {"Node name for S&R": "ImpactWildcardProcessor"}, "widgets_values": ["{1-3$$separator$$__test_wildcard/numberonly/type_str/*__}", "1separator0", "populate", 508734479462831, "randomize", "Select the Wildcard to add to the text"]}, {"id": 151, "type": "<PERSON>downNote", "pos": [-9590.7470703125, -5287.9365234375], "size": [284.1663513183594, 190.96800231933594], "flags": {}, "order": 48, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["# source: $$\\_\\_test\\_wildcard/numberonly/type_str2/*\\_\\_\nexpected  \nseparator = sep  \nprobability: len(result)  \n 1: 100%, no throw error\n"], "color": "#432", "bgcolor": "#653"}, {"id": 152, "type": "ImpactWildcardProcessor", "pos": [-9285.5517578125, -5302.275390625], "size": [400, 222], "flags": {}, "order": 49, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [41], "slot_index": 0}], "properties": {"Node name for S&R": "ImpactWildcardProcessor"}, "widgets_values": ["{1-3$$separator$$__test_wildcard/numberonly/type_str2/*__}", "1", "populate", 1067712563048875, "randomize", "Select the Wildcard to add to the text"]}, {"id": 153, "type": "ImpactWildcardProcessor", "pos": [-10532.3212890625, -5287.796875], "size": [400, 222], "flags": {}, "order": 50, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [40], "slot_index": 0}], "properties": {"Node name for S&R": "ImpactWildcardProcessor"}, "widgets_values": ["{1-3$$__test_wildcard/numberonly/type_str2/*__}", "1", "populate", 902341396140779, "randomize", "Select the Wildcard to add to the text"]}, {"id": 150, "type": "<PERSON>downNote", "pos": [-10883.1884765625, -5277.66455078125], "size": [284.1663513183594, 190.96800231933594], "flags": {}, "order": 51, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["# source: $$\\_\\_test\\_wildcard/numberonly/type_str2/*\\_\\_\nexpected  \nseparator = \" \"  \nprobability: len(result)  \n 1: 100%, no throw error\n"], "color": "#432", "bgcolor": "#653"}, {"id": 145, "type": "<PERSON>downNote", "pos": [-9603.6162109375, -5639.7470703125], "size": [284.1663513183594, 190.96800231933594], "flags": {}, "order": 52, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["# source: $$\\_\\_test\\_wildcard/numberonly/type_str/*\\_\\_\nexpected  \nseparator = sep  \nprobability: len(result)  \n 1: 50%, no throw error  \n 2: 50%, no throw error  \n"], "color": "#432", "bgcolor": "#653"}, {"id": 144, "type": "<PERSON>downNote", "pos": [-10896.0576171875, -5629.47509765625], "size": [284.1663513183594, 190.96800231933594], "flags": {}, "order": 53, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["# source: $$\\_\\_test\\_wildcard/numberonly/type_str/*\\_\\_\nexpected  \nseparator = \" \"  \nprobability: len(result)  \n 1: 50%, no throw error  \n 2: 50%, no throw error  \n"], "color": "#432", "bgcolor": "#653"}, {"id": 133, "type": "<PERSON>downNote", "pos": [-10913.8330078125, -6609.12109375], "size": [284.1663513183594, 190.96800231933594], "flags": {}, "order": 54, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["# source: $$\\_\\_test\\_wildcard/numberonly/type_int/*\\_\\_\nexpected  \nseparator = \" \"  \nprobability: len(result)  \n 1: 50%, no throw error  \n 2: 50%, no throw error  \n"], "color": "#432", "bgcolor": "#653"}, {"id": 132, "type": "<PERSON>downNote", "pos": [-9621.3916015625, -6619.39306640625], "size": [284.1663513183594, 190.96800231933594], "flags": {}, "order": 55, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["# source: $$\\_\\_test\\_wildcard/numberonly/type_int/*\\_\\_\nexpected  \nseparator = sep  \nprobability: len(result)  \n 1: 50%, no throw error  \n 2: 50%, no throw error  \n"], "color": "#432", "bgcolor": "#653"}, {"id": 146, "type": "ImpactWildcardProcessor", "pos": [-10545.1904296875, -5639.607421875], "size": [400, 222], "flags": {}, "order": 56, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [38], "slot_index": 0}], "properties": {"Node name for S&R": "ImpactWildcardProcessor"}, "widgets_values": ["{1-3$$__test_wildcard/numberonly/type_str/*__}", "0 1", "populate", 217160399221458, "randomize", "Select the Wildcard to add to the text"]}, {"id": 154, "type": "ShowText|pysssss", "pos": [-10087.2021484375, -5960.27001953125], "size": [315, 76], "flags": {}, "order": 101, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 42, "widget": {"name": "text"}}], "outputs": [{"name": "STRING", "type": "STRING", "links": null, "shape": 6}], "properties": {"Node name for S&R": "ShowText|pysssss"}, "widgets_values": ["", "1"]}, {"id": 155, "type": "ShowText|pysssss", "pos": [-8840.431640625, -5974.74853515625], "size": [315, 76], "flags": {}, "order": 102, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 43, "widget": {"name": "text"}}], "outputs": [{"name": "STRING", "type": "STRING", "links": null, "shape": 6}], "properties": {"Node name for S&R": "ShowText|pysssss"}, "widgets_values": ["", "1"]}, {"id": 156, "type": "<PERSON>downNote", "pos": [-10888.873046875, -5946.43359375], "size": [284.1663513183594, 190.96800231933594], "flags": {}, "order": 57, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["# source: $$\\_\\_test\\_wildcard/numberonly/type_int3/*\\_\\_\nexpected  \nseparator = \" \"  \nprobability: len(result)  \n 1: 100%, no throw error\n"], "color": "#432", "bgcolor": "#653"}, {"id": 159, "type": "ImpactWildcardProcessor", "pos": [-10538.005859375, -5956.56591796875], "size": [400, 222], "flags": {}, "order": 58, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [42], "slot_index": 0}], "properties": {"Node name for S&R": "ImpactWildcardProcessor"}, "widgets_values": ["{1-3$$__test_wildcard/numberonly/type_int3/*__}", "1", "populate", 939551364174087, "randomize", "Select the Wildcard to add to the text"]}, {"id": 157, "type": "<PERSON>downNote", "pos": [-9596.4326171875, -5956.70556640625], "size": [284.1663513183594, 190.96800231933594], "flags": {}, "order": 59, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["# source: $$\\_\\_test\\_wildcard/numberonly/type_int3/*\\_\\_\nexpected  \nseparator = sep  \nprobability: len(result)  \n 1: 100%, no throw error\n"], "color": "#432", "bgcolor": "#653"}, {"id": 158, "type": "ImpactWildcardProcessor", "pos": [-9291.236328125, -5971.04443359375], "size": [400, 222], "flags": {}, "order": 60, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [43], "slot_index": 0}], "properties": {"Node name for S&R": "ImpactWildcardProcessor"}, "widgets_values": ["{1-3$$separator$$__test_wildcard/numberonly/type_int3/*__}", "1", "populate", 801077747724021, "randomize", "Select the Wildcard to add to the text"]}, {"id": 95, "type": "<PERSON>downNote", "pos": [-13515.75390625, -6241.0751953125], "size": [284.1663513183594, 190.96800231933594], "flags": {}, "order": 61, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["# source: $$\\_\\_test\\_wildcard/empty_text/*\\_\\_\nexpected  \nseparator = \" \"  \nprobability: len(result)  \n \"\": 100%, no throw error\n"], "color": "#432", "bgcolor": "#653"}, {"id": 101, "type": "<PERSON>downNote", "pos": [-13543.1142578125, -5911.0673828125], "size": [284.1663513183594, 190.96800231933594], "flags": {}, "order": 62, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["# source: $$\\_\\_test\\_wildcard/text_only/*\\_\\_\nexpected  \nseparator = \" \"  \nprobability: len(result)  \n 1: 33%, 2: 33%, 3: 33%\n"], "color": "#432", "bgcolor": "#653"}, {"id": 102, "type": "<PERSON>downNote", "pos": [-12275.244140625, -5917.9892578125], "size": [284.1663513183594, 190.96800231933594], "flags": {}, "order": 63, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["# source: $$\\_\\_test\\_\\_wildcard/text_only/*\\_\\_\nexpected  \nseparator = sep  \nprobability: len(result)  \n 1: 33%, 2: 33%, 3: 33%\n"], "color": "#432", "bgcolor": "#653"}, {"id": 108, "type": "<PERSON>downNote", "pos": [-12275.24609375, -5605.1513671875], "size": [284.1663513183594, 190.96800231933594], "flags": {}, "order": 64, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["# source: $$\\_\\_test\\_\\_wildcard/raw_wildcard/*\\_\\_\nexpected  \nseparator = sep  \nprobability: len(result)  \n 1: 33%, 2: 33%, 3: 33%\n"], "color": "#432", "bgcolor": "#653"}, {"id": 107, "type": "<PERSON>downNote", "pos": [-13567.6875, -5594.87939453125], "size": [284.1663513183594, 190.96800231933594], "flags": {}, "order": 65, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["# source: $$\\_\\_test\\_wildcard/raw_wildcard/*\\_\\_\nexpected  \nseparator = \" \"  \nprobability: len(result)  \n 1: 33%, 2: 33%, 3: 33%\n"], "color": "#432", "bgcolor": "#653"}, {"id": 113, "type": "<PERSON>downNote", "pos": [-13549.626953125, -5264.916015625], "size": [284.1663513183594, 190.96800231933594], "flags": {}, "order": 66, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["# source: $$\\_\\_test\\_wildcard/reference_wildcard/*\\_\\_\nexpected  \nseparator = \" \"  \nprobability: len(result)  \n 1: 100%"], "color": "#432", "bgcolor": "#653"}, {"id": 114, "type": "<PERSON>downNote", "pos": [-12257.185546875, -5275.18798828125], "size": [284.1663513183594, 190.96800231933594], "flags": {}, "order": 67, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["# source: $$\\_\\_test\\_\\_wildcard/reference_wildcard/*\\_\\_\nexpected  \nseparator = sep  \nprobability: len(result)  \n 1: 100%\n"], "color": "#432", "bgcolor": "#653"}, {"id": 120, "type": "<PERSON>downNote", "pos": [-12225.322265625, -4937.4375], "size": [284.1663513183594, 190.96800231933594], "flags": {}, "order": 68, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["# source: $$\\_\\_test\\_\\_wildcard/reference_single_text_only_wildcard/*\\_\\_\nexpected  \nseparator = sep  \nprobability: len(result)  \n 1: 100%\n"], "color": "#432", "bgcolor": "#653"}, {"id": 119, "type": "<PERSON>downNote", "pos": [-13517.763671875, -4927.16552734375], "size": [284.1663513183594, 190.96800231933594], "flags": {}, "order": 69, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["# source: $$\\_\\_test\\_wildcard/reference_single_text_only_wildcard/*\\_\\_\nexpected  \nseparator = \" \"  \nprobability: len(result)  \n 1: 100%"], "color": "#432", "bgcolor": "#653"}, {"id": 161, "type": "<PERSON>downNote", "pos": [-14064.0478515625, -5289.62060546875], "size": [459.7430725097656, 304.9413146972656], "flags": {}, "order": 70, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["# Notice\n\n```\ntest_wildcard:\n  text_only:\n    - a\n    - b\n    - c\n\n  # {1-3$$__test_wildcard/text_only/*__} return a|b|c only\n  reference_wildcard:\n    - __test_wildcard/text_only/*__\n\n  # __test_wildcard/text_only/*__ return a|a b|a b c|...\n  reference_wildcard_mutiple:\n    - {1-3$$__test_wildcard/text_only/*__}\n\n```\n"], "color": "#432", "bgcolor": "#653"}], "links": [[4, 8, 0, 9, 0, "STRING"], [7, 29, 0, 25, 0, "STRING"], [10, 47, 0, 48, 0, "STRING"], [11, 54, 0, 50, 0, "STRING"], [12, 53, 0, 51, 0, "STRING"], [13, 49, 0, 52, 0, "STRING"], [14, 64, 0, 55, 0, "STRING"], [15, 61, 0, 56, 0, "STRING"], [16, 63, 0, 57, 0, "STRING"], [17, 62, 0, 58, 0, "STRING"], [22, 93, 0, 91, 0, "STRING"], [23, 94, 0, 92, 0, "STRING"], [24, 99, 0, 97, 0, "STRING"], [25, 100, 0, 98, 0, "STRING"], [26, 105, 0, 103, 0, "STRING"], [27, 106, 0, 104, 0, "STRING"], [28, 111, 0, 109, 0, "STRING"], [29, 112, 0, 110, 0, "STRING"], [30, 117, 0, 115, 0, "STRING"], [31, 118, 0, 116, 0, "STRING"], [32, 123, 0, 121, 0, "STRING"], [33, 124, 0, 122, 0, "STRING"], [34, 134, 0, 128, 0, "STRING"], [35, 135, 0, 129, 0, "STRING"], [36, 140, 0, 136, 0, "STRING"], [37, 141, 0, 137, 0, "STRING"], [38, 146, 0, 142, 0, "STRING"], [39, 147, 0, 143, 0, "STRING"], [40, 153, 0, 148, 0, "STRING"], [41, 152, 0, 149, 0, "STRING"], [42, 159, 0, 154, 0, "STRING"], [43, 158, 0, 155, 0, "STRING"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 1.196764113929575, "offset": [14049.181343985432, 5500.03266852812]}, "node_versions": {"ComfyUI-Custom-Scripts": "bc8922deff73f59311c05cef27b9d4caaf43e87b", "ComfyUI-Impact-Pack": "ebcb6f91abf4c8de1ab3636260959177615566ad"}}, "version": 0.4}