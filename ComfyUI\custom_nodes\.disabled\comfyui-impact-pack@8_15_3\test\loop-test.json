{"last_node_id": 43, "last_link_id": 49, "nodes": [{"id": 7, "type": "CLIPTextEncode", "pos": [413, 389], "size": {"0": 425.27801513671875, "1": 180.6060791015625}, "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 5}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [6], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["text, watermark"]}, {"id": 6, "type": "CLIPTextEncode", "pos": [415, 186], "size": {"0": 422.84503173828125, "1": 164.31304931640625}, "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 3}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [4], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["beautiful scenery nature glass bottle landscape, , purple galaxy bottle,"]}, {"id": 9, "type": "SaveImage", "pos": [1451, 189], "size": {"0": 210, "1": 270}, "flags": {}, "order": 11, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 9}], "properties": {}, "widgets_values": ["ComfyUI"]}, {"id": 4, "type": "CheckpointLoaderSimple", "pos": [26, 474], "size": {"0": 315, "1": 98}, "flags": {}, "order": 0, "mode": 0, "outputs": [{"name": "MODEL", "type": "MODEL", "links": [1], "slot_index": 0}, {"name": "CLIP", "type": "CLIP", "links": [3, 5], "slot_index": 1}, {"name": "VAE", "type": "VAE", "links": [8], "slot_index": 2}], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["V07_v07.safetensors"]}, {"id": 8, "type": "VAEDecode", "pos": [1209, 188], "size": {"0": 210, "1": 46}, "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 7}, {"name": "vae", "type": "VAE", "link": 8}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [9, 12], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}}, {"id": 19, "type": "ImpactMinMax", "pos": [2480, 1160], "size": {"0": 210, "1": 78}, "flags": {}, "order": 16, "mode": 0, "inputs": [{"name": "a", "type": "*", "link": 24}, {"name": "b", "type": "*", "link": 25, "slot_index": 1}], "outputs": [{"name": "INT", "type": "INT", "links": [34], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "ImpactMinMax"}, "widgets_values": [false]}, {"id": 15, "type": "ImpactValueSender", "pos": [3520, 1140], "size": {"0": 210, "1": 58}, "flags": {}, "order": 20, "mode": 0, "inputs": [{"name": "value", "type": "*", "link": 39}], "properties": {"Node name for S&R": "ImpactValueSender"}, "widgets_values": [0]}, {"id": 11, "type": "ImageMaskSwitch", "pos": [1297, 893], "size": {"0": 315, "1": 198}, "flags": {}, "order": 12, "mode": 0, "inputs": [{"name": "images1", "type": "IMAGE", "link": 12}, {"name": "mask1_opt", "type": "MASK", "link": null}, {"name": "images2_opt", "type": "IMAGE", "link": 11}, {"name": "mask2_opt", "type": "MASK", "link": null}, {"name": "images3_opt", "type": "IMAGE", "link": null}, {"name": "mask3_opt", "type": "MASK", "link": null}, {"name": "images4_opt", "type": "IMAGE", "link": null}, {"name": "mask4_opt", "type": "MASK", "link": null}, {"name": "select", "type": "INT", "link": 43, "widget": {"name": "select", "config": ["INT", {"default": 1, "min": 1, "max": 4, "step": 1}]}, "slot_index": 8}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [13], "shape": 3, "slot_index": 0}, {"name": "MASK", "type": "MASK", "links": null, "shape": 3}], "properties": {"Node name for S&R": "ImageMaskSwitch"}, "widgets_values": [1]}, {"id": 34, "type": "ImpactConditionalBranch", "pos": [3264, 1006], "size": {"0": 210, "1": 66}, "flags": {}, "order": 18, "mode": 0, "inputs": [{"name": "cond", "type": "BOOLEAN", "link": 36, "slot_index": 0}, {"name": "tt_value", "type": "*", "link": 37}, {"name": "ff_value", "type": "*", "link": 38}], "outputs": [{"name": "*", "type": "*", "links": [39], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "ImpactConditionalBranch"}}, {"id": 33, "type": "ImpactInt", "pos": [3010, 930], "size": {"0": 210, "1": 58}, "flags": {}, "order": 1, "mode": 0, "outputs": [{"name": "INT", "type": "INT", "links": [37], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "ImpactInt"}, "widgets_values": [2]}, {"id": 35, "type": "ImpactInt", "pos": [3000, 1140], "size": {"0": 210, "1": 58}, "flags": {}, "order": 2, "mode": 0, "outputs": [{"name": "INT", "type": "INT", "links": [38], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "ImpactInt"}, "widgets_values": [1]}, {"id": 5, "type": "EmptyLatentImage", "pos": [473, 609], "size": {"0": 315, "1": 106}, "flags": {}, "order": 3, "mode": 0, "outputs": [{"name": "LATENT", "type": "LATENT", "links": [2], "slot_index": 0}], "properties": {"Node name for S&R": "EmptyLatentImage"}, "widgets_values": [256, 256, 1]}, {"id": 13, "type": "ImageScaleBy", "pos": [1730, 920], "size": {"0": 210, "1": 82}, "flags": {}, "order": 13, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 13}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [23, 40], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "ImageScaleBy"}, "widgets_values": ["nearest-exact", 1.2]}, {"id": 41, "type": "ImpactConditionalStopIteration", "pos": [3607, 774], "size": {"0": 252, "1": 26}, "flags": {}, "order": 21, "mode": 0, "inputs": [{"name": "cond", "type": "BOOLEAN", "link": 49}], "properties": {"Node name for S&R": "ImpactConditionalStopIteration"}}, {"id": 32, "type": "ImpactCompare", "pos": [2760, 1040], "size": {"0": 210, "1": 78}, "flags": {}, "order": 17, "mode": 0, "inputs": [{"name": "a", "type": "*", "link": 47}, {"name": "b", "type": "*", "link": 34, "slot_index": 1}], "outputs": [{"name": "BOOLEAN", "type": "BOOLEAN", "links": [36, 48], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "ImpactCompare"}, "widgets_values": ["a > b"]}, {"id": 43, "type": "ImpactNeg", "pos": [3210.6906854687495, 698.6871511123657], "size": {"0": 210, "1": 26}, "flags": {}, "order": 19, "mode": 0, "inputs": [{"name": "value", "type": "BOOLEAN", "link": 48}], "outputs": [{"name": "BOOLEAN", "type": "BOOLEAN", "links": [49], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "ImpactNeg"}}, {"id": 10, "type": "ImageReceiver", "pos": [641, 932], "size": {"0": 315, "1": 200}, "flags": {}, "order": 4, "mode": 0, "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [11], "shape": 3, "slot_index": 0}, {"name": "MASK", "type": "MASK", "links": null, "shape": 3}], "properties": {"Node name for S&R": "ImageReceiver"}, "widgets_values": ["ImgSender_temp_vxhgs_00001_.png [temp]", 0]}, {"id": 24, "type": "ImpactImageInfo", "pos": [2077, 1117], "size": {"0": 210, "1": 86}, "flags": {}, "order": 14, "mode": 0, "inputs": [{"name": "value", "type": "IMAGE", "link": 23}], "outputs": [{"name": "batch", "type": "INT", "links": null, "shape": 3}, {"name": "height", "type": "INT", "links": [24], "shape": 3, "slot_index": 1}, {"name": "width", "type": "INT", "links": [25], "shape": 3}, {"name": "channel", "type": "INT", "links": null, "shape": 3}], "properties": {"Node name for S&R": "ImpactImageInfo"}}, {"id": 42, "type": "ImpactInt", "pos": [2483, 983], "size": {"0": 210, "1": 58}, "flags": {}, "order": 5, "mode": 0, "outputs": [{"name": "INT", "type": "INT", "links": [47], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "ImpactInt"}, "widgets_values": [768]}, {"id": 39, "type": "ImpactValueReceiver", "pos": [1021, 1137], "size": {"0": 210, "1": 106}, "flags": {}, "order": 6, "mode": 0, "outputs": [{"name": "*", "type": "*", "links": [43], "shape": 3}], "properties": {"Node name for S&R": "ImpactValueReceiver"}, "widgets_values": ["INT", 1, 0]}, {"id": 3, "type": "K<PERSON><PERSON><PERSON>", "pos": [872, 217], "size": {"0": 315, "1": 474}, "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 1}, {"name": "positive", "type": "CONDITIONING", "link": 4}, {"name": "negative", "type": "CONDITIONING", "link": 6}, {"name": "latent_image", "type": "LATENT", "link": 2}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [7], "slot_index": 0}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [901257808527154, "fixed", 5, 8, "euler", "normal", 1]}, {"id": 36, "type": "ImageSender", "pos": [2046, -116], "size": [914.2697004627885, 989.0802794506753], "flags": {}, "order": 15, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 40}], "properties": {"Node name for S&R": "ImageSender"}, "widgets_values": ["ImgSender", 0]}], "links": [[1, 4, 0, 3, 0, "MODEL"], [2, 5, 0, 3, 3, "LATENT"], [3, 4, 1, 6, 0, "CLIP"], [4, 6, 0, 3, 1, "CONDITIONING"], [5, 4, 1, 7, 0, "CLIP"], [6, 7, 0, 3, 2, "CONDITIONING"], [7, 3, 0, 8, 0, "LATENT"], [8, 4, 2, 8, 1, "VAE"], [9, 8, 0, 9, 0, "IMAGE"], [11, 10, 0, 11, 2, "IMAGE"], [12, 8, 0, 11, 0, "IMAGE"], [13, 11, 0, 13, 0, "IMAGE"], [23, 13, 0, 24, 0, "IMAGE"], [24, 24, 1, 19, 0, "*"], [25, 24, 2, 19, 1, "*"], [34, 19, 0, 32, 1, "*"], [36, 32, 0, 34, 0, "BOOLEAN"], [37, 33, 0, 34, 1, "*"], [38, 35, 0, 34, 2, "*"], [39, 34, 0, 15, 0, "*"], [40, 13, 0, 36, 0, "IMAGE"], [43, 39, 0, 11, 8, "INT"], [47, 42, 0, 32, 0, "*"], [48, 32, 0, 43, 0, "BOOLEAN"], [49, 43, 0, 41, 0, "BOOLEAN"]], "groups": [], "config": {}, "extra": {}, "version": 0.4}