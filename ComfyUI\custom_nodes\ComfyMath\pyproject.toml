[tool.poetry]
name = "comfymath"
version = "0.1.0"
description = "Math nodes for ComfyUI"
authors = ["<PERSON> <<EMAIL>>"]
license = { text = "Apache License 2.0" }
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.10"
numpy = "^1.25.1"

[tool.poetry.group.dev.dependencies]
mypy = "^1.4.1"
black = "^23.7.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
