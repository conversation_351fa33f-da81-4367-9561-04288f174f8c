[project]
name = "computer-vision"
description = "Extension nodes for ComfyUI that improves automatic segmentation using bounding boxes generated by Florence 2 and segmentation from Segment Anything 2 (SAM2). Currently just an enhancement of nodes from [a/Kijai](https://github.com/kijai/ComfyUI-segment-anything-2)."
version = "1.0.0"
license = {file = "LICENSE"}
dependencies = ["sahi>=0.11.0"]

[project.urls]
Repository = "https://github.com/MicheleGuidi/comfyui-computer-vision"
#  Used by Comfy Registry https://comfyregistry.org

[tool.comfy]
PublisherId = "micheleguidi"
DisplayName = "ComfyUI-Computer-Vision"
Icon = ""
