{"id": "95ecbebc-0f9d-4011-92d2-665645ad3fb7", "revision": 0, "last_node_id": 363, "last_link_id": 581, "nodes": [{"id": 271, "type": "Anything Everywhere?", "pos": [-5714.244140625, 1633.761474609375], "size": [315, 106], "flags": {}, "order": 55, "mode": 0, "inputs": [{"color_on": "", "localized_name": "anything", "name": "SAM2MODEL", "shape": 7, "type": "*", "link": 508}], "outputs": [], "properties": {"cnr_id": "cg-use-everywhere", "ver": "5.0.9", "Node name for S&R": "Anything Everywhere?", "group_restricted": 0, "color_restricted": 0}, "widgets_values": [".*", ".*", "Large model"]}, {"id": 266, "type": "Image Comparer (rgthree)", "pos": [-4137.1494140625, 6098.59130859375], "size": [778.510009765625, 512.8377075195312], "flags": {}, "order": 72, "mode": 0, "inputs": [{"dir": 3, "name": "image_a", "type": "IMAGE", "link": 503}, {"dir": 3, "name": "image_b", "type": "IMAGE", "link": null}], "outputs": [], "title": "Small vs Original", "properties": {"cnr_id": "rgthree-comfy", "ver": "1.0.0", "comparer_mode": "Slide"}, "widgets_values": [[{"name": "A", "selected": true, "url": "/api/view?filename=rgthree.compare._temp_icbnc_00361_.png&type=temp&subfolder=&rand=0.33717343293125723"}, {"name": "B", "selected": true, "url": "/api/view?filename=rgthree.compare._temp_icbnc_00362_.png&type=temp&subfolder=&rand=0.762944695286488"}]]}, {"id": 263, "type": "Image Comparer (rgthree)", "pos": [-4074.411865234375, 2467.81201171875], "size": [778.510009765625, 512.8377075195312], "flags": {}, "order": 64, "mode": 0, "inputs": [{"dir": 3, "name": "image_a", "type": "IMAGE", "link": 502}, {"dir": 3, "name": "image_b", "type": "IMAGE", "link": null}], "outputs": [], "title": "Tiled vs Original", "properties": {"cnr_id": "rgthree-comfy", "ver": "1.0.0", "comparer_mode": "Slide"}, "widgets_values": [[{"name": "A", "selected": true, "url": "/api/view?filename=rgthree.compare._temp_nnkjh_00353_.png&type=temp&subfolder=&rand=0.3917933634010291"}, {"name": "B", "selected": true, "url": "/api/view?filename=rgthree.compare._temp_nnkjh_00354_.png&type=temp&subfolder=&rand=0.744961988954413"}]]}, {"id": 274, "type": "Image Comparer (rgthree)", "pos": [-3187.57666015625, 1324.73095703125], "size": [962.3088989257812, 839.080810546875], "flags": {}, "order": 0, "mode": 0, "inputs": [{"dir": 3, "name": "image_a", "type": "IMAGE", "link": null}, {"dir": 3, "name": "image_b", "type": "IMAGE", "link": null}], "outputs": [], "title": "vs Tiled", "properties": {"cnr_id": "rgthree-comfy", "ver": "1.0.0", "comparer_mode": "Slide"}, "widgets_values": [[{"name": "A", "selected": true, "url": "/api/view?filename=rgthree.compare._temp_bekka_00353_.png&type=temp&subfolder=&rand=0.40047250684181335"}, {"name": "B", "selected": true, "url": "/api/view?filename=rgthree.compare._temp_bekka_00354_.png&type=temp&subfolder=&rand=0.4391069575390045"}]]}, {"id": 290, "type": "Image Comparer (rgthree)", "pos": [-966.8101196289062, 2512.75], "size": [962.3088989257812, 839.080810546875], "flags": {}, "order": 1, "mode": 0, "inputs": [{"dir": 3, "name": "image_a", "type": "IMAGE", "link": null}, {"dir": 3, "name": "image_b", "type": "IMAGE", "link": null}], "outputs": [], "title": "vs Small", "properties": {"cnr_id": "rgthree-comfy", "ver": "1.0.0", "comparer_mode": "Slide"}, "widgets_values": [[{"name": "A", "selected": true, "url": "/api/view?filename=rgthree.compare._temp_dopaq_00353_.png&type=temp&subfolder=&rand=0.012924716679506565"}, {"name": "B", "selected": true, "url": "/api/view?filename=rgthree.compare._temp_dopaq_00354_.png&type=temp&subfolder=&rand=0.95402153435313"}]]}, {"id": 293, "type": "Image Comparer (rgthree)", "pos": [-985.3872680664062, 4924.880859375], "size": [962.3088989257812, 839.080810546875], "flags": {}, "order": 2, "mode": 0, "inputs": [{"dir": 3, "name": "image_a", "type": "IMAGE", "link": null}, {"dir": 3, "name": "image_b", "type": "IMAGE", "link": null}], "outputs": [], "title": "vs Small", "properties": {"cnr_id": "rgthree-comfy", "ver": "1.0.0", "comparer_mode": "Slide"}, "widgets_values": [[{"name": "A", "selected": true, "url": "/api/view?filename=rgthree.compare._temp_udycy_00357_.png&type=temp&subfolder=&rand=0.654459316067713"}, {"name": "B", "selected": true, "url": "/api/view?filename=rgthree.compare._temp_udycy_00358_.png&type=temp&subfolder=&rand=0.3944049361765585"}]]}, {"id": 288, "type": "Image Comparer (rgthree)", "pos": [-3180.006591796875, 2509.189453125], "size": [962.3088989257812, 839.080810546875], "flags": {}, "order": 3, "mode": 0, "inputs": [{"dir": 3, "name": "image_a", "type": "IMAGE", "link": null}, {"dir": 3, "name": "image_b", "type": "IMAGE", "link": null}], "outputs": [], "title": "vs Context", "properties": {"cnr_id": "rgthree-comfy", "ver": "1.0.0", "comparer_mode": "Slide"}, "widgets_values": [[{"name": "A", "selected": true, "url": "/api/view?filename=rgthree.compare._temp_gbnhk_00353_.png&type=temp&subfolder=&rand=0.46363198760672986"}, {"name": "B", "selected": true, "url": "/api/view?filename=rgthree.compare._temp_gbnhk_00354_.png&type=temp&subfolder=&rand=0.7234918819467624"}]]}, {"id": 291, "type": "Image Comparer (rgthree)", "pos": [-3198.583984375, 4921.3203125], "size": [962.3088989257812, 839.080810546875], "flags": {}, "order": 4, "mode": 0, "inputs": [{"dir": 3, "name": "image_a", "type": "IMAGE", "link": null}, {"dir": 3, "name": "image_b", "type": "IMAGE", "link": null}], "outputs": [], "title": "vs Context", "properties": {"cnr_id": "rgthree-comfy", "ver": "1.0.0", "comparer_mode": "Slide"}, "widgets_values": [[{"name": "A", "selected": true, "url": "/api/view?filename=rgthree.compare._temp_jolfu_00359_.png&type=temp&subfolder=&rand=0.04833924595858541"}, {"name": "B", "selected": true, "url": "/api/view?filename=rgthree.compare._temp_jolfu_00360_.png&type=temp&subfolder=&rand=0.5442422039468877"}]]}, {"id": 292, "type": "Image Comparer (rgthree)", "pos": [-2101.966064453125, 4929.47607421875], "size": [962.3088989257812, 839.080810546875], "flags": {}, "order": 5, "mode": 0, "inputs": [{"dir": 3, "name": "image_a", "type": "IMAGE", "link": null}, {"dir": 3, "name": "image_b", "type": "IMAGE", "link": null}], "outputs": [], "title": "vs Tiled", "properties": {"cnr_id": "rgthree-comfy", "ver": "1.0.0", "comparer_mode": "Slide"}, "widgets_values": [[{"name": "A", "selected": true, "url": "/api/view?filename=rgthree.compare._temp_rkpsv_00351_.png&type=temp&subfolder=&rand=0.13945466336351076"}, {"name": "B", "selected": true, "url": "/api/view?filename=rgthree.compare._temp_rkpsv_00352_.png&type=temp&subfolder=&rand=0.6109579696366516"}]]}, {"id": 294, "type": "Image Comparer (rgthree)", "pos": [-3204.143798828125, 6127.8232421875], "size": [962.3088989257812, 839.080810546875], "flags": {}, "order": 6, "mode": 0, "inputs": [{"dir": 3, "name": "image_a", "type": "IMAGE", "link": null}, {"dir": 3, "name": "image_b", "type": "IMAGE", "link": null}], "outputs": [], "title": "vs Context", "properties": {"cnr_id": "rgthree-comfy", "ver": "1.0.0", "comparer_mode": "Slide"}, "widgets_values": [[{"name": "A", "selected": true, "url": "/api/view?filename=rgthree.compare._temp_cqpuk_00359_.png&type=temp&subfolder=&rand=0.48126277266919426"}, {"name": "B", "selected": true, "url": "/api/view?filename=rgthree.compare._temp_cqpuk_00360_.png&type=temp&subfolder=&rand=0.29902891679051824"}]]}, {"id": 295, "type": "Image Comparer (rgthree)", "pos": [-2107.52587890625, 6135.978515625], "size": [962.3088989257812, 839.080810546875], "flags": {}, "order": 7, "mode": 0, "inputs": [{"dir": 3, "name": "image_a", "type": "IMAGE", "link": null}, {"dir": 3, "name": "image_b", "type": "IMAGE", "link": null}], "outputs": [], "title": "vs Tiled", "properties": {"cnr_id": "rgthree-comfy", "ver": "1.0.0", "comparer_mode": "Slide"}, "widgets_values": [[{"name": "A", "selected": true, "url": "/api/view?filename=rgthree.compare._temp_xcvda_00351_.png&type=temp&subfolder=&rand=0.6420885839993347"}, {"name": "B", "selected": true, "url": "/api/view?filename=rgthree.compare._temp_xcvda_00352_.png&type=temp&subfolder=&rand=0.6420314347536185"}]]}, {"id": 301, "type": "Anything Everywhere?", "pos": [-5744.12890625, 2505.1435546875], "size": [315, 106], "flags": {}, "order": 51, "mode": 0, "inputs": [{"color_on": "", "localized_name": "anything", "name": "STRING", "shape": 7, "type": "*", "link": 520}], "outputs": [], "properties": {"cnr_id": "cg-use-everywhere", "ver": "5.0.9", "Node name for S&R": "Anything Everywhere?", "group_restricted": 0, "color_restricted": 0}, "widgets_values": [".*", "mask_color", ".*"], "color": "#322", "bgcolor": "#533"}, {"id": 300, "type": "Anything Everywhere?", "pos": [-6169.82861328125, 2501.84326171875], "size": [315, 106], "flags": {}, "order": 39, "mode": 0, "inputs": [{"color_on": "", "localized_name": "anything", "name": "FLOAT", "shape": 7, "type": "*", "link": 521}], "outputs": [], "properties": {"cnr_id": "cg-use-everywhere", "ver": "5.0.9", "Node name for S&R": "Anything Everywhere?", "group_restricted": 0, "color_restricted": 0}, "widgets_values": [".*", "mask_opacity", ".*"], "color": "#322", "bgcolor": "#533"}, {"id": 260, "type": "PreviewImage", "pos": [-5093.89404296875, 3034.379150390625], "size": [210, 246], "flags": {}, "order": 44, "mode": 0, "inputs": [{"localized_name": "images", "name": "images", "type": "IMAGE", "link": 499}], "outputs": [], "title": "Tiles", "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "PreviewImage"}, "widgets_values": [""]}, {"id": 262, "type": "PreviewImage", "pos": [-4833.4658203125, 3031.84228515625], "size": [210, 258], "flags": {}, "order": 46, "mode": 0, "inputs": [{"localized_name": "images", "name": "images", "type": "IMAGE", "link": 501}], "outputs": [], "title": "Tiles + Masks", "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "PreviewImage"}, "widgets_values": [""]}, {"id": 305, "type": "Anything Everywhere?", "pos": [-6607.98193359375, 760.533447265625], "size": [315, 106], "flags": {}, "order": 54, "mode": 0, "inputs": [{"color_on": "", "localized_name": "anything", "name": "STRING", "shape": 7, "type": "*", "link": 527}], "outputs": [], "properties": {"cnr_id": "cg-use-everywhere", "ver": "5.0.9", "Node name for S&R": "Anything Everywhere?", "group_restricted": 0, "color_restricted": 0}, "widgets_values": ["Text prompt prefix", "text1", ".*"]}, {"id": 261, "type": "PreviewImage", "pos": [-4580.5029296875, 3034.861083984375], "size": [210, 258], "flags": {"collapsed": false}, "order": 45, "mode": 0, "inputs": [{"localized_name": "images", "name": "images", "type": "IMAGE", "link": 500}], "outputs": [], "title": "Used tiles", "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "PreviewImage"}, "widgets_values": [""]}, {"id": 253, "type": "ImageAndMaskPreview", "pos": [-4622.96435546875, 2471.1318359375], "size": [210, 126], "flags": {}, "order": 43, "mode": 0, "inputs": [{"localized_name": "image", "name": "image", "shape": 7, "type": "IMAGE", "link": null}, {"localized_name": "mask", "name": "mask", "shape": 7, "type": "MASK", "link": 498}, {"name": "mask_opacity", "type": "FLOAT", "widget": {"name": "mask_opacity"}, "link": null}, {"name": "mask_color", "type": "STRING", "widget": {"name": "mask_color"}, "link": null}], "outputs": [{"localized_name": "composite", "name": "composite", "shape": 3, "type": "IMAGE", "links": [502, 513, 516, 533]}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "1.0.8", "Node name for S&R": "ImageAndMaskPreview"}, "widgets_values": [0.5000000000000001, "255, 0, 0", true]}, {"id": 267, "type": "ImageAndMaskPreview", "pos": [-4798.4404296875, 4873.89599609375], "size": [210, 126], "flags": {}, "order": 47, "mode": 0, "inputs": [{"localized_name": "image", "name": "image", "shape": 7, "type": "IMAGE", "link": null}, {"localized_name": "mask", "name": "mask", "shape": 7, "type": "MASK", "link": 505}, {"name": "mask_opacity", "type": "FLOAT", "widget": {"name": "mask_opacity"}, "link": null}, {"name": "mask_color", "type": "STRING", "widget": {"name": "mask_color"}, "link": null}], "outputs": [{"localized_name": "composite", "name": "composite", "shape": 3, "type": "IMAGE", "links": [506, 514, 517, 536]}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "1.0.8", "Node name for S&R": "ImageAndMaskPreview"}, "widgets_values": [0.5000000000000001, "255, 0, 0", true]}, {"id": 265, "type": "ImageAndMaskPreview", "pos": [-4813.95751953125, 6095.3828125], "size": [210, 126], "flags": {}, "order": 48, "mode": 0, "inputs": [{"localized_name": "image", "name": "image", "shape": 7, "type": "IMAGE", "link": null}, {"localized_name": "mask", "name": "mask", "shape": 7, "type": "MASK", "link": 504}, {"name": "mask_color", "type": "STRING", "widget": {"name": "mask_color"}, "link": null}, {"name": "mask_opacity", "type": "FLOAT", "widget": {"name": "mask_opacity"}, "link": null}], "outputs": [{"localized_name": "composite", "name": "composite", "shape": 3, "type": "IMAGE", "links": [503, 515, 518, 539]}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "1.0.8", "Node name for S&R": "ImageAndMaskPreview"}, "widgets_values": [0.5000000000000001, "255, 0, 0", true]}, {"id": 318, "type": "CR Text Concatenate", "pos": [-5971.21826171875, 888.4044799804688], "size": [210, 126], "flags": {}, "order": 35, "mode": 0, "inputs": [{"name": "text1", "shape": 7, "type": "STRING", "widget": {"name": "text1"}, "link": null}, {"name": "text2", "shape": 7, "type": "STRING", "widget": {"name": "text2"}, "link": null}, {"name": "text2", "shape": 7, "type": "STRING", "widget": {"name": "text2"}, "link": 540}], "outputs": [{"localized_name": "STRING", "name": "STRING", "type": "*", "links": [541]}, {"localized_name": "show_help", "name": "show_help", "type": "STRING", "links": null}], "title": "Text prompt prefix", "properties": {"cnr_id": "ComfyUI_Comfyroll_CustomNodes", "ver": "d78b780ae43fcf8c6b7c6505e6ffb4584281ceca", "Node name for S&R": "CR Text Concatenate"}, "widgets_values": ["", "", "_"]}, {"id": 319, "type": "Text Prompt (JPS)", "pos": [-5978.05078125, 738.91552734375], "size": [210, 88], "flags": {}, "order": 8, "mode": 0, "inputs": [], "outputs": [{"localized_name": "text", "name": "text", "type": "STRING", "links": [540]}], "properties": {"cnr_id": "ComfyUI_JPS-Nodes", "ver": "0e2a9aca02b17dde91577bfe4b65861df622dcaf", "Node name for S&R": "Text Prompt (JPS)"}, "widgets_values": ["Florence"]}, {"id": 320, "type": "SaveImage", "pos": [-5733.76171875, 744.3521728515625], "size": [210, 282], "flags": {}, "order": 77, "mode": 0, "inputs": [{"localized_name": "images", "name": "images", "type": "IMAGE", "link": 542}, {"name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}, "link": 541}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "SaveImage"}, "widgets_values": ["ComfyUI", ""]}, {"id": 287, "type": "Anything Everywhere?", "pos": [-4599.5078125, 1530.57763671875], "size": [210, 106], "flags": {}, "order": 81, "mode": 0, "inputs": [{"color_on": "#64B5F6", "localized_name": "anything", "name": "IMAGE", "shape": 7, "type": "*", "link": 519}], "outputs": [], "properties": {"cnr_id": "cg-use-everywhere", "ver": "5.0.9", "Node name for S&R": "Anything Everywhere?", "group_restricted": 0, "color_restricted": 0}, "widgets_values": ["vs Context", "image_b", ".*"]}, {"id": 311, "type": "SaveImage", "pos": [-3947.16015625, 3113.30322265625], "size": [210, 270], "flags": {}, "order": 67, "mode": 0, "inputs": [{"localized_name": "images", "name": "images", "type": "IMAGE", "link": 533}, {"name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}, "link": 532}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "SaveImage"}, "widgets_values": ["ComfyUI", ""]}, {"id": 280, "type": "Anything Everywhere?", "pos": [-4609.21826171875, 2673.053466796875], "size": [232.76426696777344, 106], "flags": {}, "order": 65, "mode": 0, "inputs": [{"color_on": "#64B5F6", "localized_name": "anything", "name": "IMAGE", "shape": 7, "type": "*", "link": 513}], "outputs": [], "properties": {"cnr_id": "cg-use-everywhere", "ver": "5.0.9", "Node name for S&R": "Anything Everywhere?", "group_restricted": 0, "color_restricted": 0}, "widgets_values": [".*", "image_a", "Tiled comparisons"]}, {"id": 284, "type": "Anything Everywhere?", "pos": [-4601.62841796875, 2841.37109375], "size": [210, 106], "flags": {}, "order": 66, "mode": 0, "inputs": [{"color_on": "#64B5F6", "localized_name": "anything", "name": "IMAGE", "shape": 7, "type": "*", "link": 516}], "outputs": [], "properties": {"cnr_id": "cg-use-everywhere", "ver": "5.0.9", "Node name for S&R": "Anything Everywhere?", "group_restricted": 0, "color_restricted": 0}, "widgets_values": ["vs Tiled", "image_b", ".*"]}, {"id": 309, "type": "CR Text Concatenate", "pos": [-4286.314453125, 3246.2763671875], "size": [210, 126], "flags": {}, "order": 36, "mode": 0, "inputs": [{"name": "text1", "shape": 7, "type": "STRING", "widget": {"name": "text1"}, "link": null}, {"name": "text2", "shape": 7, "type": "STRING", "widget": {"name": "text2"}, "link": null}, {"name": "text2", "shape": 7, "type": "STRING", "widget": {"name": "text2"}, "link": 531}], "outputs": [{"localized_name": "STRING", "name": "STRING", "type": "*", "links": [532]}, {"localized_name": "show_help", "name": "show_help", "type": "STRING", "links": null}], "title": "Text prompt prefix", "properties": {"cnr_id": "ComfyUI_Comfyroll_CustomNodes", "ver": "d78b780ae43fcf8c6b7c6505e6ffb4584281ceca", "Node name for S&R": "CR Text Concatenate"}, "widgets_values": ["", "", "_"]}, {"id": 310, "type": "Text Prompt (JPS)", "pos": [-4287.05126953125, 3089.678955078125], "size": [210, 88], "flags": {}, "order": 9, "mode": 0, "inputs": [], "outputs": [{"localized_name": "text", "name": "text", "type": "STRING", "links": [531]}], "properties": {"cnr_id": "ComfyUI_JPS-Nodes", "ver": "0e2a9aca02b17dde91577bfe4b65861df622dcaf", "Node name for S&R": "Text Prompt (JPS)"}, "widgets_values": ["Tiled"]}, {"id": 314, "type": "SaveImage", "pos": [-4228.287109375, 5466.50146484375], "size": [210, 270], "flags": {}, "order": 71, "mode": 0, "inputs": [{"localized_name": "images", "name": "images", "type": "IMAGE", "link": 536}, {"name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}, "link": 535}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "SaveImage"}, "widgets_values": ["ComfyUI", ""]}, {"id": 312, "type": "CR Text Concatenate", "pos": [-4497.3505859375, 5585.267578125], "size": [210, 126], "flags": {}, "order": 40, "mode": 0, "inputs": [{"name": "text1", "shape": 7, "type": "STRING", "widget": {"name": "text1"}, "link": null}, {"name": "text2", "shape": 7, "type": "STRING", "widget": {"name": "text2"}, "link": null}, {"name": "text2", "shape": 7, "type": "STRING", "widget": {"name": "text2"}, "link": 534}], "outputs": [{"localized_name": "STRING", "name": "STRING", "type": "*", "links": [535]}, {"localized_name": "show_help", "name": "show_help", "type": "STRING", "links": null}], "title": "Text prompt prefix", "properties": {"cnr_id": "ComfyUI_Comfyroll_CustomNodes", "ver": "d78b780ae43fcf8c6b7c6505e6ffb4584281ceca", "Node name for S&R": "CR Text Concatenate"}, "widgets_values": ["", "", "_"]}, {"id": 282, "type": "Anything Everywhere?", "pos": [-4828.6953125, 6338.40869140625], "size": [249.00836181640625, 106], "flags": {}, "order": 73, "mode": 0, "inputs": [{"color_on": "#64B5F6", "localized_name": "anything", "name": "IMAGE", "shape": 7, "type": "*", "link": 515}], "outputs": [], "properties": {"cnr_id": "cg-use-everywhere", "ver": "5.0.9", "Node name for S&R": "Anything Everywhere?", "group_restricted": 0, "color_restricted": 0}, "widgets_values": [".*", "image_a", "Small comparisons"]}, {"id": 286, "type": "Anything Everywhere?", "pos": [-4808.4443359375, 6523.55322265625], "size": [210, 106], "flags": {}, "order": 74, "mode": 0, "inputs": [{"color_on": "#64B5F6", "localized_name": "anything", "name": "IMAGE", "shape": 7, "type": "*", "link": 518}], "outputs": [], "properties": {"cnr_id": "cg-use-everywhere", "ver": "5.0.9", "Node name for S&R": "Anything Everywhere?", "group_restricted": 0, "color_restricted": 0}, "widgets_values": ["vs Small", "image_b", ".*"]}, {"id": 317, "type": "SaveImage", "pos": [-4211.482421875, 6733.86669921875], "size": [210, 270], "flags": {}, "order": 75, "mode": 0, "inputs": [{"localized_name": "images", "name": "images", "type": "IMAGE", "link": 539}, {"name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}, "link": 538}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "SaveImage"}, "widgets_values": ["ComfyUI", ""]}, {"id": 315, "type": "CR Text Concatenate", "pos": [-4483.3671875, 6890.6708984375], "size": [210, 126], "flags": {}, "order": 37, "mode": 0, "inputs": [{"name": "text1", "shape": 7, "type": "STRING", "widget": {"name": "text1"}, "link": null}, {"name": "text2", "shape": 7, "type": "STRING", "widget": {"name": "text2"}, "link": null}, {"name": "text2", "shape": 7, "type": "STRING", "widget": {"name": "text2"}, "link": 537}], "outputs": [{"localized_name": "STRING", "name": "STRING", "type": "*", "links": [538]}, {"localized_name": "show_help", "name": "show_help", "type": "STRING", "links": null}], "title": "Text prompt prefix", "properties": {"cnr_id": "ComfyUI_Comfyroll_CustomNodes", "ver": "d78b780ae43fcf8c6b7c6505e6ffb4584281ceca", "Node name for S&R": "CR Text Concatenate"}, "widgets_values": ["", "", "_"]}, {"id": 316, "type": "Text Prompt (JPS)", "pos": [-4482.07763671875, 6714.7841796875], "size": [210, 88], "flags": {}, "order": 10, "mode": 0, "inputs": [], "outputs": [{"localized_name": "text", "name": "text", "type": "STRING", "links": [537]}], "properties": {"cnr_id": "ComfyUI_JPS-Nodes", "ver": "0e2a9aca02b17dde91577bfe4b65861df622dcaf", "Node name for S&R": "Text Prompt (JPS)"}, "widgets_values": ["Small"]}, {"id": 278, "type": "Anything Everywhere?", "pos": [-4613.9140625, 1694.3935546875], "size": [253.57701110839844, 106], "flags": {}, "order": 80, "mode": 0, "inputs": [{"color_on": "#64B5F6", "localized_name": "anything", "name": "IMAGE", "shape": 7, "type": "*", "link": 510}], "outputs": [], "properties": {"cnr_id": "cg-use-everywhere", "ver": "5.0.9", "Node name for S&R": "Anything Everywhere?", "group_restricted": 0, "color_restricted": 0}, "widgets_values": [".*", "image_a", "Context comparisons"]}, {"id": 90, "type": "PreviewImage", "pos": [-5923.8046875, 1137.541748046875], "size": [568.406494140625, 384.9489440917969], "flags": {}, "order": 76, "mode": 0, "inputs": [{"localized_name": "images", "name": "images", "type": "IMAGE", "link": 200}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "PreviewImage"}, "widgets_values": [""], "color": "#232", "bgcolor": "#353"}, {"id": 304, "type": "SaveImage", "pos": [-3884.79296875, 1967.5067138671875], "size": [210, 270], "flags": {}, "order": 82, "mode": 0, "inputs": [{"localized_name": "images", "name": "images", "type": "IMAGE", "link": 528}, {"name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}, "link": 529}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "SaveImage"}, "widgets_values": ["ComfyUI", ""]}, {"id": 229, "type": "PreviewImage", "pos": [-5133.77880859375, 1991.5067138671875], "size": [210, 246], "flags": {}, "order": 59, "mode": 0, "inputs": [{"localized_name": "images", "name": "images", "type": "IMAGE", "link": 548}], "outputs": [], "title": "Colored masks", "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "PreviewImage"}, "widgets_values": [""]}, {"id": 308, "type": "Text Prompt (JPS)", "pos": [-4128.85888671875, 1956.432861328125], "size": [210, 88], "flags": {}, "order": 11, "mode": 0, "inputs": [], "outputs": [{"localized_name": "text", "name": "text", "type": "STRING", "links": [530]}], "properties": {"cnr_id": "ComfyUI_JPS-Nodes", "ver": "0e2a9aca02b17dde91577bfe4b65861df622dcaf", "Node name for S&R": "Text Prompt (JPS)"}, "widgets_values": ["Context"]}, {"id": 307, "type": "CR Text Concatenate", "pos": [-4121.52099609375, 2104.585205078125], "size": [210, 126], "flags": {}, "order": 38, "mode": 0, "inputs": [{"name": "text1", "shape": 7, "type": "STRING", "widget": {"name": "text1"}, "link": null}, {"name": "text2", "shape": 7, "type": "STRING", "widget": {"name": "text2"}, "link": null}, {"name": "text2", "shape": 7, "type": "STRING", "widget": {"name": "text2"}, "link": 530}], "outputs": [{"localized_name": "STRING", "name": "STRING", "type": "*", "links": [529]}, {"localized_name": "show_help", "name": "show_help", "type": "STRING", "links": null}], "title": "Text prompt prefix", "properties": {"cnr_id": "ComfyUI_Comfyroll_CustomNodes", "ver": "d78b780ae43fcf8c6b7c6505e6ffb4584281ceca", "Node name for S&R": "CR Text Concatenate"}, "widgets_values": ["", "", "_"]}, {"id": 238, "type": "ImageAndMaskPreview", "pos": [-4516.54296875, 1887.5067138671875], "size": [315, 350], "flags": {}, "order": 58, "mode": 0, "inputs": [{"localized_name": "image", "name": "image", "shape": 7, "type": "IMAGE", "link": 547}, {"localized_name": "mask", "name": "mask", "shape": 7, "type": "MASK", "link": 553}, {"name": "mask_opacity", "type": "FLOAT", "widget": {"name": "mask_opacity"}, "link": null}, {"name": "mask_color", "type": "STRING", "widget": {"name": "mask_color"}, "link": null}], "outputs": [{"localized_name": "composite", "name": "composite", "type": "IMAGE", "links": null}], "title": "Context tiles", "properties": {"cnr_id": "comfyui-kjnodes", "ver": "1.0.8", "Node name for S&R": "ImageAndMaskPreview"}, "widgets_values": [0.5000000000000001, "255, 0, 255", false, ""]}, {"id": 297, "type": "Primitive float [Crystools]", "pos": [-6170.92919921875, 2352.243408203125], "size": [315, 58], "flags": {}, "order": 12, "mode": 0, "inputs": [], "outputs": [{"localized_name": "float", "name": "float", "type": "FLOAT", "links": [521]}], "title": "Mask opacity", "properties": {"cnr_id": "ComfyUI-Crystools", "ver": "0820a7560bcc405ef6d0a7c5c53a83cc02ae7db2", "Node name for S&R": "Primitive float [Crystools]"}, "widgets_values": [0.5000000000000001], "color": "#322", "bgcolor": "#533"}, {"id": 327, "type": "MaskPreview+", "pos": [-4863.01171875, 1993.843505859375], "size": [210, 246], "flags": {}, "order": 57, "mode": 0, "inputs": [{"localized_name": "mask", "name": "mask", "type": "MASK", "link": 550}], "outputs": [], "title": "Cleaned mask", "properties": {"cnr_id": "comfyui_essentials", "ver": "9d9f4bedfc9f0321c19faf71855e228c93bd0dc9", "Node name for S&R": "MaskPreview+"}, "widgets_values": [""]}, {"id": 224, "type": "Image Comparer (rgthree)", "pos": [-4078.91259765625, 1295.529296875], "size": [778.510009765625, 512.8377075195312], "flags": {}, "order": 79, "mode": 0, "inputs": [{"dir": 3, "name": "image_a", "type": "IMAGE", "link": 465}, {"dir": 3, "name": "image_b", "type": "IMAGE", "link": null}], "outputs": [], "title": "Context vs Original", "properties": {"cnr_id": "rgthree-comfy", "ver": "1.0.0", "comparer_mode": "Slide"}, "widgets_values": [[{"name": "A", "selected": true, "url": "/api/view?filename=rgthree.compare._temp_sqsfm_00361_.png&type=temp&subfolder=&rand=0.9632070391413623"}, {"name": "B", "selected": true, "url": "/api/view?filename=rgthree.compare._temp_sqsfm_00362_.png&type=temp&subfolder=&rand=0.02556614408604263"}]]}, {"id": 233, "type": "ImageAndMaskPreview", "pos": [-4607.16455078125, 1307.0479736328125], "size": [210, 126], "flags": {}, "order": 56, "mode": 0, "inputs": [{"localized_name": "image", "name": "image", "shape": 7, "type": "IMAGE", "link": null}, {"localized_name": "mask", "name": "mask", "shape": 7, "type": "MASK", "link": 545}, {"name": "mask_color", "type": "STRING", "widget": {"name": "mask_color"}, "link": null}, {"name": "mask_opacity", "type": "FLOAT", "widget": {"name": "mask_opacity"}, "link": null}], "outputs": [{"localized_name": "composite", "name": "composite", "shape": 3, "type": "IMAGE", "links": [431, 465, 510, 519, 528]}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "1.0.8", "Node name for S&R": "ImageAndMaskPreview"}, "widgets_values": [0.7000000000000002, "255, 0, 0", true]}, {"id": 273, "type": "Anything Everywhere?", "pos": [-5739.4287109375, 2092.283447265625], "size": [315, 106], "flags": {}, "order": 50, "mode": 0, "inputs": [{"color_on": "", "localized_name": "anything", "name": "SAM2MODEL", "shape": 7, "type": "*", "link": 509}], "outputs": [], "properties": {"cnr_id": "cg-use-everywhere", "ver": "5.0.9", "Node name for S&R": "Anything Everywhere?", "group_restricted": 0, "color_restricted": 0}, "widgets_values": [".*", ".*", "Small model"]}, {"id": 336, "type": "Anything Everywhere?", "pos": [-5710.3134765625, 1870.9320068359375], "size": [315, 106], "flags": {}, "order": 49, "mode": 0, "inputs": [{"color_on": "", "localized_name": "anything", "name": "SAM2MODEL", "shape": 7, "type": "*", "link": 560}], "outputs": [], "properties": {"cnr_id": "cg-use-everywhere", "ver": "5.0.9", "Node name for S&R": "Anything Everywhere?", "group_restricted": 0, "color_restricted": 0}, "widgets_values": [".*", ".*", "Base model"]}, {"id": 289, "type": "Image Comparer (rgthree)", "pos": [-2083.388671875, 2517.344970703125], "size": [962.3088989257812, 839.080810546875], "flags": {}, "order": 13, "mode": 0, "inputs": [{"dir": 3, "name": "image_a", "type": "IMAGE", "link": null}, {"dir": 3, "name": "image_b", "type": "IMAGE", "link": null}], "outputs": [], "title": "vs Base", "properties": {"cnr_id": "rgthree-comfy", "ver": "1.0.0", "comparer_mode": "Slide"}, "widgets_values": [[{"name": "A", "selected": true, "url": "/api/view?filename=rgthree.compare._temp_pelgv_00351_.png&type=temp&subfolder=&rand=0.8019104114267226"}, {"name": "B", "selected": true, "url": "/api/view?filename=rgthree.compare._temp_pelgv_00352_.png&type=temp&subfolder=&rand=0.029803330251277904"}]]}, {"id": 296, "type": "Image Comparer (rgthree)", "pos": [-990.9471435546875, 6131.3837890625], "size": [962.3088989257812, 839.080810546875], "flags": {}, "order": 14, "mode": 0, "inputs": [{"dir": 3, "name": "image_a", "type": "IMAGE", "link": null}, {"dir": 3, "name": "image_b", "type": "IMAGE", "link": null}], "outputs": [], "title": "vs Base", "properties": {"cnr_id": "rgthree-comfy", "ver": "1.0.0", "comparer_mode": "Slide"}, "widgets_values": [[{"name": "A", "selected": true, "url": "/api/view?filename=rgthree.compare._temp_lcyis_00357_.png&type=temp&subfolder=&rand=0.033208106119227754"}, {"name": "B", "selected": true, "url": "/api/view?filename=rgthree.compare._temp_lcyis_00358_.png&type=temp&subfolder=&rand=0.11285751799164401"}]]}, {"id": 268, "type": "Image Comparer (rgthree)", "pos": [-4124.29541015625, 4867.787109375], "size": [778.510009765625, 512.8377075195312], "flags": {}, "order": 68, "mode": 0, "inputs": [{"dir": 3, "name": "image_a", "type": "IMAGE", "link": 506}, {"dir": 3, "name": "image_b", "type": "IMAGE", "link": null}], "outputs": [], "title": "Base vs Original", "properties": {"cnr_id": "rgthree-comfy", "ver": "1.0.0", "comparer_mode": "Slide"}, "widgets_values": [[{"name": "A", "selected": true, "url": "/api/view?filename=rgthree.compare._temp_zxeyk_00359_.png&type=temp&subfolder=&rand=0.6808438831585484"}, {"name": "B", "selected": true, "url": "/api/view?filename=rgthree.compare._temp_zxeyk_00360_.png&type=temp&subfolder=&rand=0.43000376661139095"}]]}, {"id": 313, "type": "Text Prompt (JPS)", "pos": [-4507.4560546875, 5426.11669921875], "size": [210, 88], "flags": {}, "order": 15, "mode": 0, "inputs": [], "outputs": [{"localized_name": "text", "name": "text", "type": "STRING", "links": [534]}], "properties": {"cnr_id": "ComfyUI_JPS-Nodes", "ver": "0e2a9aca02b17dde91577bfe4b65861df622dcaf", "Node name for S&R": "Text Prompt (JPS)"}, "widgets_values": ["Base"]}, {"id": 285, "type": "Anything Everywhere?", "pos": [-4831.67724609375, 5259.81494140625], "size": [210, 106], "flags": {}, "order": 70, "mode": 0, "inputs": [{"color_on": "#64B5F6", "localized_name": "anything", "name": "IMAGE", "shape": 7, "type": "*", "link": 517}], "outputs": [], "properties": {"cnr_id": "cg-use-everywhere", "ver": "5.0.9", "Node name for S&R": "Anything Everywhere?", "group_restricted": 0, "color_restricted": 0}, "widgets_values": ["vs Base", "image_b", ".*"]}, {"id": 281, "type": "Anything Everywhere?", "pos": [-4819.34228515625, 5093.787109375], "size": [265.9476013183594, 107.63507843017578], "flags": {}, "order": 69, "mode": 0, "inputs": [{"color_on": "#64B5F6", "localized_name": "anything", "name": "IMAGE", "shape": 7, "type": "*", "link": 514}], "outputs": [], "properties": {"cnr_id": "cg-use-everywhere", "ver": "5.0.9", "Node name for S&R": "Anything Everywhere?", "group_restricted": 0, "color_restricted": 0}, "widgets_values": [".*", "image_a", "Base comparisons"]}, {"id": 337, "type": "Image Comparer (rgthree)", "pos": [-959.3278198242188, 3711.781005859375], "size": [962.3088989257812, 839.080810546875], "flags": {}, "order": 16, "mode": 0, "inputs": [{"dir": 3, "name": "image_a", "type": "IMAGE", "link": null}, {"dir": 3, "name": "image_b", "type": "IMAGE", "link": null}], "outputs": [], "title": "vs Small", "properties": {"cnr_id": "rgthree-comfy", "ver": "1.0.0", "comparer_mode": "Slide"}, "widgets_values": [[{"name": "A", "selected": true, "url": "/api/view?filename=rgthree.compare._temp_vagbn_00207_.png&type=temp&subfolder=&rand=0.22411051141138927"}, {"name": "B", "selected": true, "url": "/api/view?filename=rgthree.compare._temp_vagbn_00208_.png&type=temp&subfolder=&rand=0.35804928229542865"}]]}, {"id": 338, "type": "Image Comparer (rgthree)", "pos": [-3172.524169921875, 3708.220458984375], "size": [962.3088989257812, 839.080810546875], "flags": {}, "order": 17, "mode": 0, "inputs": [{"dir": 3, "name": "image_a", "type": "IMAGE", "link": null}, {"dir": 3, "name": "image_b", "type": "IMAGE", "link": null}], "outputs": [], "title": "vs Context", "properties": {"cnr_id": "rgthree-comfy", "ver": "1.0.0", "comparer_mode": "Slide"}, "widgets_values": [[{"name": "A", "selected": true, "url": "/api/view?filename=rgthree.compare._temp_balpz_00205_.png&type=temp&subfolder=&rand=0.6842314455133811"}, {"name": "B", "selected": true, "url": "/api/view?filename=rgthree.compare._temp_balpz_00206_.png&type=temp&subfolder=&rand=0.41673463428762525"}]]}, {"id": 339, "type": "Image Comparer (rgthree)", "pos": [-2075.90625, 3716.376220703125], "size": [962.3088989257812, 839.080810546875], "flags": {}, "order": 18, "mode": 0, "inputs": [{"dir": 3, "name": "image_a", "type": "IMAGE", "link": null}, {"dir": 3, "name": "image_b", "type": "IMAGE", "link": null}], "outputs": [], "title": "vs Tiled", "properties": {"cnr_id": "rgthree-comfy", "ver": "1.0.0", "comparer_mode": "Slide"}, "widgets_values": [[{"name": "A", "selected": true, "url": "/api/view?filename=rgthree.compare._temp_kbeya_00199_.png&type=temp&subfolder=&rand=0.9839825947763612"}, {"name": "B", "selected": true, "url": "/api/view?filename=rgthree.compare._temp_kbeya_00200_.png&type=temp&subfolder=&rand=0.42365193923924016"}]]}, {"id": 340, "type": "ImageAndMaskPreview", "pos": [-4772.380859375, 3660.796142578125], "size": [210, 126], "flags": {}, "order": 42, "mode": 0, "inputs": [{"localized_name": "image", "name": "image", "shape": 7, "type": "IMAGE", "link": null}, {"localized_name": "mask", "name": "mask", "shape": 7, "type": "MASK", "link": 561}, {"name": "mask_opacity", "type": "FLOAT", "widget": {"name": "mask_opacity"}, "link": null}, {"name": "mask_color", "type": "STRING", "widget": {"name": "mask_color"}, "link": null}], "outputs": [{"localized_name": "composite", "name": "composite", "shape": 3, "type": "IMAGE", "links": [562, 565, 566, 567]}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "1.0.8", "Node name for S&R": "ImageAndMaskPreview"}, "widgets_values": [0.5000000000000001, "255, 0, 0", true]}, {"id": 341, "type": "SaveImage", "pos": [-4202.2275390625, 4253.4013671875], "size": [210, 270], "flags": {}, "order": 60, "mode": 0, "inputs": [{"localized_name": "images", "name": "images", "type": "IMAGE", "link": 562}, {"name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}, "link": 563}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "SaveImage"}, "widgets_values": ["ComfyUI", ""]}, {"id": 342, "type": "CR Text Concatenate", "pos": [-4471.291015625, 4372.16748046875], "size": [210, 126], "flags": {}, "order": 41, "mode": 0, "inputs": [{"name": "text1", "shape": 7, "type": "STRING", "widget": {"name": "text1"}, "link": null}, {"name": "text2", "shape": 7, "type": "STRING", "widget": {"name": "text2"}, "link": null}, {"name": "text2", "shape": 7, "type": "STRING", "widget": {"name": "text2"}, "link": 564}], "outputs": [{"localized_name": "STRING", "name": "STRING", "type": "*", "links": [563]}, {"localized_name": "show_help", "name": "show_help", "type": "STRING", "links": null}], "title": "Text prompt prefix", "properties": {"cnr_id": "ComfyUI_Comfyroll_CustomNodes", "ver": "d78b780ae43fcf8c6b7c6505e6ffb4584281ceca", "Node name for S&R": "CR Text Concatenate"}, "widgets_values": ["", "", "_"]}, {"id": 343, "type": "Image Comparer (rgthree)", "pos": [-4098.23583984375, 3654.687255859375], "size": [778.510009765625, 512.8377075195312], "flags": {}, "order": 61, "mode": 0, "inputs": [{"dir": 3, "name": "image_a", "type": "IMAGE", "link": 565}, {"dir": 3, "name": "image_b", "type": "IMAGE", "link": null}], "outputs": [], "title": "Large vs Original", "properties": {"cnr_id": "rgthree-comfy", "ver": "1.0.0", "comparer_mode": "Slide"}, "widgets_values": [[{"name": "A", "selected": true, "url": "/api/view?filename=rgthree.compare._temp_hkxcv_00207_.png&type=temp&subfolder=&rand=0.08826835110570652"}, {"name": "B", "selected": true, "url": "/api/view?filename=rgthree.compare._temp_hkxcv_00208_.png&type=temp&subfolder=&rand=0.786119026456501"}]]}, {"id": 345, "type": "Anything Everywhere?", "pos": [-4805.61767578125, 4046.715087890625], "size": [210, 106], "flags": {}, "order": 62, "mode": 0, "inputs": [{"color_on": "#64B5F6", "localized_name": "anything", "name": "IMAGE", "shape": 7, "type": "*", "link": 566}], "outputs": [], "properties": {"cnr_id": "cg-use-everywhere", "ver": "5.0.9", "Node name for S&R": "Anything Everywhere?", "group_restricted": 0, "color_restricted": 0}, "widgets_values": ["vs Large", "image_b", ".*"]}, {"id": 346, "type": "Anything Everywhere?", "pos": [-4793.28271484375, 3880.687255859375], "size": [265.9476013183594, 107.63507843017578], "flags": {}, "order": 63, "mode": 0, "inputs": [{"color_on": "#64B5F6", "localized_name": "anything", "name": "IMAGE", "shape": 7, "type": "*", "link": 567}], "outputs": [], "properties": {"cnr_id": "cg-use-everywhere", "ver": "5.0.9", "Node name for S&R": "Anything Everywhere?", "group_restricted": 0, "color_restricted": 0}, "widgets_values": [".*", "image_a", "Large comparisons"]}, {"id": 344, "type": "Text Prompt (JPS)", "pos": [-4481.396484375, 4213.0166015625], "size": [210, 88], "flags": {}, "order": 19, "mode": 0, "inputs": [], "outputs": [{"localized_name": "text", "name": "text", "type": "STRING", "links": [564]}], "properties": {"cnr_id": "ComfyUI_JPS-Nodes", "ver": "0e2a9aca02b17dde91577bfe4b65861df622dcaf", "Node name for S&R": "Text Prompt (JPS)"}, "widgets_values": ["Large"]}, {"id": 277, "type": "Image Comparer (rgthree)", "pos": [161.72183227539062, 1349.72705078125], "size": [962.3088989257812, 839.080810546875], "flags": {}, "order": 20, "mode": 0, "inputs": [{"dir": 3, "name": "image_a", "type": "IMAGE", "link": null}, {"dir": 3, "name": "image_b", "type": "IMAGE", "link": null}], "outputs": [], "title": "vs Small", "properties": {"cnr_id": "rgthree-comfy", "ver": "1.0.0", "comparer_mode": "Slide"}, "widgets_values": [[{"name": "A", "selected": true, "url": "/api/view?filename=rgthree.compare._temp_fdvex_00361_.png&type=temp&subfolder=&rand=0.9209450990016244"}, {"name": "B", "selected": true, "url": "/api/view?filename=rgthree.compare._temp_fdvex_00362_.png&type=temp&subfolder=&rand=0.030876684686087508"}]]}, {"id": 276, "type": "Image Comparer (rgthree)", "pos": [-954.856689453125, 1354.322021484375], "size": [962.3088989257812, 839.080810546875], "flags": {}, "order": 21, "mode": 0, "inputs": [{"dir": 3, "name": "image_a", "type": "IMAGE", "link": null}, {"dir": 3, "name": "image_b", "type": "IMAGE", "link": null}], "outputs": [], "title": "vs Base", "properties": {"cnr_id": "rgthree-comfy", "ver": "1.0.0", "comparer_mode": "Slide"}, "widgets_values": [[{"name": "A", "selected": true, "url": "/api/view?filename=rgthree.compare._temp_afokn_00357_.png&type=temp&subfolder=&rand=0.6188252762993851"}, {"name": "B", "selected": true, "url": "/api/view?filename=rgthree.compare._temp_afokn_00358_.png&type=temp&subfolder=&rand=0.13005124948743219"}]]}, {"id": 348, "type": "Image Comparer (rgthree)", "pos": [-2027.43701171875, 1324.307861328125], "size": [962.3088989257812, 839.080810546875], "flags": {}, "order": 22, "mode": 0, "inputs": [{"dir": 3, "name": "image_a", "type": "IMAGE", "link": null}, {"dir": 3, "name": "image_b", "type": "IMAGE", "link": null}], "outputs": [], "title": "vs Large", "properties": {"cnr_id": "rgthree-comfy", "ver": "1.0.0", "comparer_mode": "Slide"}, "widgets_values": [[{"name": "A", "selected": true, "url": "/api/view?filename=rgthree.compare._temp_syrcr_00205_.png&type=temp&subfolder=&rand=0.44075011192172386"}, {"name": "B", "selected": true, "url": "/api/view?filename=rgthree.compare._temp_syrcr_00206_.png&type=temp&subfolder=&rand=0.025259805921423073"}]]}, {"id": 347, "type": "Sam2Segmentation", "pos": [-5157.25927734375, 3661.133056640625], "size": [315, 190], "flags": {}, "order": 23, "mode": 0, "inputs": [{"localized_name": "sam2_model", "name": "sam2_model", "type": "SAM2MODEL", "link": null}, {"localized_name": "image", "name": "image", "type": "IMAGE", "link": null}, {"localized_name": "bboxes", "name": "bboxes", "shape": 7, "type": "BBOX", "link": null}, {"localized_name": "mask", "name": "mask", "shape": 7, "type": "MASK", "link": null}, {"name": "coordinates_positive", "shape": 7, "type": "STRING", "widget": {"name": "coordinates_positive"}, "link": null}, {"name": "coordinates_negative", "shape": 7, "type": "STRING", "widget": {"name": "coordinates_negative"}, "link": null}], "outputs": [{"localized_name": "mask", "name": "mask", "type": "MASK", "links": [561]}], "properties": {"cnr_id": "ComfyUI-segment-anything-2", "ver": "c59676b008a76237002926f684d0ca3a9b29ac54", "Node name for S&R": "Sam2Segmentation"}, "widgets_values": [true, "", "", true]}, {"id": 259, "type": "Sam2TiledSegmentation", "pos": [-5090.51025390625, 2456.277587890625], "size": [330, 306], "flags": {}, "order": 24, "mode": 0, "inputs": [{"localized_name": "sam2_model", "name": "sam2_model", "type": "SAM2MODEL", "link": null}, {"localized_name": "image", "name": "image", "type": "IMAGE", "link": null}, {"localized_name": "bboxes", "name": "bboxes", "shape": 7, "type": "BBOX", "link": null}, {"localized_name": "mask", "name": "mask", "shape": 7, "type": "MASK", "link": null}, {"name": "coordinates_positive", "shape": 7, "type": "STRING", "widget": {"name": "coordinates_positive"}, "link": null}, {"name": "coordinates_negative", "shape": 7, "type": "STRING", "widget": {"name": "coordinates_negative"}, "link": null}, {"name": "mask_opacity", "type": "FLOAT", "widget": {"name": "mask_opacity"}, "link": null}, {"name": "mask_color", "type": "STRING", "widget": {"name": "mask_color"}, "link": null}], "outputs": [{"localized_name": "mask", "name": "mask", "type": "MASK", "links": [498]}, {"localized_name": "tiles", "name": "tiles", "type": "IMAGE", "links": [499]}, {"localized_name": "tile_bboxes", "name": "tile_bboxes", "type": "BBOX", "links": null}, {"localized_name": "annotated_image", "name": "annotated_image", "type": "IMAGE", "links": [500]}, {"localized_name": "masked_tiles", "name": "masked_tiles", "type": "IMAGE", "links": [501]}], "properties": {"cnr_id": "computer-vision", "ver": "e4601b9e36e88fa4d6a23d722ea58ca883367f9b", "Node name for S&R": "Sam2TiledSegmentation", "aux_id": "MicheleGuidi/comfyui-computer-vision"}, "widgets_values": [512, 0.25, true, 0.8, "255,0,0", "", "", true]}, {"id": 269, "type": "Sam2Segmentation", "pos": [-5183.31884765625, 4874.23291015625], "size": [315, 190], "flags": {}, "order": 25, "mode": 0, "inputs": [{"localized_name": "sam2_model", "name": "sam2_model", "type": "SAM2MODEL", "link": null}, {"localized_name": "image", "name": "image", "type": "IMAGE", "link": null}, {"localized_name": "bboxes", "name": "bboxes", "shape": 7, "type": "BBOX", "link": null}, {"localized_name": "mask", "name": "mask", "shape": 7, "type": "MASK", "link": null}, {"name": "coordinates_positive", "shape": 7, "type": "STRING", "widget": {"name": "coordinates_positive"}, "link": null}, {"name": "coordinates_negative", "shape": 7, "type": "STRING", "widget": {"name": "coordinates_negative"}, "link": null}], "outputs": [{"localized_name": "mask", "name": "mask", "type": "MASK", "links": [505]}], "properties": {"cnr_id": "ComfyUI-segment-anything-2", "ver": "c59676b008a76237002926f684d0ca3a9b29ac54", "Node name for S&R": "Sam2Segmentation"}, "widgets_values": [true, "", "", true]}, {"id": 264, "type": "Sam2Segmentation", "pos": [-5198.8359375, 6095.7197265625], "size": [315, 190], "flags": {}, "order": 26, "mode": 0, "inputs": [{"localized_name": "sam2_model", "name": "sam2_model", "type": "SAM2MODEL", "link": null}, {"localized_name": "image", "name": "image", "type": "IMAGE", "link": null}, {"localized_name": "bboxes", "name": "bboxes", "shape": 7, "type": "BBOX", "link": null}, {"localized_name": "mask", "name": "mask", "shape": 7, "type": "MASK", "link": null}, {"name": "coordinates_positive", "shape": 7, "type": "STRING", "widget": {"name": "coordinates_positive"}, "link": null}, {"name": "coordinates_negative", "shape": 7, "type": "STRING", "widget": {"name": "coordinates_negative"}, "link": null}], "outputs": [{"localized_name": "mask", "name": "mask", "type": "MASK", "links": [504]}], "properties": {"cnr_id": "ComfyUI-segment-anything-2", "ver": "c59676b008a76237002926f684d0ca3a9b29ac54", "Node name for S&R": "Sam2Segmentation"}, "widgets_values": [true, "", "", true]}, {"id": 87, "type": "Florence2Run", "pos": [-6584.29052734375, 1139.2342529296875], "size": [400, 364], "flags": {}, "order": 53, "mode": 0, "inputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "link": 551}, {"localized_name": "florence2_model", "name": "florence2_model", "type": "FL2MODEL", "link": 197}, {"name": "text_input", "type": "STRING", "widget": {"name": "text_input"}, "link": 526}], "outputs": [{"localized_name": "image", "name": "image", "shape": 3, "type": "IMAGE", "slot_index": 0, "links": [200, 542]}, {"localized_name": "mask", "name": "mask", "shape": 3, "type": "MASK", "links": []}, {"localized_name": "caption", "name": "caption", "shape": 3, "type": "STRING", "slot_index": 2, "links": []}, {"localized_name": "data", "name": "data", "shape": 3, "type": "JSON", "slot_index": 3, "links": [430, 488]}], "properties": {"cnr_id": "comfyui-florence2", "ver": "1.0.3", "Node name for S&R": "Florence2Run"}, "widgets_values": ["cap plus ball plus shoes", "caption_to_phrase_grounding", false, true, 1024, 3, true, "", 70331, "fixed"], "color": "#232", "bgcolor": "#353"}, {"id": 88, "type": "DownloadAndLoadFlorence2Model", "pos": [-6570.90771484375, 962.9730224609375], "size": [315, 106], "flags": {}, "order": 27, "mode": 0, "inputs": [{"localized_name": "lora", "name": "lora", "shape": 7, "type": "PEFTLORA", "link": null}], "outputs": [{"localized_name": "florence2_model", "name": "florence2_model", "shape": 3, "type": "FL2MODEL", "slot_index": 0, "links": [197]}], "properties": {"cnr_id": "comfyui-florence2", "ver": "1.0.3", "Node name for S&R": "DownloadAndLoadFlorence2Model"}, "widgets_values": ["microsoft/Florence-2-large", "fp16", "sdpa"], "color": "#232", "bgcolor": "#353"}, {"id": 255, "type": "Florence2toCoordinates", "pos": [-6557.40234375, 1582.477294921875], "size": [210, 102], "flags": {}, "order": 78, "mode": 0, "inputs": [{"localized_name": "data", "name": "data", "type": "JSON", "link": 488}], "outputs": [{"localized_name": "center_coordinates", "name": "center_coordinates", "type": "STRING", "links": null}, {"localized_name": "bboxes", "name": "bboxes", "type": "BBOX", "links": [497]}], "properties": {"cnr_id": "ComfyUI-segment-anything-2", "ver": "c59676b008a76237002926f684d0ca3a9b29ac54", "Node name for S&R": "Florence2toCoordinates"}, "widgets_values": ["", true]}, {"id": 258, "type": "Anything Everywhere", "pos": [-6560.94140625, 1750.745849609375], "size": [239.40000915527344, 26], "flags": {}, "order": 83, "mode": 0, "inputs": [{"color_on": "", "localized_name": "anything", "name": "BBOX", "shape": 7, "type": "*", "link": 497}], "outputs": [], "properties": {"cnr_id": "cg-use-everywhere", "ver": "5.0.9", "Node name for S&R": "Anything Everywhere", "group_restricted": 0, "color_restricted": 0}, "widgets_values": []}, {"id": 335, "type": "DownloadAndLoadSAM2Model", "pos": [-6185.7314453125, 1876.762451171875], "size": [351.7801513671875, 130], "flags": {}, "order": 28, "mode": 0, "inputs": [], "outputs": [{"localized_name": "sam2_model", "name": "sam2_model", "shape": 3, "type": "SAM2MODEL", "slot_index": 0, "links": [560]}], "properties": {"cnr_id": "ComfyUI-segment-anything-2", "ver": "c59676b008a76237002926f684d0ca3a9b29ac54", "Node name for S&R": "DownloadAndLoadSAM2Model"}, "widgets_values": ["sam2.1_hiera_base_plus.safetensors", "single_image", "cuda", "fp32"]}, {"id": 272, "type": "DownloadAndLoadSAM2Model", "pos": [-6197.02880859375, 2099.983642578125], "size": [351.7801513671875, 130], "flags": {}, "order": 29, "mode": 0, "inputs": [], "outputs": [{"localized_name": "sam2_model", "name": "sam2_model", "shape": 3, "type": "SAM2MODEL", "slot_index": 0, "links": [509]}], "properties": {"cnr_id": "ComfyUI-segment-anything-2", "ver": "c59676b008a76237002926f684d0ca3a9b29ac54", "Node name for S&R": "DownloadAndLoadSAM2Model"}, "widgets_values": ["sam2.1_hiera_small.safetensors", "single_image", "cuda", "fp32"]}, {"id": 279, "type": "Anything Everywhere?", "pos": [-6982.3876953125, 1581.6993408203125], "size": [315, 106], "flags": {}, "order": 52, "mode": 0, "inputs": [{"color_on": "#64B5F6", "localized_name": "anything", "name": "IMAGE", "shape": 7, "type": "*", "link": 552}], "outputs": [], "properties": {"cnr_id": "cg-use-everywhere", "ver": "5.0.9", "Node name for S&R": "Anything Everywhere?", "group_restricted": 0, "color_restricted": 0}, "widgets_values": [".*", ".*", "Segmentation"]}, {"id": 299, "type": "Primitive string [Crysto<PERSON>]", "pos": [-5743.029296875, 2351.1435546875], "size": [315, 58], "flags": {}, "order": 30, "mode": 0, "inputs": [], "outputs": [{"localized_name": "string", "name": "string", "type": "STRING", "links": [520]}], "title": "Mask color", "properties": {"cnr_id": "ComfyUI-Crystools", "ver": "0820a7560bcc405ef6d0a7c5c53a83cc02ae7db2", "Node name for S&R": "Primitive string [Crysto<PERSON>]"}, "widgets_values": ["255,0,0"], "color": "#322", "bgcolor": "#533"}, {"id": 330, "type": "LoadImage", "pos": [-6979.322265625, 1151.424072265625], "size": [315, 314], "flags": {}, "order": 31, "mode": 0, "inputs": [], "outputs": [{"localized_name": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [551, 552]}, {"localized_name": "MASK", "name": "MASK", "type": "MASK", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "LoadImage"}, "widgets_values": ["1_original image.jpg", "image", ""], "color": "#223", "bgcolor": "#335"}, {"id": 303, "type": "Text Prompt (JPS)", "pos": [-7055.19775390625, 766.2294311523438], "size": [400, 200], "flags": {}, "order": 32, "mode": 0, "inputs": [], "outputs": [{"localized_name": "text", "name": "text", "type": "STRING", "links": [526, 527]}], "title": "Florence prompt", "properties": {"cnr_id": "ComfyUI_JPS-Nodes", "ver": "0e2a9aca02b17dde91577bfe4b65861df622dcaf", "Node name for S&R": "Text Prompt (JPS)"}, "widgets_values": ["human face"], "color": "#232", "bgcolor": "#353"}, {"id": 270, "type": "DownloadAndLoadSAM2Model", "pos": [-6189.662109375, 1639.5919189453125], "size": [351.7801513671875, 130], "flags": {}, "order": 33, "mode": 0, "inputs": [], "outputs": [{"localized_name": "sam2_model", "name": "sam2_model", "shape": 3, "type": "SAM2MODEL", "slot_index": 0, "links": [508]}], "properties": {"cnr_id": "ComfyUI-segment-anything-2", "ver": "c59676b008a76237002926f684d0ca3a9b29ac54", "Node name for S&R": "DownloadAndLoadSAM2Model"}, "widgets_values": ["sam2.1_hiera_large.safetensors", "single_image", "cuda", "fp32"]}, {"id": 325, "type": "Sam2ContextSegmentation", "pos": [-5111.1484375, 1307.023681640625], "size": [366.6081848144531, 474], "flags": {}, "order": 34, "mode": 0, "inputs": [{"localized_name": "sam2_model", "name": "sam2_model", "type": "SAM2MODEL", "link": null}, {"localized_name": "image", "name": "image", "type": "IMAGE", "link": null}, {"localized_name": "bboxes", "name": "bboxes", "shape": 7, "type": "BBOX", "link": null}, {"localized_name": "mask", "name": "mask", "shape": 7, "type": "MASK", "link": null}, {"name": "coordinates_positive", "shape": 7, "type": "STRING", "widget": {"name": "coordinates_positive"}, "link": null}, {"name": "coordinates_negative", "shape": 7, "type": "STRING", "widget": {"name": "coordinates_negative"}, "link": null}, {"name": "mask_opacity", "type": "FLOAT", "widget": {"name": "mask_opacity"}, "link": null}], "outputs": [{"localized_name": "mask", "name": "mask", "type": "MASK", "links": [545]}, {"localized_name": "tile_bboxes", "name": "tile_bboxes", "type": "BBOX", "links": null}, {"localized_name": "annotated_image", "name": "annotated_image", "type": "IMAGE", "links": [547]}, {"localized_name": "cleaned_mask", "name": "cleaned_mask", "type": "MASK", "links": [549, 550, 553]}, {"localized_name": "colored_masks", "name": "colored_masks", "type": "IMAGE", "links": [548]}], "properties": {"cnr_id": "computer-vision", "ver": "2c09ee9b0c43dee58a0bd18ccf19f0ac4d6d9cc4", "Node name for S&R": "Sam2ContextSegmentation", "aux_id": "MicheleGuidi/comfyui-computer-vision"}, "widgets_values": [2.0000000000000004, false, false, 1024, "disabled", 70, 0.020000000000000004, false, 0, 0, true, 0.5, "", "", false]}], "links": [[197, 88, 0, 87, 1, "FL2MODEL"], [200, 87, 0, 90, 0, "IMAGE"], [465, 233, 0, 224, 0, "IMAGE"], [488, 87, 3, 255, 0, "JSON"], [497, 255, 1, 258, 0, "BBOX"], [498, 259, 0, 253, 1, "MASK"], [499, 259, 1, 260, 0, "IMAGE"], [500, 259, 3, 261, 0, "IMAGE"], [501, 259, 4, 262, 0, "IMAGE"], [502, 253, 0, 263, 0, "IMAGE"], [503, 265, 0, 266, 0, "IMAGE"], [504, 264, 0, 265, 1, "MASK"], [505, 269, 0, 267, 1, "MASK"], [506, 267, 0, 268, 0, "IMAGE"], [508, 270, 0, 271, 0, "SAM2MODEL"], [509, 272, 0, 273, 0, "SAM2MODEL"], [510, 233, 0, 278, 0, "IMAGE"], [513, 253, 0, 280, 0, "IMAGE"], [514, 267, 0, 281, 0, "IMAGE"], [515, 265, 0, 282, 0, "IMAGE"], [516, 253, 0, 284, 0, "IMAGE"], [517, 267, 0, 285, 0, "IMAGE"], [518, 265, 0, 286, 0, "IMAGE"], [519, 233, 0, 287, 0, "IMAGE"], [520, 299, 0, 301, 0, "STRING"], [521, 297, 0, 300, 0, "FLOAT"], [526, 303, 0, 87, 2, "STRING"], [527, 303, 0, 305, 0, "STRING"], [528, 233, 0, 304, 0, "IMAGE"], [529, 307, 0, 304, 1, "STRING"], [530, 308, 0, 307, 2, "STRING"], [531, 310, 0, 309, 2, "STRING"], [532, 309, 0, 311, 1, "STRING"], [533, 253, 0, 311, 0, "IMAGE"], [534, 313, 0, 312, 2, "STRING"], [535, 312, 0, 314, 1, "STRING"], [536, 267, 0, 314, 0, "IMAGE"], [537, 316, 0, 315, 2, "STRING"], [538, 315, 0, 317, 1, "STRING"], [539, 265, 0, 317, 0, "IMAGE"], [540, 319, 0, 318, 2, "STRING"], [541, 318, 0, 320, 1, "STRING"], [542, 87, 0, 320, 0, "IMAGE"], [545, 325, 0, 233, 1, "MASK"], [547, 325, 2, 238, 0, "IMAGE"], [548, 325, 4, 229, 0, "IMAGE"], [550, 325, 3, 327, 0, "MASK"], [551, 330, 0, 87, 0, "IMAGE"], [552, 330, 0, 279, 0, "IMAGE"], [553, 325, 3, 238, 1, "MASK"], [560, 335, 0, 336, 0, "SAM2MODEL"], [561, 347, 0, 340, 1, "MASK"], [562, 340, 0, 341, 0, "IMAGE"], [563, 342, 0, 341, 1, "STRING"], [564, 344, 0, 342, 2, "STRING"], [565, 340, 0, 343, 0, "IMAGE"], [566, 340, 0, 345, 0, "IMAGE"], [567, 340, 0, 346, 0, "IMAGE"]], "groups": [{"id": 1, "title": "Context Segmentation - Large model", "bounding": [-5204.5859375, 1157.6407470703125, 1944.712158203125, 1107.873291015625], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 2, "title": "Tiled Segmentation (SAHI) - Large model", "bounding": [-5203.32373046875, 2352.1396484375, 1957.6278076171875, 1096.259765625], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 3, "title": "Regular Segmentation - Small model", "bounding": [-5260.291015625, 5972.99560546875, 1978.430908203125, 1087.6502685546875], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 4, "title": "Regular Segmentation - Base model", "bounding": [-5232.037109375, 4760.86962890625, 1963.2408447265625, 1106.6378173828125], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 5, "title": "Context comparisons", "bounding": [-3233.48876953125, 1165.679931640625, 4555.37255859375, 1092.7933349609375], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 6, "title": "Tiled comparisons", "bounding": [-3225.918701171875, 2350.138427734375, 3331.560791015625, 1096.3963623046875], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 7, "title": "Base comparisons", "bounding": [-3244.49609375, 4762.2685546875, 3331.560791015625, 1096.3963623046875], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 8, "title": "Small comparisons", "bounding": [-3250.055908203125, 5968.77197265625, 3331.560791015625, 1096.3963623046875], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 9, "title": "Regular Segmentation - Large model", "bounding": [-5205.9775390625, 3547.769775390625, 1963.2408447265625, 1106.6378173828125], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 10, "title": "Large comparisons", "bounding": [-3218.436279296875, 3549.168701171875, 3331.560791015625, 1096.3963623046875], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.5644739300537776, "offset": [6361.154017553362, -4852.37440215566]}, "ue_links": [{"downstream": 266, "downstream_slot": 1, "upstream": "330", "upstream_slot": 0, "controller": 279, "type": "IMAGE"}, {"downstream": 263, "downstream_slot": 1, "upstream": "330", "upstream_slot": 0, "controller": 279, "type": "IMAGE"}, {"downstream": 274, "downstream_slot": 0, "upstream": "233", "upstream_slot": 0, "controller": 278, "type": "IMAGE"}, {"downstream": 274, "downstream_slot": 1, "upstream": "253", "upstream_slot": 0, "controller": 284, "type": "IMAGE"}, {"downstream": 290, "downstream_slot": 0, "upstream": "253", "upstream_slot": 0, "controller": 280, "type": "IMAGE"}, {"downstream": 290, "downstream_slot": 1, "upstream": "265", "upstream_slot": 0, "controller": 286, "type": "IMAGE"}, {"downstream": 293, "downstream_slot": 0, "upstream": "267", "upstream_slot": 0, "controller": 281, "type": "IMAGE"}, {"downstream": 293, "downstream_slot": 1, "upstream": "265", "upstream_slot": 0, "controller": 286, "type": "IMAGE"}, {"downstream": 288, "downstream_slot": 0, "upstream": "253", "upstream_slot": 0, "controller": 280, "type": "IMAGE"}, {"downstream": 288, "downstream_slot": 1, "upstream": "233", "upstream_slot": 0, "controller": 287, "type": "IMAGE"}, {"downstream": 291, "downstream_slot": 0, "upstream": "267", "upstream_slot": 0, "controller": 281, "type": "IMAGE"}, {"downstream": 291, "downstream_slot": 1, "upstream": "233", "upstream_slot": 0, "controller": 287, "type": "IMAGE"}, {"downstream": 292, "downstream_slot": 0, "upstream": "267", "upstream_slot": 0, "controller": 281, "type": "IMAGE"}, {"downstream": 292, "downstream_slot": 1, "upstream": "253", "upstream_slot": 0, "controller": 284, "type": "IMAGE"}, {"downstream": 294, "downstream_slot": 0, "upstream": "265", "upstream_slot": 0, "controller": 282, "type": "IMAGE"}, {"downstream": 294, "downstream_slot": 1, "upstream": "233", "upstream_slot": 0, "controller": 287, "type": "IMAGE"}, {"downstream": 295, "downstream_slot": 0, "upstream": "265", "upstream_slot": 0, "controller": 282, "type": "IMAGE"}, {"downstream": 295, "downstream_slot": 1, "upstream": "253", "upstream_slot": 0, "controller": 284, "type": "IMAGE"}, {"downstream": 253, "downstream_slot": 0, "upstream": "330", "upstream_slot": 0, "controller": 279, "type": "IMAGE"}, {"downstream": 253, "downstream_slot": 2, "upstream": "297", "upstream_slot": 0, "controller": 300, "type": "FLOAT"}, {"downstream": 253, "downstream_slot": 3, "upstream": "299", "upstream_slot": 0, "controller": 301, "type": "STRING"}, {"downstream": 267, "downstream_slot": 0, "upstream": "330", "upstream_slot": 0, "controller": 279, "type": "IMAGE"}, {"downstream": 267, "downstream_slot": 2, "upstream": "297", "upstream_slot": 0, "controller": 300, "type": "FLOAT"}, {"downstream": 267, "downstream_slot": 3, "upstream": "299", "upstream_slot": 0, "controller": 301, "type": "STRING"}, {"downstream": 265, "downstream_slot": 0, "upstream": "330", "upstream_slot": 0, "controller": 279, "type": "IMAGE"}, {"downstream": 265, "downstream_slot": 2, "upstream": "299", "upstream_slot": 0, "controller": 301, "type": "STRING"}, {"downstream": 265, "downstream_slot": 3, "upstream": "297", "upstream_slot": 0, "controller": 300, "type": "FLOAT"}, {"downstream": 318, "downstream_slot": 0, "upstream": "303", "upstream_slot": 0, "controller": 305, "type": "STRING"}, {"downstream": 309, "downstream_slot": 0, "upstream": "303", "upstream_slot": 0, "controller": 305, "type": "STRING"}, {"downstream": 312, "downstream_slot": 0, "upstream": "303", "upstream_slot": 0, "controller": 305, "type": "STRING"}, {"downstream": 315, "downstream_slot": 0, "upstream": "303", "upstream_slot": 0, "controller": 305, "type": "STRING"}, {"downstream": 307, "downstream_slot": 0, "upstream": "303", "upstream_slot": 0, "controller": 305, "type": "STRING"}, {"downstream": 238, "downstream_slot": 2, "upstream": "297", "upstream_slot": 0, "controller": 300, "type": "FLOAT"}, {"downstream": 238, "downstream_slot": 3, "upstream": "299", "upstream_slot": 0, "controller": 301, "type": "STRING"}, {"downstream": 224, "downstream_slot": 1, "upstream": "330", "upstream_slot": 0, "controller": 279, "type": "IMAGE"}, {"downstream": 233, "downstream_slot": 0, "upstream": "330", "upstream_slot": 0, "controller": 279, "type": "IMAGE"}, {"downstream": 233, "downstream_slot": 2, "upstream": "299", "upstream_slot": 0, "controller": 301, "type": "STRING"}, {"downstream": 233, "downstream_slot": 3, "upstream": "297", "upstream_slot": 0, "controller": 300, "type": "FLOAT"}, {"downstream": 289, "downstream_slot": 0, "upstream": "253", "upstream_slot": 0, "controller": 280, "type": "IMAGE"}, {"downstream": 289, "downstream_slot": 1, "upstream": "267", "upstream_slot": 0, "controller": 285, "type": "IMAGE"}, {"downstream": 296, "downstream_slot": 0, "upstream": "265", "upstream_slot": 0, "controller": 282, "type": "IMAGE"}, {"downstream": 296, "downstream_slot": 1, "upstream": "267", "upstream_slot": 0, "controller": 285, "type": "IMAGE"}, {"downstream": 268, "downstream_slot": 1, "upstream": "330", "upstream_slot": 0, "controller": 279, "type": "IMAGE"}, {"downstream": 337, "downstream_slot": 0, "upstream": "340", "upstream_slot": 0, "controller": 346, "type": "IMAGE"}, {"downstream": 337, "downstream_slot": 1, "upstream": "265", "upstream_slot": 0, "controller": 286, "type": "IMAGE"}, {"downstream": 338, "downstream_slot": 0, "upstream": "340", "upstream_slot": 0, "controller": 346, "type": "IMAGE"}, {"downstream": 338, "downstream_slot": 1, "upstream": "233", "upstream_slot": 0, "controller": 287, "type": "IMAGE"}, {"downstream": 339, "downstream_slot": 0, "upstream": "340", "upstream_slot": 0, "controller": 346, "type": "IMAGE"}, {"downstream": 339, "downstream_slot": 1, "upstream": "253", "upstream_slot": 0, "controller": 284, "type": "IMAGE"}, {"downstream": 340, "downstream_slot": 0, "upstream": "330", "upstream_slot": 0, "controller": 279, "type": "IMAGE"}, {"downstream": 340, "downstream_slot": 2, "upstream": "297", "upstream_slot": 0, "controller": 300, "type": "FLOAT"}, {"downstream": 340, "downstream_slot": 3, "upstream": "299", "upstream_slot": 0, "controller": 301, "type": "STRING"}, {"downstream": 342, "downstream_slot": 0, "upstream": "303", "upstream_slot": 0, "controller": 305, "type": "STRING"}, {"downstream": 343, "downstream_slot": 1, "upstream": "330", "upstream_slot": 0, "controller": 279, "type": "IMAGE"}, {"downstream": 277, "downstream_slot": 0, "upstream": "233", "upstream_slot": 0, "controller": 278, "type": "IMAGE"}, {"downstream": 277, "downstream_slot": 1, "upstream": "265", "upstream_slot": 0, "controller": 286, "type": "IMAGE"}, {"downstream": 276, "downstream_slot": 0, "upstream": "233", "upstream_slot": 0, "controller": 278, "type": "IMAGE"}, {"downstream": 276, "downstream_slot": 1, "upstream": "267", "upstream_slot": 0, "controller": 285, "type": "IMAGE"}, {"downstream": 348, "downstream_slot": 0, "upstream": "233", "upstream_slot": 0, "controller": 278, "type": "IMAGE"}, {"downstream": 348, "downstream_slot": 1, "upstream": "340", "upstream_slot": 0, "controller": 345, "type": "IMAGE"}, {"downstream": 347, "downstream_slot": 0, "upstream": "270", "upstream_slot": 0, "controller": 271, "type": "SAM2MODEL"}, {"downstream": 347, "downstream_slot": 1, "upstream": "330", "upstream_slot": 0, "controller": 279, "type": "IMAGE"}, {"downstream": 347, "downstream_slot": 2, "upstream": "255", "upstream_slot": 1, "controller": 258, "type": "BBOX"}, {"downstream": 259, "downstream_slot": 0, "upstream": "270", "upstream_slot": 0, "controller": 271, "type": "SAM2MODEL"}, {"downstream": 259, "downstream_slot": 1, "upstream": "330", "upstream_slot": 0, "controller": 279, "type": "IMAGE"}, {"downstream": 259, "downstream_slot": 2, "upstream": "255", "upstream_slot": 1, "controller": 258, "type": "BBOX"}, {"downstream": 259, "downstream_slot": 6, "upstream": "297", "upstream_slot": 0, "controller": 300, "type": "FLOAT"}, {"downstream": 259, "downstream_slot": 7, "upstream": "299", "upstream_slot": 0, "controller": 301, "type": "STRING"}, {"downstream": 269, "downstream_slot": 0, "upstream": "335", "upstream_slot": 0, "controller": 336, "type": "SAM2MODEL"}, {"downstream": 269, "downstream_slot": 1, "upstream": "330", "upstream_slot": 0, "controller": 279, "type": "IMAGE"}, {"downstream": 269, "downstream_slot": 2, "upstream": "255", "upstream_slot": 1, "controller": 258, "type": "BBOX"}, {"downstream": 264, "downstream_slot": 0, "upstream": "272", "upstream_slot": 0, "controller": 273, "type": "SAM2MODEL"}, {"downstream": 264, "downstream_slot": 1, "upstream": "330", "upstream_slot": 0, "controller": 279, "type": "IMAGE"}, {"downstream": 264, "downstream_slot": 2, "upstream": "255", "upstream_slot": 1, "controller": 258, "type": "BBOX"}, {"downstream": 325, "downstream_slot": 0, "upstream": "270", "upstream_slot": 0, "controller": 271, "type": "SAM2MODEL"}, {"downstream": 325, "downstream_slot": 1, "upstream": "330", "upstream_slot": 0, "controller": 279, "type": "IMAGE"}, {"downstream": 325, "downstream_slot": 2, "upstream": "255", "upstream_slot": 1, "controller": 258, "type": "BBOX"}, {"downstream": 325, "downstream_slot": 6, "upstream": "297", "upstream_slot": 0, "controller": 300, "type": "FLOAT"}]}, "version": 0.4}