import os
import importlib

# --- CONFIGURATION ---
# This tells the loader where to find your Python and Javascript files.
MODULES_DIR = "modules"
WEB_DIRECTORY = "./web"

# --- LOADER LOGIC ---
NODE_CLASS_MAPPINGS = {}
NODE_DISPLAY_NAME_MAPPINGS = {}

print('------------------------------------')
print('Loading nodes from: MY_NODES')

# Get the full path to the 'modules' directory
modules_path = os.path.join(os.path.dirname(__file__), MODULES_DIR)

# Check if modules directory exists
if not os.path.exists(modules_path):
    print(f"Warning: Modules directory '{modules_path}' does not exist")
    print('------------------------------------')
else:
    # Find and import all Python files in the 'modules' directory
    for f in os.listdir(modules_path):
        if f.endswith(".py") and f != "__init__.py" and os.path.isfile(os.path.join(modules_path, f)):
            module_name = f[:-3] # remove the .py extension
            try:
                # Dynamically import the module
                module = importlib.import_module(f".{MODULES_DIR}.{module_name}", __name__)
                
                # Find and merge the mappings from that file
                if hasattr(module, 'NODE_CLASS_MAPPINGS'):
                    NODE_CLASS_MAPPINGS.update(module.NODE_CLASS_MAPPINGS)
                if hasattr(module, 'NODE_DISPLAY_NAME_MAPPINGS'):
                    NODE_DISPLAY_NAME_MAPPINGS.update(module.NODE_DISPLAY_NAME_MAPPINGS)

                print(f"  -> Loaded nodes from {module_name}.py")
            except Exception as e:
                print(f"  -> Failed to load nodes from {module_name}.py: {e}")

    print('------------------------------------')

# --- EXPORT FOR COMFYUI ---
# This makes your nodes and web directory visible to the main program.
__all__ = ['NODE_CLASS_MAPPINGS', 'NODE_DISPLAY_NAME_MAPPINGS', 'WEB_DIRECTORY']