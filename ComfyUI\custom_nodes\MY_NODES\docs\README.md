# SimpleSliderNode: Custom ComfyUI Node Documentation

## 1. Overview

The `SimpleSliderNode` is a custom ComfyUI node that provides an interactive slider interface for numeric input. Unlike standard ComfyUI input widgets, this node replaces the default UI with a custom-drawn slider that offers enhanced visual feedback and user interaction patterns inspired by the MXToolkit design philosophy.

**Key Features:**
- Custom-drawn slider interface with visual track and handle
- Support for both integer and floating-point values
- Dynamic output type switching (INT/FLOAT)
- Advanced interaction features (Ctrl+drag for precision, Shift+drag for snapping)
- Clean, minimal appearance with hidden output labels
- Theme-aware styling that adapts to ComfyUI's color scheme

**Primary Use Case:** Providing a more intuitive and visually appealing way to input numeric values in ComfyUI workflows, particularly useful for parameters that benefit from slider-style adjustment rather than text input.

## 2. File & Directory Structure

```
MY_NODES/
├── __init__.py                    # Master package loader and configuration
├── modules/
│   └── simple_slider.py          # Python backend node definition
├── web/
│   └── js/
│       ├── simple_slider.js       # JavaScript frontend UI implementation
│       └── global_appearance.js   # Optional global styling (currently unused)
└── docs/
    ├── README.md                  # This documentation
    ├── architecture.md            # Detailed architecture guide
    ├── troubleshooting.md         # Common issues and solutions
    └── api-reference.md           # Code reference documentation
```

**File Roles:**
- **`__init__.py`**: Acts as the package entry point, dynamically discovers and loads Python modules, and configures the web directory for JavaScript file serving
- **`simple_slider.py`**: Defines the backend node class with input specifications, output types, and core processing logic
- **`simple_slider.js`**: Implements the custom frontend UI, handles user interactions, and bridges communication back to the Python backend

## 3. Quick Start

### Installation
1. Copy the `MY_NODES` folder to your ComfyUI `custom_nodes` directory
2. Restart ComfyUI completely
3. Look for "Simple Slider" in the "My Nodes" category

### Basic Usage
1. Add a "Simple Slider" node to your workflow
2. Click and drag on the slider to adjust the value
3. Use Ctrl+drag for precision mode
4. Use Shift+drag to toggle step snapping
5. Connect the output to any node that accepts numeric input

## 4. Documentation Structure

This documentation is organized into several focused documents:

- **[Architecture Guide](architecture.md)** - Deep dive into the technical implementation
- **[Troubleshooting Guide](troubleshooting.md)** - Common issues and solutions
- **[API Reference](api-reference.md)** - Code reference and customization options

## 5. Key Concepts

### Frontend-Backend Bridge
The most critical aspect of this node is how the custom JavaScript UI communicates with the Python backend through hidden widgets. This maintains ComfyUI's data flow while providing enhanced UX.

### Widget Replacement Pattern
The implementation follows a widget replacement pattern where default ComfyUI inputs are hidden and replaced with custom-drawn interfaces, ensuring full compatibility with ComfyUI's processing pipeline.

### Theme Integration
The slider automatically adapts to ComfyUI's current theme using LiteGraph color constants, ensuring consistent appearance across different UI themes.

## 6. Contributing

When modifying this node:
1. Test thoroughly across different ComfyUI themes
2. Ensure the frontend-backend bridge remains intact
3. Follow the defensive programming patterns in the drawing functions
4. Update documentation for any API changes

## 7. License

This implementation is provided as-is for educational and development purposes. Feel free to adapt and extend for your own custom nodes.
