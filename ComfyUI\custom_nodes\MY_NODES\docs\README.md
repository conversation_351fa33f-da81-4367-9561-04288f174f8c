# MY_NODES Documentation

Welcome to the MY_NODES custom node collection for ComfyUI. This documentation provides comprehensive guides for each node in the collection.

## Available Nodes

### [SimpleSliderNode](simple_slider/)
An interactive slider node that provides enhanced numeric input with custom UI, theme integration, and advanced interaction features.

**Key Features:**
- Custom-drawn slider interface
- Support for both integer and floating-point values
- Advanced interaction modes (precision, snapping)
- Theme-aware styling
- Clean, minimal appearance

**Documentation:**
- [Overview & Quick Start](simple_slider/README.md)
- [Architecture Guide](simple_slider/architecture.md)
- [Troubleshooting](simple_slider/troubleshooting.md)
- [API Reference](simple_slider/api-reference.md)

## General Information

### Installation
1. Copy the entire `MY_NODES` folder to your ComfyUI `custom_nodes` directory
2. Restart ComfyUI completely
3. Look for nodes in the "My Nodes" category

### File Structure
```
MY_NODES/
├── __init__.py                    # Package loader and configuration
├── modules/                       # Python node definitions
│   └── simple_slider.py
├── web/                          # Frontend JavaScript files
│   └── js/
│       ├── simple_slider.js
│       └── global_appearance.js
└── docs/                         # Documentation
    ├── README.md                 # This file
    └── simple_slider/            # SimpleSliderNode docs
        ├── README.md
        ├── architecture.md
        ├── troubleshooting.md
        └── api-reference.md
```

### Common Troubleshooting

#### JavaScript Files Not Loading
- Ensure `WEB_DIRECTORY = "./web"` in `__init__.py`
- Verify `WEB_DIRECTORY` is in the `__all__` export list
- Restart ComfyUI completely after changes

#### Nodes Not Appearing
- Check ComfyUI console for loading messages
- Verify Python files are in the `modules/` directory
- Ensure proper `NODE_CLASS_MAPPINGS` exports

#### Theme Issues
- Use LiteGraph color constants instead of hardcoded colors
- Test across different ComfyUI themes
- Check browser console for JavaScript errors

### Development Guidelines

When creating new nodes for this collection:

1. **Follow the established patterns** from SimpleSliderNode
2. **Create dedicated documentation** in `docs/[node_name]/`
3. **Use the widget replacement pattern** for custom UIs
4. **Implement proper theme integration** with LiteGraph constants
5. **Include comprehensive troubleshooting** documentation

### Future Nodes

Additional nodes will be added to this collection, each with their own dedicated documentation folder following the same structure:

```
docs/
├── README.md                     # This index
├── simple_slider/               # Current node
└── [future_node_name]/          # Future nodes
    ├── README.md
    ├── architecture.md
    ├── troubleshooting.md
    └── api-reference.md
```

## Contributing

When modifying or extending nodes:
1. Test thoroughly across different ComfyUI themes
2. Maintain backward compatibility
3. Update documentation for any API changes
4. Follow the defensive programming patterns established in existing nodes

## Support

For issues specific to individual nodes, refer to their dedicated troubleshooting guides. For general package issues, check the common troubleshooting section above.

## License

This node collection is provided as-is for educational and development purposes. Feel free to adapt and extend for your own custom nodes.
