# Architecture Guide

## Backend Implementation (`simple_slider.py`)

### Core Architecture

The `SimpleSliderNode` follows ComfyUI's standard node pattern with a class-based approach that defines inputs, outputs, and processing logic.

```python
class SimpleSliderNode:
    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "int_val": ("INT", {"default": 20, "min": -4294967296, "max": 4294967296, "step": 1}),
                "float_val": ("FLOAT", {"default": 20.0, "min": -4294967296, "max": 4294967296, "step": 0.01}),
                "use_float": ("INT", {"default": 0, "min": 0, "max": 1, "step": 1}),
            },
        }
```

### Input Types Analysis

**Design Pattern: Dual-Type Value Storage**
The node uses a sophisticated dual-storage approach to handle both integer and floating-point values:

1. **`int_val`**: Stores the integer representation of the slider value
   - Range: Full 32-bit signed integer range for maximum compatibility
   - Default: 20 (reasonable starting point for most use cases)

2. **`float_val`**: Stores the floating-point representation with decimal precision
   - Range: Same as int_val but with 0.01 step precision
   - Default: 20.0 (matches int_val for consistency)

3. **`use_float`**: Boolean flag (0/1) that determines output type
   - Controls whether the node outputs integer or float values
   - Allows dynamic type switching without recreating the node

### Output Configuration

```python
RETURN_TYPES = ("*",)  # Universal type - critical for preventing infinite buffering
RETURN_NAMES = ("value",)
FUNCTION = "pass_value"
CATEGORY = "My Nodes"
```

**Critical Design Choice: Universal Return Type**
The `("*",)` return type is essential - using undefined types like `(any,)` causes ComfyUI to enter infinite buffering loops as it attempts to resolve the type.

### Processing Logic

```python
def pass_value(self, int_val, float_val, use_float):
    if use_float > 0:
        return (float_val,)
    else:
        return (int_val,)
```

**Pattern: Conditional Type Passthrough**
The processing function acts as a smart router that selects the appropriate value based on the `use_float` flag, enabling seamless switching between data types without data loss.

### Registration Mappings

```python
NODE_CLASS_MAPPINGS = { "SimpleSliderNode": SimpleSliderNode }
NODE_DISPLAY_NAME_MAPPINGS = { "SimpleSliderNode": "Simple Slider" }
```

These mappings are crucial for ComfyUI's node discovery system and must be exported for the dynamic loader in `__init__.py` to find and register the node.

## Frontend UI Implementation (`simple_slider.js`)

### Overall Strategy: Widget Replacement Architecture

The frontend implementation follows a **widget replacement pattern** where the default ComfyUI input widgets are hidden and replaced with a custom-drawn interface. This approach maintains full compatibility with ComfyUI's data flow while providing enhanced UX.

### Core Class Structure

```javascript
class SimpleSlider {
    constructor(node) {
        this.node = node;
        // Initialize properties, hide widgets, set up event handlers
    }
}
```

### Property System

```javascript
this.node.properties = {
    value: 20,      // Current slider value
    min: 0,         // Minimum value
    max: 100,       // Maximum value  
    step: 1,        // Step increment
    decimals: 0,    // Decimal places for display
    snap: true      // Enable step snapping
};
```

**Design Pattern: MXToolkit Compatibility**
The property structure mirrors the MXToolkit slider implementation, ensuring consistent behavior and potential interoperability.

### Widget Hiding Mechanism

```javascript
// Hide original widgets and set type to hidden
for (let i = 0; i < 3; i++) { 
    this.node.widgets[i].hidden = true; 
    this.node.widgets[i].type = "hidden"; 
}
```

**Two-Stage Hiding Process:**
1. **Visual hiding**: `hidden = true` removes widgets from display
2. **Type masking**: `type = "hidden"` prevents ComfyUI from processing them as active inputs

### Advanced Hiding: The `onAdded` Handler

```javascript
this.node.onAdded = function() {
    this.outputs[0].name = this.outputs[0].localized_name = ""; // Hide output name
    this.widgets_start_y = -2.4e8 * LiteGraph.NODE_SLOT_HEIGHT; // Hide widgets completely
    // ... additional setup
};
```

**Critical Technique: Extreme Offset**
The `widgets_start_y = -2.4e8 * LiteGraph.NODE_SLOT_HEIGHT` line pushes the widget rendering area far off-screen, ensuring complete visual hiding even if other hiding methods fail.

### Custom Drawing: The `onDrawForeground` Function

```javascript
this.node.onDrawForeground = function(ctx) {
    if (this.flags.collapsed) return false; // Defensive programming

    // Draw slider track
    ctx.fillStyle = "rgba(20,20,20,0.5)";
    ctx.roundRect(shiftLeft, shY - 1, this.size[0] - shiftRight - shiftLeft, 4, 2);
    ctx.fill();

    // Draw slider handle
    ctx.fillStyle = LiteGraph.NODE_TEXT_COLOR;
    ctx.arc(shiftLeft + (this.size[0] - shiftRight - shiftLeft) * this.intpos.x, shY + 1, 7, 0, 2 * Math.PI, false);
    ctx.fill();

    // Draw value text
    ctx.fillText(this.properties.value.toFixed(dgt), this.size[0] - shiftRight + 24, shX);
};
```

**Canvas API Usage Pattern:**
- **Track**: Semi-transparent rounded rectangle for the slider rail
- **Handle**: Filled circle with outline, positioned based on normalized value (`intpos.x`)
- **Text**: Current value displayed with appropriate decimal precision
- **Theme Integration**: Uses `LiteGraph.NODE_TEXT_COLOR` for automatic theme adaptation

## The Frontend-Backend Bridge: Critical Communication Pattern

This is the most critical aspect of the architecture. The communication happens through a **two-stage process** that maintains ComfyUI's data flow integrity while providing custom UI interaction.

### Stage 1: User Interaction Capture

The frontend captures user interactions through a sophisticated mouse event system:

```javascript
// Mouse down - initiate interaction
this.node.onMouseDown = function(e) {
    // Boundary checking
    if (e.canvasY - this.pos[1] < 0) return false;
    if (e.canvasX < this.pos[0] + shiftLeft - 5 || e.canvasX > this.pos[0] + this.size[0] - shiftRight + 5) return false;

    this.capture = true;           // Enable capture mode
    this.unlock = false;           // Reset precision mode
    this.captureInput(true);       // Capture mouse events
    this.valueUpdate(e);           // Calculate new value
    return true;
};

// Mouse move - continuous updates during drag
this.node.onMouseMove = function(e, pos, canvas) {
    if (!this.capture) return;
    if (canvas.pointer.isDown === false) {
        this.onMouseUp(e);
        return;
    }
    this.valueUpdate(e);           // Update value during drag
};
```

### Value Calculation Logic

```javascript
this.node.valueUpdate = function(e) {
    let prevX = this.properties.value;
    let rn = Math.pow(10, this.properties.decimals);

    // Convert mouse position to normalized value (0-1)
    let vX = (e.canvasX - this.pos[0] - shiftLeft) / (this.size[0] - shiftRight - shiftLeft);

    // Handle special interaction modes
    if (e.ctrlKey) this.unlock = true;  // Precision mode
    if (e.shiftKey !== this.properties.snap) {  // Step snapping
        let step = this.properties.step / (this.properties.max - this.properties.min);
        vX = Math.round(vX / step) * step;
    }

    // Clamp and convert to actual value
    this.intpos.x = Math.max(0, Math.min(1, vX));
    this.properties.value = Math.round(rn * (this.properties.min + (this.properties.max - this.properties.min) * this.intpos.x)) / rn;

    // Trigger redraw if value changed
    if (this.properties.value !== prevX) {
        this.setDirtyCanvas(true, false);
    }
};
```

### Stage 2: Backend Widget Synchronization

**Critical Bridge Mechanism:**
When the user releases the mouse, the frontend updates the **original hidden widgets** that were defined in the Python backend:

```javascript
this.node.onMouseUp = function(e) {
    if (!this.capture) return;
    this.capture = false;
    this.captureInput(false);

    // THIS IS THE CRITICAL BRIDGE: Update the hidden backend widgets
    this.widgets[0].value = Math.floor(this.properties.value);  // int_val
    this.widgets[1].value = this.properties.value;              // float_val
    this.widgets[2].value = (this.properties.decimals > 0) ? 1 : 0;  // use_float
};
```

**How the Bridge Works:**
1. **Widget Array Correspondence**: `this.widgets[0]`, `this.widgets[1]`, and `this.widgets[2]` directly correspond to the `int_val`, `float_val`, and `use_float` inputs defined in the Python `INPUT_TYPES`
2. **Value Synchronization**: The custom UI updates these hidden widget values, which ComfyUI then passes to the Python `pass_value` function when the workflow executes
3. **Type Consistency**: The `use_float` widget is automatically set based on the `decimals` property, ensuring the correct output type

**Data Flow Summary:**
```
User Interaction → JavaScript Value Calculation → Hidden Widget Update → ComfyUI Processing → Python pass_value Function → Output
```
