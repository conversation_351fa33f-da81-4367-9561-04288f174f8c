# SimpleSliderNode Documentation

## Overview

The `SimpleSliderNode` is a custom ComfyUI node that provides an interactive slider interface for numeric input. Unlike standard ComfyUI input widgets, this node replaces the default UI with a custom-drawn slider that offers enhanced visual feedback and user interaction patterns inspired by the MXToolkit design philosophy.

**Key Features:**
- Custom-drawn slider interface with visual track and handle
- Support for both integer and floating-point values
- Dynamic output type switching (INT/FLOAT)
- Advanced interaction features (Ctrl+drag for precision, Shift+drag for snapping)
- Clean, minimal appearance with hidden output labels
- Theme-aware styling that adapts to ComfyUI's color scheme

**Primary Use Case:** Providing a more intuitive and visually appealing way to input numeric values in ComfyUI workflows, particularly useful for parameters that benefit from slider-style adjustment rather than text input.

## Quick Start

### Installation
The SimpleSliderNode is part of the MY_NODES package. Ensure the entire MY_NODES folder is in your ComfyUI `custom_nodes` directory.

### Basic Usage
1. Add a "Simple Slider" node to your workflow from the "My Nodes" category
2. Click and drag on the slider to adjust the value
3. Use Ctrl+drag for precision mode
4. Use Shift+drag to toggle step snapping
5. Connect the output to any node that accepts numeric input

### Visual Interface
The slider displays:
- A horizontal track showing the value range (automatically darker than node background)
- A progress bar showing filled portion from start to current position
- A draggable handle indicating current position
- The current numeric value displayed to the right (centered with track)

## File Structure

The SimpleSliderNode consists of three key files:

```
MY_NODES/
├── __init__.py                    # Package loader (shared)
├── modules/
│   └── simple_slider.py          # Python backend definition
└── web/
    └── js/
        └── simple_slider.js       # JavaScript frontend UI
```

## Documentation Structure

- **[Architecture Guide](architecture.md)** - Deep dive into the technical implementation
- **[Troubleshooting Guide](troubleshooting.md)** - Common issues and solutions
- **[API Reference](api-reference.md)** - Code reference and customization options

## Key Concepts

### Frontend-Backend Bridge
The most critical aspect of this node is how the custom JavaScript UI communicates with the Python backend through hidden widgets. This maintains ComfyUI's data flow while providing enhanced UX.

### Widget Replacement Pattern
The implementation follows a widget replacement pattern where default ComfyUI inputs are hidden and replaced with custom-drawn interfaces, ensuring full compatibility with ComfyUI's processing pipeline.

### Theme Integration
The slider automatically adapts to ComfyUI's current theme using LiteGraph color constants, ensuring consistent appearance across different UI themes.

## Configuration Options

The slider can be customized by modifying the properties in the JavaScript file:

```javascript
this.node.properties = {
    value: 20,      // Default value
    min: 0,         // Minimum value
    max: 100,       // Maximum value  
    step: 1,        // Step increment
    decimals: 0,    // Decimal places (0 = integer, >0 = float)
    snap: true      // Enable step snapping
};
```

## Advanced Features

### Precision Mode (Ctrl+Drag)
Hold Ctrl while dragging for fine-grained control that bypasses step constraints.

### Snap Toggle (Shift+Drag)
Hold Shift to temporarily toggle the snap behavior while dragging.

### Dynamic Type Output
The node automatically switches between INT and FLOAT output based on the `decimals` setting.

## Contributing

When modifying this node:
1. Test thoroughly across different ComfyUI themes
2. Ensure the frontend-backend bridge remains intact
3. Follow the defensive programming patterns in the drawing functions
4. Update documentation for any API changes

## Related Nodes

This is part of the MY_NODES collection. Other nodes in this package will have their own documentation folders following the same structure.
