# SimpleSliderNode API Reference

## Python Backend API

### SimpleSliderNode Class

```python
class SimpleSliderNode:
    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "int_val": ("INT", {"default": 20, "min": -4294967296, "max": 4294967296, "step": 1}),
                "float_val": ("FLOAT", {"default": 20.0, "min": -4294967296, "max": 4294967296, "step": 0.01}),
                "use_float": ("INT", {"default": 0, "min": 0, "max": 1, "step": 1}),
            },
        }

    RETURN_TYPES = ("*",)
    RETURN_NAMES = ("value",)
    FUNCTION = "pass_value"
    CATEGORY = "My Nodes"

    def pass_value(self, int_val, float_val, use_float):
        if use_float > 0:
            return (float_val,)
        else:
            return (int_val,)
```

### Input Parameters

#### int_val
- **Type**: INT
- **Default**: 20
- **Range**: -4294967296 to 4294967296
- **Purpose**: Stores the integer representation of the slider value

#### float_val
- **Type**: FLOAT
- **Default**: 20.0
- **Range**: -4294967296.0 to 4294967296.0
- **Step**: 0.01
- **Purpose**: Stores the floating-point representation with decimal precision

#### use_float
- **Type**: INT (boolean flag)
- **Default**: 0
- **Range**: 0 to 1
- **Purpose**: Determines output type (0 = integer, 1 = float)

### Output

#### value
- **Type**: Dynamic ("*")
- **Content**: Either int_val or float_val based on use_float flag
- **Purpose**: The final numeric value for use in workflows

## JavaScript Frontend API

### SimpleSlider Class

```javascript
class SimpleSlider {
    constructor(node) {
        this.node = node;
        // Initialization and setup
    }
}
```

### Node Properties

```javascript
this.node.properties = {
    value: 20,      // Current slider value
    min: 0,         // Minimum value
    max: 100,       // Maximum value  
    step: 1,        // Step increment
    decimals: 0,    // Decimal places for display
    snap: true      // Enable step snapping
};
```

#### Property Details

##### value
- **Type**: Number
- **Default**: 20
- **Purpose**: Current slider position value

##### min
- **Type**: Number
- **Default**: 0
- **Purpose**: Minimum slider value

##### max
- **Type**: Number
- **Default**: 100
- **Purpose**: Maximum slider value

##### step
- **Type**: Number
- **Default**: 1
- **Purpose**: Increment step for snapping

##### decimals
- **Type**: Integer
- **Default**: 0
- **Range**: 0-4
- **Purpose**: Number of decimal places to display and calculate

##### snap
- **Type**: Boolean
- **Default**: true
- **Purpose**: Enable/disable step snapping behavior

### Event Handlers

#### onMouseDown(e)
- **Purpose**: Initiates slider interaction
- **Parameters**: Mouse event object
- **Returns**: Boolean (true if handled)
- **Behavior**: Starts capture mode and begins value updates

#### onMouseMove(e, pos, canvas)
- **Purpose**: Handles continuous slider updates during drag
- **Parameters**: Mouse event, position, canvas object
- **Behavior**: Updates slider value while dragging

#### onMouseUp(e)
- **Purpose**: Finalizes slider interaction
- **Parameters**: Mouse event object
- **Behavior**: Releases capture and updates backend widgets

#### onDrawForeground(ctx)
- **Purpose**: Renders the custom slider UI
- **Parameters**: Canvas 2D context
- **Behavior**: Draws track, progress bar, handle, and value text with theme-intelligent coloring

### Interaction Modes

#### Normal Mode
- Click and drag to adjust value
- Snaps to step increments if snap is enabled

#### Precision Mode (Ctrl+Drag)
- Hold Ctrl while dragging for precise control
- Bypasses step snapping
- Allows fine-grained value adjustment

#### Snap Toggle (Shift+Drag)
- Hold Shift to toggle snap behavior
- If snap is normally on, Shift turns it off
- If snap is normally off, Shift turns it on

### Widget Bridge API

The frontend communicates with the backend through hidden widget updates:

```javascript
// Update backend widgets on mouse release
this.widgets[0].value = Math.floor(this.properties.value);  // int_val
this.widgets[1].value = this.properties.value;              // float_val  
this.widgets[2].value = (this.properties.decimals > 0) ? 1 : 0;  // use_float
```

#### Widget Array Mapping
- `widgets[0]` → `int_val` (Python INPUT_TYPES first parameter)
- `widgets[1]` → `float_val` (Python INPUT_TYPES second parameter)
- `widgets[2]` → `use_float` (Python INPUT_TYPES third parameter)

## Customization Options

### Changing Slider Range
Modify the properties in the constructor:
```javascript
this.node.properties = {
    value: 50,      // New default
    min: -100,      // New minimum
    max: 200,       // New maximum
    step: 5,        // New step size
    decimals: 1,    // Enable decimals
    snap: true
};
```

### Visual Customization

#### Track and Progress Bar Colors
The slider uses theme-intelligent rgba overlays for automatic color adaptation:
```javascript
// Track darkness (empty portion)
ctx.fillStyle = "rgba(0,0,0,0.3)"; // Adjust 0.3 for darker/lighter track

// Progress bar darkness (filled portion)
ctx.fillStyle = "rgba(0,0,0,0.15)"; // Adjust 0.15 for darker/lighter progress
```

#### Custom Dimensions
Modify the drawing constants:
```javascript
// Track dimensions
const trackHeight = 4;  // Default track height
const trackRadius = 2;  // Default corner radius

// Handle dimensions
const handleRadius = 7; // Default handle radius
const handleOutlineRadius = 5; // Default outline radius
```

#### Color Hierarchy
The visual hierarchy from darkest to lightest:
1. **Empty Track**: `rgba(0,0,0,0.3)` overlay on background
2. **Progress Bar**: Handle color + `rgba(0,0,0,0.15)` overlay
3. **Handle**: Pure `LiteGraph.NODE_TEXT_COLOR`

### Adding New Properties
1. Add to the properties object
2. Update the `onPropertyChanged` handler
3. Modify drawing/interaction logic as needed

## Extension Registration

```javascript
app.registerExtension({
    name: "SimpleSliderUI",
    async beforeRegisterNodeDef(nodeType, nodeData, _app) {
        if (nodeData.name === "SimpleSliderNode") {
            const onNodeCreated = nodeType.prototype.onNodeCreated;
            nodeType.prototype.onNodeCreated = function() {
                if (onNodeCreated) onNodeCreated.apply(this, arguments);
                this.simpleSlider = new SimpleSlider(this);
            };
        }
    }
});
```

This registration pattern ensures the custom UI is applied only to SimpleSliderNode instances while preserving any existing onNodeCreated behavior.

## Layout Constants

The slider uses these layout constants for positioning:

```javascript
const fontsize = LiteGraph.NODE_SUBTEXT_SIZE;
const shX = (this.node.slot_start_y || 0) + fontsize * 1.5;
const shY = LiteGraph.NODE_SLOT_HEIGHT / 1.5;
const shiftLeft = 10;
const shiftRight = 60;
```

- **shiftLeft**: Left margin for slider track
- **shiftRight**: Right margin (space for value text)
- **shY**: Vertical position of slider track
- **shX**: Vertical position of value text

## Theme Integration

The slider uses these LiteGraph constants for theme compatibility:

```javascript
// Text color (adapts to theme)
ctx.fillStyle = LiteGraph.NODE_TEXT_COLOR;

// Background color (adapts to theme)
ctx.strokeStyle = this.bgcolor || LiteGraph.NODE_DEFAULT_BGCOLOR;

// Node sizing
this.node.size = [210, Math.floor(LiteGraph.NODE_SLOT_HEIGHT * 1.5)];
```
