# SimpleSliderNode Troubleshooting Guide

## Issue: JavaScript File Not Loading

**Symptoms:**
- Custom slider UI doesn't appear
- Default widgets remain visible
- No console errors about the slider extension

**Root Cause:**
The `__init__.py` file is not properly configured to serve JavaScript files.

**Critical Configuration Points:**

1. **WEB_DIRECTORY Setting:**
```python
WEB_DIRECTORY = "./web"  # Must point to correct directory
```

2. **Export List:**
```python
__all__ = ['NODE_CLASS_MAPPINGS', 'NODE_DISPLAY_NAME_MAPPINGS', 'WEB_DIRECTORY']
```
The `WEB_DIRECTORY` **must** be included in `__all__` or ComfyUI won't serve the JavaScript files.

3. **File Structure Alignment:**
Ensure the directory structure matches the `WEB_DIRECTORY` setting:
```
MY_NODES/
├── __init__.py (WEB_DIRECTORY = "./web")
└── web/
    └── js/
        └── simple_slider.js
```

**Solution:**
1. Verify `WEB_DIRECTORY` path in `__init__.py`
2. Confirm `WEB_DIRECTORY` is in the `__all__` export list
3. Check file paths and directory structure
4. Restart ComfyUI completely (not just refresh browser)

## Issue: Default Widgets Still Visible

**Symptoms:**
- Custom slider appears but original input fields are also visible
- Node appears larger than expected
- Input widgets overlap with custom UI

**Root Cause:**
The JavaScript widget hiding mechanism failed to execute properly.

**Debugging Steps:**

1. **Check Browser Console:**
   Open browser developer tools (F12) and look for JavaScript errors in the console.

2. **Verify Extension Loading:**
   Look for console messages indicating the extension loaded:
   ```javascript
   console.log("SimpleSliderUI extension loaded");
   ```

3. **Hard Refresh:**
   Use Ctrl+Shift+R (or Cmd+Shift+R on Mac) to clear browser cache and force reload of JavaScript files.

**Solution Hierarchy:**

1. **Primary Hiding Method:**
   ```javascript
   for (let i = 0; i < 3; i++) { 
       this.node.widgets[i].hidden = true; 
       this.node.widgets[i].type = "hidden"; 
   }
   ```

2. **Secondary Hiding (onAdded):**
   ```javascript
   this.widgets_start_y = -2.4e8 * LiteGraph.NODE_SLOT_HEIGHT;
   ```

3. **Nuclear Option:**
   If widgets still appear, add this to the constructor:
   ```javascript
   this.node.widgets.forEach(widget => {
       widget.hidden = true;
       widget.type = "hidden";
       widget.computeSize = () => [0, 0];
   });
   ```

## Issue: UI Freeze or Crash

**Symptoms:**
- ComfyUI becomes unresponsive when adding the node
- Browser tab crashes
- Infinite loop in console

**Root Cause:**
Error in the `onDrawForeground` function causing infinite redraw loops.

**Critical Defensive Programming Patterns:**

1. **Collapse Check:**
   ```javascript
   this.node.onDrawForeground = function(ctx) {
       if (this.flags.collapsed) return false;  // CRITICAL: Prevent drawing when collapsed
       // ... rest of drawing code
   };
   ```

2. **Property Existence Checks:**
   ```javascript
   if (!this.properties || !this.properties.value) return false;
   if (!this.intpos || typeof this.intpos.x !== 'number') return false;
   ```

3. **Canvas Context Validation:**
   ```javascript
   if (!ctx || typeof ctx.fillRect !== 'function') return false;
   ```

4. **Size Validation:**
   ```javascript
   if (!this.size || this.size.length < 2 || this.size[0] <= 0 || this.size[1] <= 0) return false;
   ```

**Emergency Recovery:**
If the node causes crashes, temporarily comment out the entire `onDrawForeground` function to isolate the issue:

```javascript
this.node.onDrawForeground = function(ctx) {
    return false; // Disable all drawing temporarily
};
```

## Issue: Values Not Updating in Workflow

**Symptoms:**
- Slider moves but output values don't change
- Workflow uses old/default values
- Custom UI appears to work but backend doesn't receive updates

**Root Cause:**
The widget synchronization in `onMouseUp` is not properly updating the backend widgets.

**Debugging:**
Add console logging to verify widget updates:
```javascript
this.node.onMouseUp = function(e) {
    console.log("Before update:", this.widgets[0].value, this.widgets[1].value, this.widgets[2].value);
    
    this.widgets[0].value = Math.floor(this.properties.value);
    this.widgets[1].value = this.properties.value;
    this.widgets[2].value = (this.properties.decimals > 0) ? 1 : 0;
    
    console.log("After update:", this.widgets[0].value, this.widgets[1].value, this.widgets[2].value);
};
```

**Solution:**
Ensure the widget array indices match the Python `INPUT_TYPES` order:

- `widgets[0]` = `int_val` (first in INPUT_TYPES)
- `widgets[1]` = `float_val` (second in INPUT_TYPES)  
- `widgets[2]` = `use_float` (third in INPUT_TYPES)

## Issue: Theme Colors Not Adapting

**Symptoms:**
- Slider appears with wrong colors in different themes
- UI elements don't match ComfyUI's current theme

**Root Cause:**
Using hardcoded colors instead of theme-aware techniques.

**Solution:**
Use rgba overlays for automatic theme adaptation:
```javascript
// Good - theme intelligent rgba overlays
ctx.fillStyle = "rgba(0,0,0,0.3)"; // Always darker than background
ctx.fillStyle = LiteGraph.NODE_TEXT_COLOR; // Theme-aware text color

// Bad - hardcoded colors
ctx.fillStyle = "#333333"; // Fixed color that may not work in all themes
ctx.fillStyle = "#ffffff"; // Fixed color that may not work in all themes
```

**Current Implementation:**
The SimpleSliderNode uses rgba overlays that automatically adapt:
- **Track**: `rgba(0,0,0,0.3)` - Always appears darker than node background
- **Progress**: Handle color + `rgba(0,0,0,0.15)` - Slightly darker than handle
- **Handle**: `LiteGraph.NODE_TEXT_COLOR` - Theme-aware text color

## Issue: Infinite Buffering on Node Creation

**Symptoms:**
- ComfyUI shows infinite loading/buffering when adding the node
- UI becomes unresponsive
- Console shows type resolution errors

**Root Cause:**
Using undefined return types in the Python node definition.

**Critical Fix:**
```python
# Good - uses universal type
RETURN_TYPES = ("*",)

# Bad - causes infinite buffering
RETURN_TYPES = (any,)  # 'any' is not defined
```

**Solution:**
Always use `("*",)` for dynamic return types in ComfyUI custom nodes.

## Issue: Progress Bar or Track Not Visible

**Symptoms:**
- Slider handle works but track appears invisible
- Progress bar doesn't show filled portion
- Visual elements blend into background

**Root Cause:**
The rgba overlay values may be too light for certain themes or backgrounds.

**Solution:**
Adjust the rgba opacity values in the drawing code:
```javascript
// Make track darker (increase opacity)
ctx.fillStyle = "rgba(0,0,0,0.5)"; // Increase from 0.3 to 0.5

// Make progress bar more visible (increase opacity)
ctx.fillStyle = "rgba(0,0,0,0.25)"; // Increase from 0.15 to 0.25
```

**Debugging:**
Add temporary high-contrast colors to verify drawing:
```javascript
// Temporary debugging colors
ctx.fillStyle = "red"; // Track should appear red
ctx.fillStyle = "blue"; // Progress should appear blue
```

## Issue: Visual Elements Misaligned

**Symptoms:**
- Text not centered with track
- Handle appears offset from track
- Progress bar doesn't align properly

**Root Cause:**
Layout constants may be incorrect for your ComfyUI version.

**Solution:**
Verify the layout constants match your setup:
```javascript
const shY = LiteGraph.NODE_SLOT_HEIGHT / 1.5; // Track vertical position
const textY = shY + 1; // Text should match track center
```

**Debugging:**
Add visual guides to check alignment:
```javascript
// Draw alignment guides (remove after debugging)
ctx.strokeStyle = "red";
ctx.beginPath();
ctx.moveTo(0, shY);
ctx.lineTo(this.size[0], shY);
ctx.stroke();
```

## General Debugging Tips

### Enable Console Logging
Add debug logging to track execution:
```javascript
console.log("SimpleSlider constructor called");
console.log("Node properties:", this.node.properties);
console.log("Widget values:", this.node.widgets.map(w => w.value));
```

### Check ComfyUI Console
Monitor the ComfyUI server console for Python errors:
```
Loading custom nodes from: MY_NODES
-> Loaded nodes from simple_slider.py
```

### Browser Developer Tools
Use F12 to open developer tools and check:

- Console tab for JavaScript errors
- Network tab to verify JS files are loading
- Elements tab to inspect the node DOM structure

### Hard Refresh Protocol
When making changes:

1. Save all files
2. Hard refresh browser (Ctrl+Shift+R)
3. If issues persist, restart ComfyUI server
4. Clear browser cache completely if needed
