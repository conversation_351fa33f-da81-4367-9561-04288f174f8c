import { app } from "../../../scripts/app.js";


class SimpleSlider
{
    constructor(node)
    {
        this.node = node;

        // Basic properties
        this.node.properties = {
            value: 20
        };

        // Basic size
        this.node.size = [210, 50];

        // Hide original widgets
        for (let i = 0; i < 3; i++) {
            this.node.widgets[i].hidden = true;
        }

        // Simple drawing - just show the value
        this.node.onDrawForeground = function(ctx) {
            if (this.flags.collapsed) return;

            ctx.fillStyle = "#fff";
            ctx.font = "12px Arial";
            ctx.textAlign = "center";
            ctx.fillText(this.properties.value, this.size[0] / 2, 35);
        };

        // Initialize widget values once
        this.node.widgets[0].value = Math.floor(this.node.properties.value);  // int_val
        this.node.widgets[1].value = this.node.properties.value;              // float_val
        this.node.widgets[2].value = 0;                                       // use_float
    }
}

app.registerExtension({
    name: "SimpleSlider<PERSON>",
    async beforeRegisterNodeDef(nodeType, nodeData, _app) {
        if (nodeData.name === "SimpleSliderNode") {
            const onNodeCreated = nodeType.prototype.onNodeCreated;
            nodeType.prototype.onNodeCreated = function() {
                if (onNodeCreated) onNodeCreated.apply(this, arguments);
                this.simpleSlider = new SimpleSlider(this);
            };
        }
    }
});