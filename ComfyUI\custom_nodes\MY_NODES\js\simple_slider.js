import { app } from "../../../scripts/app.js";


class SimpleSlider
{
    constructor(node)
    {
        this.node = node;

        // Basic properties
        this.node.properties = {
            value: 20,
            min: 0,
            max: 100,
            step: 1
        }

        // Basic size
        this.node.size = [210, 50];

        // Track interaction state
        this.node.capture = false;

        // Hide original widgets
        for (let i = 0; i < 3; i++) {
            this.node.widgets[i].hidden = true;
        }

        // Draw the slider
        this.node.onDrawForeground = function(ctx) {
            if (this.flags.collapsed) return;

            const sliderY = 25;
            const sliderLeft = 10;
            const sliderRight = this.size[0] - 10;
            const sliderWidth = sliderRight - sliderLeft;

            // Draw slider track
            ctx.fillStyle = "#444";
            ctx.fillRect(sliderLeft, sliderY - 2, sliderWidth, 4);

            // Calculate slider position
            const normalizedValue = (this.properties.value - this.properties.min) / (this.properties.max - this.properties.min);
            const sliderPos = sliderLeft + normalizedValue * sliderWidth;

            // Draw slider handle
            ctx.fillStyle = "#fff";
            ctx.beginPath();
            ctx.arc(sliderPos, sliderY, 6, 0, 2 * Math.PI);
            ctx.fill();

            // Draw value text
            ctx.fillStyle = "#fff";
            ctx.font = "12px Arial";
            ctx.textAlign = "center";
            ctx.fillText(this.properties.value.toFixed(0), this.size[0] / 2, 45);
        };

        // Mouse down handler
        this.node.onMouseDown = function(e) {
            if (e.canvasY - this.pos[1] < 10 || e.canvasY - this.pos[1] > 40) return false;
            if (e.canvasX < this.pos[0] + 10 || e.canvasX > this.pos[0] + this.size[0] - 10) return false;

            this.capture = true;
            this.captureInput(true);
            this.valueUpdate(e);
            return true;
        };

        // Mouse move handler
        this.node.onMouseMove = function(e, pos, canvas) {
            if (!this.capture) return;
            if (canvas.pointer.isDown === false) {
                this.onMouseUp(e);
                return;
            }
            this.valueUpdate(e);
        };

        // Mouse up handler
        this.node.onMouseUp = function(e) {
            if (!this.capture) return;
            this.capture = false;
            this.captureInput(false);

            // Update the hidden widgets with final values
            this.widgets[0].value = Math.floor(this.properties.value);  // int_val
            this.widgets[1].value = this.properties.value;              // float_val
            this.widgets[2].value = 0;                                  // use_float
        };

        // Value update function
        this.node.valueUpdate = function(e) {
            const sliderLeft = 10;
            const sliderWidth = this.size[0] - 20;

            // Calculate normalized position (0-1)
            let normalizedPos = (e.canvasX - this.pos[0] - sliderLeft) / sliderWidth;
            normalizedPos = Math.max(0, Math.min(1, normalizedPos));

            // Convert to actual value
            const newValue = this.properties.min + normalizedPos * (this.properties.max - this.properties.min);

            // Apply step rounding
            const steppedValue = Math.round(newValue / this.properties.step) * this.properties.step;

            // Update property if changed
            if (this.properties.value !== steppedValue) {
                this.properties.value = steppedValue;
                // Only mark as dirty when value actually changes
                this.setDirtyCanvas(true, false);
            }
        };
    }
}

app.registerExtension({
    name: "SimpleSliderUI",
    async beforeRegisterNodeDef(nodeType, nodeData, _app) {
        if (nodeData.name === "SimpleSliderNode") {
            const onNodeCreated = nodeType.prototype.onNodeCreated;
            nodeType.prototype.onNodeCreated = function() {
                if (onNodeCreated) onNodeCreated.apply(this, arguments);
                this.simpleSlider = new SimpleSlider(this);
            };
        }
    }
});