class SimpleSliderNode:
    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "int_val": ("INT", {"default": 20, "min": -4294967296, "max": 4294967296, "step": 1}),
                "float_val": ("FLOAT", {"default": 20.0, "min": -4294967296, "max": 4294967296, "step": 0.01}),
                "use_float": ("INT", {"default": 0, "min": 0, "max": 1, "step": 1}),
            },
        }

    RETURN_TYPES = ("*",)  # Use "*" for any type instead of undefined 'any'
    RETURN_NAMES = ("value",)
    FUNCTION = "pass_value"
    CATEGORY = "MY NODES"

    def pass_value(self, int_val, float_val, use_float):
        if use_float > 0:
            return (float_val,)
        else:
            return (int_val,)

# The mappings must also be updated if you change the class name, but the widget names inside are fine.
NODE_CLASS_MAPPINGS = { "SimpleSliderNode": SimpleSliderNode }
NODE_DISPLAY_NAME_MAPPINGS = { "SimpleSliderNode": "Simple Slider" }