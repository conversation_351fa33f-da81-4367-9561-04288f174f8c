import folder_paths
import comfy.sd
import comfy.utils

class SliderLoraLoaderNode:
    def __init__(self):
        self.loaded_lora = None

    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "model": ("MODEL",),
                "clip": ("CLIP",),
                "lora_name": (folder_paths.get_filename_list("loras"), ),
                "strength_float": ("FLOAT", {"default": 1.0, "min": -10.0, "max": 10.0, "step": 0.01}),
                "strength_int": ("INT", {"default": 100, "min": -1000, "max": 1000, "step": 1}),
                "use_float": ("INT", {"default": 1, "min": 0, "max": 1, "step": 1}),
            },
        }

    RETURN_TYPES = ("MODEL", "CLIP")
    RETURN_NAMES = ("model", "clip")
    FUNCTION = "load_lora"
    CATEGORY = "My Nodes"

    def load_lora(self, model, clip, lora_name, strength_float, strength_int, use_float):
        # Determine which strength value to use based on use_float flag
        if use_float > 0:
            strength_model = strength_float
            strength_clip = strength_float
        else:
            # Convert integer percentage to float (100 = 1.0)
            strength_model = strength_int / 100.0
            strength_clip = strength_int / 100.0

        # Handle zero strength case
        if strength_model == 0 and strength_clip == 0:
            return (model, clip)

        # Get the full path to the LoRA file
        lora_path = folder_paths.get_full_path("loras", lora_name)
        lora = None
        
        # Check if we already have this LoRA loaded
        if self.loaded_lora is not None:
            if self.loaded_lora[0] == lora_path:
                lora = self.loaded_lora[1]
            else:
                del self.loaded_lora

        # Load the LoRA if not already loaded
        if lora is None:
            lora = comfy.utils.load_torch_file(lora_path, safe_load=True)
            self.loaded_lora = (lora_path, lora)

        # Apply the LoRA to the model and clip
        model_lora, clip_lora = comfy.sd.load_lora_for_models(model, clip, lora, strength_model, strength_clip)
        
        return (model_lora, clip_lora)

# The mappings for the node
NODE_CLASS_MAPPINGS = { "SliderLoraLoaderNode": SliderLoraLoaderNode }
NODE_DISPLAY_NAME_MAPPINGS = { "SliderLoraLoaderNode": "Slider Lora Loader" }
