// import { app } from "../../../../scripts/app.js";

// console.log("MY_NODES global appearance extension loading...");

// // Register our extension for global appearance customization
// app.registerExtension({
//     name: "MY_NODES.GlobalAppearance",

//     setup() {
//         console.log("MY_NODES global appearance setup complete");
//     },

//     nodeCreated(node) {
//         // Apply custom colors to MY_NODES (if needed)
//         if (node.constructor.category === "My Nodes") {
//             console.log("Applying colors to MY_NODES node:", node.comfyClass);
//             // Available color options that work well across themes:
//             // red, green, blue, brown, purple, yellow, pale_blue, cyan
//             // Choose the one that looks best in your themes:
//             node.color = LGraphCanvas.node_colors.pale_blue.color;
//             node.bgcolor = LGraphCanvas.node_colors.pale_blue.bgcolor;
//         }
//     }
// });

// Note: Currently commented out because ComfyUI's native theme adaptation
// works better than manual color overrides. Uncomment and customize if you
// need specific color schemes that differ from the default theme behavior.
