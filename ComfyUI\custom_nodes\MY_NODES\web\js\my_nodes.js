import { app } from "../../../../scripts/app.js";

console.log("MY_NODES extension loading...");

// Register our extension
app.registerExtension({
    name: "MY_NODES",

    setup() {
        console.log("MY_NODES extension setup complete");
    },

    nodeCreated(node) {
        // Apply theme-aware colors to MY_NODES (like other custom nodes do)
        if (node.constructor.category === "My Nodes") {
            console.log("Applying colors to MY_NODES node:", node.comfyClass);
            // Available color options that work well across themes:
            // red, green, blue, brown, purple, yellow, pale_blue, cyan
            // Choose the one that looks best in your themes:
            node.color = LGraphCanvas.node_colors.pale_blue.color;
            node.bgcolor = LGraphCanvas.node_colors.pale_blue.bgcolor;
        }
    }
});