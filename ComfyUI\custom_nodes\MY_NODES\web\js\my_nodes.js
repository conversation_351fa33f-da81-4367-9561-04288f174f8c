import { app } from "../../../scripts/app.js";

// Function to apply theme colors to MY_NODES
function applyMyNodesColors(node) {
    if (node.constructor.category === "My Nodes") {
        // Use ComfyUI's theme-aware color system
        // This automatically adapts to dark/light themes
        node.color = LGraphCanvas.node_colors.blue.color;
        node.bgcolor = LGraphCanvas.node_colors.blue.bgcolor;
    }
}

// Register our extension
app.registerExtension({
    name: "MY_NODES",

    setup() {
        // Listen for theme changes and reapply colors
        const originalSetCanvasTheme = app.canvas.setCanvasTheme;
        if (originalSetCanvasTheme) {
            app.canvas.setCanvasTheme = function(...args) {
                const result = originalSetCanvasTheme.apply(this, args);

                // Reapply colors to all MY_NODES after theme change
                if (app.graph && app.graph._nodes) {
                    app.graph._nodes.forEach(node => {
                        if (node.constructor.category === "My Nodes") {
                            applyMyNodesColors(node);
                        }
                    });
                    app.canvas.setDirty(true, true);
                }

                return result;
            };
        }
    },

    nodeCreated(node) {
        // Apply proper theme colors when node is created
        applyMyNodesColors(node);
    }
});