import { app } from "../../../scripts/app.js";

// Register our extension
app.registerExtension({
    name: "MY_NODES",
    
    async setup() {
        // Define custom category colors
        const categoryColors = {
            "My Nodes": { 
                bgcolor: "#3a5c7c", // Choose your preferred background color
                color: "#c9daea"    // Choose your preferred text color
            }
            // You can add more categories if needed
        };
        
        // Apply custom category colors
        if (app.canvas && app.canvas.default_connection_color_byType) {
            for (const [category, colors] of Object.entries(categoryColors)) {
                app.canvas.default_connection_color_byType[category] = colors;
            }
        }
    }
});