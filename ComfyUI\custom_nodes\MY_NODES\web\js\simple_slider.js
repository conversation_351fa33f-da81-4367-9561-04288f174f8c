import { app } from "../../../../scripts/app.js";


class SimpleSlider
{
    constructor(node)
    {
        this.node = node;

        // Basic properties (matching MXToolkit structure)
        this.node.properties = {
            value: 20,
            min: 0,
            max: 100,
            step: 1,
            decimals: 0,
            snap: true
        };

        // Track interaction state and position
        this.node.capture = false;
        this.node.intpos = { x: 0.2 }; // Normalized position (0-1)

        // Size matching MXToolkit
        this.node.size = [210, Math.floor(LiteGraph.NODE_SLOT_HEIGHT * 1.5)];
        
        // Constants for layout (matching MXToolkit)
        const fontsize = LiteGraph.NODE_SUBTEXT_SIZE;
        const shX = (this.node.slot_start_y || 0) + fontsize * 1.5;
        const shY = LiteGraph.NODE_SLOT_HEIGHT / 1.5;
        const shiftLeft = 15;
        const shiftRight = 60;

        // Hide original widgets and set type to hidden (like MXToolkit)
        for (let i = 0; i < 3; i++) { 
            this.node.widgets[i].hidden = true; 
            this.node.widgets[i].type = "hidden"; 
        }

        // onAdded handler (matching MXToolkit)
        this.node.onAdded = function() {
            this.outputs[0].name = this.outputs[0].localized_name = ""; // Hide output name
            this.widgets_start_y = -2.4e8 * LiteGraph.NODE_SLOT_HEIGHT; // Hide widgets completely
            this.intpos.x = Math.max(0, Math.min(1, (this.properties.value - this.properties.min) / (this.properties.max - this.properties.min)));
            if (this.size && this.size.length && this.size[1] > LiteGraph.NODE_SLOT_HEIGHT * 1.5) {
                this.size[1] = LiteGraph.NODE_SLOT_HEIGHT * 1.5;
            }
            this.outputs[0].type = (this.properties.decimals > 0) ? "FLOAT" : "INT";
        };

        // onConfigure handler (matching MXToolkit)
        this.node.onConfigure = function() {
            this.outputs[0].type = (this.properties.decimals > 0) ? "FLOAT" : "INT";
        };

        // Helper function to darken a color
        function darkenColor(color, amount = 0.3) {
            // Parse the color (handles both hex and rgb formats)
            let r, g, b, a = 1;

            if (color.startsWith('#')) {
                // Hex color
                const hex = color.slice(1);
                r = parseInt(hex.substr(0, 2), 16);
                g = parseInt(hex.substr(2, 2), 16);
                b = parseInt(hex.substr(4, 2), 16);
            } else if (color.startsWith('rgb')) {
                // RGB/RGBA color
                const matches = color.match(/rgba?\(([^)]+)\)/);
                if (matches) {
                    const values = matches[1].split(',').map(v => parseFloat(v.trim()));
                    r = values[0];
                    g = values[1];
                    b = values[2];
                    a = values[3] || 1;
                }
            } else {
                // Fallback for unknown formats
                return "rgba(20,20,20,0.5)";
            }

            // Darken by reducing RGB values
            r = Math.max(0, Math.floor(r * (1 - amount)));
            g = Math.max(0, Math.floor(g * (1 - amount)));
            b = Math.max(0, Math.floor(b * (1 - amount)));

            return `rgba(${r},${g},${b},${a})`;
        }

        // Draw the interactive slider (matching MXToolkit style)
        this.node.onDrawForeground = function(ctx) {
            if (this.flags.collapsed) return false;
            if (this.size[1] > LiteGraph.NODE_SLOT_HEIGHT * 1.5) this.size[1] = LiteGraph.NODE_SLOT_HEIGHT * 1.5;

            let dgt = parseInt(this.properties.decimals);

            // Draw slider track with darkened theme color
            const baseTrackColor = LiteGraph.NODE_DEFAULT_BOXCOLOR || "#444444";
            ctx.fillStyle = darkenColor(baseTrackColor, 0.4); // 40% darker
            ctx.beginPath();
            ctx.roundRect(shiftLeft, shY - 1, this.size[0] - shiftRight - shiftLeft, 4, 2);
            ctx.fill();

            // Draw slider handle (matching MXToolkit style)
            ctx.fillStyle = LiteGraph.NODE_TEXT_COLOR;
            ctx.beginPath();
            ctx.arc(shiftLeft + (this.size[0] - shiftRight - shiftLeft) * this.intpos.x, shY + 1, 7, 0, 2 * Math.PI, false);
            ctx.fill();

            // Draw handle outline
            ctx.lineWidth = 1.5;
            ctx.strokeStyle = this.bgcolor || LiteGraph.NODE_DEFAULT_BGCOLOR;
            ctx.beginPath();
            ctx.arc(shiftLeft + (this.size[0] - shiftRight - shiftLeft) * this.intpos.x, shY + 1, 5, 0, 2 * Math.PI, false);
            ctx.stroke();

            // Draw value text (matching MXToolkit positioning)
            ctx.fillStyle = LiteGraph.NODE_TEXT_COLOR;
            ctx.font = fontsize + "px Arial";
            ctx.textAlign = "center";
            ctx.fillText(this.properties.value.toFixed(dgt), this.size[0] - shiftRight + 24, shX);
        };

        // Mouse down handler (matching MXToolkit)
        this.node.onMouseDown = function(e) {
            if (e.canvasY - this.pos[1] < 0) return false;
            if (e.canvasX < this.pos[0] + shiftLeft - 5 || e.canvasX > this.pos[0] + this.size[0] - shiftRight + 5) return false;
            if (e.canvasY < this.pos[1] + shiftLeft - 5 || e.canvasY > this.pos[1] + this.size[1] - shiftLeft + 5) return false;

            this.capture = true;
            this.unlock = false;
            this.captureInput(true);
            this.valueUpdate(e);
            return true;
        };

        // Mouse move handler (matching MXToolkit)
        this.node.onMouseMove = function(e, pos, canvas) {
            if (!this.capture) return;
            if (canvas.pointer.isDown === false) { 
                this.onMouseUp(e); 
                return; 
            }
            this.valueUpdate(e);
        };

        // Mouse up handler (matching MXToolkit)
        this.node.onMouseUp = function(e) {
            if (!this.capture) return;
            this.capture = false;
            this.captureInput(false);
            
            // Update widgets (matching MXToolkit)
            this.widgets[0].value = Math.floor(this.properties.value);
            this.widgets[1].value = this.properties.value;
            this.widgets[2].value = (this.properties.decimals > 0) ? 1 : 0;
        };

        // Value update function (matching MXToolkit)
        this.node.valueUpdate = function(e) {
            let prevX = this.properties.value;
            let rn = Math.pow(10, this.properties.decimals);
            let vX = (e.canvasX - this.pos[0] - shiftLeft) / (this.size[0] - shiftRight - shiftLeft);

            if (e.ctrlKey) this.unlock = true;
            if (e.shiftKey !== this.properties.snap) {
                let step = this.properties.step / (this.properties.max - this.properties.min);
                vX = Math.round(vX / step) * step;
            }

            this.intpos.x = Math.max(0, Math.min(1, vX));
            this.properties.value = Math.round(rn * (this.properties.min + (this.properties.max - this.properties.min) * ((this.unlock) ? vX : this.intpos.x))) / rn;

            if (this.properties.value !== prevX) {
                this.setDirtyCanvas(true, false);
            }
        };

        // Property change handler (matching MXToolkit)
        this.node.onPropertyChanged = function(propName) {
            if (this.properties.step <= 0) this.properties.step = 1;
            if (isNaN(this.properties.value)) this.properties.value = this.properties.min;
            if (this.properties.min >= this.properties.max) this.properties.max = this.properties.min + this.properties.step;
            if ((propName === "min") && (this.properties.value < this.properties.min)) this.properties.value = this.properties.min;
            if ((propName === "max") && (this.properties.value > this.properties.max)) this.properties.value = this.properties.max;
            
            this.properties.decimals = Math.floor(this.properties.decimals);
            if (this.properties.decimals > 4) this.properties.decimals = 4;
            if (this.properties.decimals < 0) this.properties.decimals = 0;
            
            this.properties.value = Math.round(Math.pow(10, this.properties.decimals) * this.properties.value) / Math.pow(10, this.properties.decimals);
            this.intpos.x = Math.max(0, Math.min(1, (this.properties.value - this.properties.min) / (this.properties.max - this.properties.min)));
            
            this.outputs[0].type = (this.properties.decimals > 0) ? "FLOAT" : "INT";
            this.widgets[2].value = (this.properties.decimals > 0) ? 1 : 0;
            this.widgets[1].value = this.properties.value;
            this.widgets[0].value = Math.floor(this.properties.value);
        };

        // onSelected handler (matching MXToolkit)
        this.node.onSelected = function(e) { this.onMouseUp(e); };
        
        // computeSize handler (matching MXToolkit)
        this.node.computeSize = () => [LiteGraph.NODE_WIDTH, Math.floor(LiteGraph.NODE_SLOT_HEIGHT * 1.5)];
    }
}

app.registerExtension({
    name: "SimpleSliderUI",
    async beforeRegisterNodeDef(nodeType, nodeData, _app) {
        if (nodeData.name === "SimpleSliderNode") {
            const onNodeCreated = nodeType.prototype.onNodeCreated;
            nodeType.prototype.onNodeCreated = function() {
                if (onNodeCreated) onNodeCreated.apply(this, arguments);
                this.simpleSlider = new SimpleSlider(this);
            };
        }
    }
});
