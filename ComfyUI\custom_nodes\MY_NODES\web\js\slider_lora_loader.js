import { app } from "../../../../scripts/app.js";

// Slider LoRA Loader UI - uses the same slider implementation as SimpleSlider
class SliderLoraLoader {
    constructor(node) {
        this.node = node;
        
        // Set up properties for LoRA strength (0.0 to 2.0 range, default 1.0)
        this.node.properties = {
            value: 1.0,        // Current LoRA strength
            min: 0.0,          // Minimum strength
            max: 2.0,          // Maximum strength  
            step: 0.01,        // Step increment
            decimals: 2,       // Show 2 decimal places
            snap: false        // Disable step snapping for smooth control
        };

        // Calculate initial position based on value
        this.node.intpos = { x: (this.node.properties.value - this.node.properties.min) / (this.node.properties.max - this.node.properties.min) };

        // Constants for layout (matching SimpleSlider)
        const fontsize = LiteGraph.NODE_SUBTEXT_SIZE;
        const shY = LiteGraph.NODE_SLOT_HEIGHT / 1.5;
        const shiftLeft = 15;
        const shiftRight = 60;

        // Hide original widgets and set type to hidden
        // Find and hide the strength-related widgets (skip model, clip, lora_name)
        if (this.node.widgets && this.node.widgets.length > 2) {
            for (let i = 2; i < this.node.widgets.length; i++) {
                if (this.node.widgets[i]) {
                    this.node.widgets[i].hidden = true;
                    this.node.widgets[i].type = "hidden";
                }
            }
        }

        // Set up node configuration
        this.node.onAdded = function() {
            this.outputs[0].name = this.outputs[0].localized_name = ""; // Hide output names
            this.outputs[1].name = this.outputs[1].localized_name = "";
            this.widgets_start_y = -2.4e8 * LiteGraph.NODE_SLOT_HEIGHT; // Hide widgets completely
            this.size = [280, Math.floor(LiteGraph.NODE_SLOT_HEIGHT * 2.2)]; // Wider for LoRA name + slider
        };

        this.node.onConfigure = function() {
            // Always use float for LoRA strength
            this.outputs[0].type = "MODEL";
            this.outputs[1].type = "CLIP";
        };

        // Draw the interactive slider (same as SimpleSlider but for LoRA strength)
        this.node.onDrawForeground = function(ctx) {
            if (this.flags.collapsed) return false;
            if (this.size[1] > LiteGraph.NODE_SLOT_HEIGHT * 2.2) this.size[1] = LiteGraph.NODE_SLOT_HEIGHT * 2.2;
            
            let dgt = parseInt(this.properties.decimals);

            // Draw slider track - theme-intelligent darkening
            ctx.fillStyle = "rgba(0,0,0,0.3)";
            ctx.beginPath();
            ctx.roundRect(shiftLeft, shY - 1, this.size[0] - shiftRight - shiftLeft, 4, 2);
            ctx.fill();

            // Draw intelligent progress bar (handles negative/positive ranges)
            const trackWidth = this.size[0] - shiftRight - shiftLeft;
            const hasNegativeRange = this.properties.min < 0 && this.properties.max > 0;
            
            if (hasNegativeRange) {
                // Calculate zero position for negative/positive ranges
                const zeroPos = shiftLeft + trackWidth * (-this.properties.min / (this.properties.max - this.properties.min));
                const currentPos = shiftLeft + trackWidth * this.intpos.x;
                
                if (this.properties.value >= 0) {
                    // Positive value: white progress from zero to handle
                    const progressWidth = Math.abs(currentPos - zeroPos);
                    if (progressWidth > 1) {
                        ctx.fillStyle = LiteGraph.NODE_TEXT_COLOR;
                        ctx.beginPath();
                        ctx.roundRect(zeroPos, shY - 1, progressWidth, 4, 2);
                        ctx.fill();
                        
                        // Standard darkening overlay for positive values
                        ctx.fillStyle = "rgba(0,0,0,0.15)";
                        ctx.beginPath();
                        ctx.roundRect(zeroPos, shY - 1, progressWidth, 4, 2);
                        ctx.fill();
                    }
                    
                    // Draw zero indicator with positive progress color
                    ctx.fillStyle = LiteGraph.NODE_TEXT_COLOR;
                    ctx.beginPath();
                    ctx.arc(zeroPos, shY + 1, 3, 0, 2 * Math.PI, false);
                    ctx.fill();
                } else {
                    // Negative value: red progress from handle to zero
                    const progressWidth = Math.abs(zeroPos - currentPos);
                    if (progressWidth > 1) {
                        ctx.fillStyle = LiteGraph.NODE_TEXT_COLOR;
                        ctx.beginPath();
                        ctx.roundRect(currentPos, shY - 1, progressWidth, 4, 2);
                        ctx.fill();
                        
                        // Red tint for negative values
                        ctx.fillStyle = "rgba(100,0,0,0.3)";
                        ctx.beginPath();
                        ctx.roundRect(currentPos, shY - 1, progressWidth, 4, 2);
                        ctx.fill();
                    }
                    
                    // Draw zero indicator with red tint but lighter than progress bar
                    ctx.fillStyle = LiteGraph.NODE_TEXT_COLOR;
                    ctx.beginPath();
                    ctx.arc(zeroPos, shY + 1, 3, 0, 2 * Math.PI, false);
                    ctx.fill();
                    
                    // Add lighter red tint to zero indicator
                    ctx.fillStyle = "rgba(100,0,0,0.15)";
                    ctx.beginPath();
                    ctx.arc(zeroPos, shY + 1, 3, 0, 2 * Math.PI, false);
                    ctx.fill();
                }
            } else {
                // Standard progress bar for positive-only ranges
                const progressWidth = trackWidth * this.intpos.x;
                if (progressWidth > 0) {
                    ctx.fillStyle = LiteGraph.NODE_TEXT_COLOR;
                    ctx.beginPath();
                    ctx.roundRect(shiftLeft, shY - 1, progressWidth, 4, 2);
                    ctx.fill();
                    
                    // Standard darkening overlay
                    ctx.fillStyle = "rgba(0,0,0,0.15)";
                    ctx.beginPath();
                    ctx.roundRect(shiftLeft, shY - 1, progressWidth, 4, 2);
                    ctx.fill();
                }
            }

            // Draw slider handle
            ctx.fillStyle = LiteGraph.NODE_TEXT_COLOR;
            ctx.beginPath();
            ctx.arc(shiftLeft + (this.size[0] - shiftRight - shiftLeft) * this.intpos.x, shY + 1, 7, 0, 2 * Math.PI, false);
            ctx.fill();

            // Draw handle outline
            ctx.lineWidth = 1.5;
            ctx.strokeStyle = this.bgcolor || LiteGraph.NODE_DEFAULT_BGCOLOR;
            ctx.beginPath();
            ctx.arc(shiftLeft + (this.size[0] - shiftRight - shiftLeft) * this.intpos.x, shY + 1, 5, 0, 2 * Math.PI, false);
            ctx.stroke();

            // Draw value text (centered with track)
            ctx.fillStyle = LiteGraph.NODE_TEXT_COLOR;
            ctx.font = fontsize + "px Arial";
            ctx.textAlign = "center";
            ctx.fillText(this.properties.value.toFixed(dgt), this.size[0] - shiftRight + 24, shY + 1);
        };

        // Mouse down handler (same as SimpleSlider)
        this.node.onMouseDown = function(e) {
            if (e.canvasY - this.pos[1] < 0) return false;
            if (e.canvasX < this.pos[0] + shiftLeft - 5 || e.canvasX > this.pos[0] + this.size[0] - shiftRight + 5) return false;
            
            this.capture = true;
            this.unlock = false;
            this.captureInput(true);
            this.valueUpdate(e);
            return true;
        };

        // Mouse move handler
        this.node.onMouseMove = function(e, pos, canvas) {
            if (!this.capture) return;
            if (canvas.pointer.isDown === false) { 
                this.onMouseUp(e); 
                return; 
            }
            this.valueUpdate(e);
        };

        // Mouse up handler - updates the backend widgets
        this.node.onMouseUp = function(e) {
            if (!this.capture) return;
            this.capture = false;
            this.captureInput(false);
            
            // Update the hidden backend widgets for LoRA strength
            if (this.widgets && this.widgets.length >= 5) {
                this.widgets[2].value = this.properties.value;  // strength_float
                this.widgets[3].value = Math.floor(this.properties.value * 100);  // strength_int (as percentage)
                this.widgets[4].value = 1;  // use_float (always use float for LoRA)
            }
        };

        // Value update function (same as SimpleSlider)
        this.node.valueUpdate = function(e) {
            let prevX = this.properties.value;
            let rn = Math.pow(10, this.properties.decimals);
            
            // Convert mouse position to normalized value (0-1)
            let vX = (e.canvasX - this.pos[0] - shiftLeft) / (this.size[0] - shiftRight - shiftLeft);
            
            // Handle special interaction modes
            if (e.ctrlKey) this.unlock = true;  // Precision mode
            if (e.shiftKey !== this.properties.snap) {  // Step snapping
                let step = this.properties.step / (this.properties.max - this.properties.min);
                vX = Math.round(vX / step) * step;
            }
            
            // Clamp and convert to actual value
            this.intpos.x = Math.max(0, Math.min(1, vX));
            this.properties.value = Math.round(rn * (this.properties.min + (this.properties.max - this.properties.min) * this.intpos.x)) / rn;
            
            // Trigger redraw if value changed
            if (this.properties.value !== prevX) {
                this.setDirtyCanvas(true, false);
            }
        };
    }
}

// Register the extension
app.registerExtension({
    name: "SliderLoraLoaderUI",
    async beforeRegisterNodeDef(nodeType, nodeData, _app) {
        if (nodeData.name === "SliderLoraLoaderNode") {
            const onNodeCreated = nodeType.prototype.onNodeCreated;
            nodeType.prototype.onNodeCreated = function() {
                if (onNodeCreated) onNodeCreated.apply(this, arguments);
                this.sliderLoraLoader = new SliderLoraLoader(this);
            };
        }
    }
});

console.log("SliderLoraLoaderUI extension loaded");
