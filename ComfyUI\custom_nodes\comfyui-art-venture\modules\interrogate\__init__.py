from .blip_node import <PERSON><PERSON><PERSON><PERSON><PERSON>, Blip<PERSON>ap<PERSON>, DownloadAndLoadBlip
from .danbooru import DeepDanbooruCaption

NODE_CLASS_MAPPINGS = {
    "BLIPLoader": <PERSON><PERSON><PERSON>oa<PERSON>,
    "BLIPCaption": <PERSON><PERSON><PERSON>ap<PERSON>,
    "DownloadAndLoadBlip": DownloadAndLoadBlip,
    "DeepDanbooruCaption": DeepD<PERSON>booruCaption,
}
NODE_DISPLAY_NAME_MAPPINGS = {
    "BLIPLoader": "BLIP Loader",
    "BLIPCaption": "BLIP Caption",
    "DownloadAndLoadBlip": "Download and Load BLIP Model",
    "DeepDanbooruCaption": "Deep Danbooru Caption",
}

__all__ = ["NODE_CLASS_MAPPINGS", "NODE_DISPLAY_NAME_MAPPINGS"]
