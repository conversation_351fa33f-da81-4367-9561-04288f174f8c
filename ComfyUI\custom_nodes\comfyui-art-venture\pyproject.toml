[project]
name = "comfyui-art-venture"
description = "A comprehensive set of custom nodes for ComfyUI, focusing on utilities for image processing, JSON manipulation, model operations and working with object via URLs"
version = "1.0.7"
license = "LICENSE"
dependencies = ["timm==0.6.13", "transformers", "fairscale", "pycocoevalcap", "opencv-python", "qrcode[pil]", "pytorch_lightning", "kornia", "pydantic", "segment_anything", "boto3>=1.34.101"]

[project.urls]
Repository = "https://github.com/sipherxyz/comfyui-art-venture"
#  Used by Comfy Registry https://comfyregistry.org

[tool.comfy]
PublisherId = "protogaia"
DisplayName = "ComfyUI ArtVenture"
Icon = "https://cdn.protogaia.com/assets/gaia.png"
