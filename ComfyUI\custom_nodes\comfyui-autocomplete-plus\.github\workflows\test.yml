name: Run Tests and Linters

on: [push, pull_request]

jobs:
  test:
    name: jest
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run tests
        run: npm test

  linters:
    name: stylelint
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v1
      - uses: actions-hub/stylelint@master
        env:
          PATTERN : 'web/css/*.css'