[project]
name = "comfyui-autocomplete-plus"
description = "Autocomplete and Related Tag display for ComfyUI"
version = "1.1.0"
license = {file = "LICENSE"}
dependencies = ["",]

[project.urls]
Repository = "https://github.com/newtextdoc1111/ComfyUI-Autocomplete-Plus"
#  Used by Comfy Registry https://comfyregistry.org

[tool.comfy]
PublisherId = "newtextdoc1111"
DisplayName = "ComfyUI-Autocomplete-Plus"
Icon = ""
