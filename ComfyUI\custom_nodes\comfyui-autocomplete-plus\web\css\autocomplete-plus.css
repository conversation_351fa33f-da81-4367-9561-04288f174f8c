/* Light Theme */
 body {
  --autocomplete-plus-text-color-cat-blue: var(--p-blue-600);
  --autocomplete-plus-text-color-cat-red: var(--p-red-600);
  --autocomplete-plus-text-color-cat-purple: var(--p-purple-600);
  --autocomplete-plus-text-color-cat-green: var(--p-green-600);
  --autocomplete-plus-text-color-cat-yellow: var(--p-yellow-600);
  --autocomplete-plus-text-color-cat-gray: var(--p-gray-600);
  --autocomplete-plus-text-color-cat-sky: var(--p-sky-500);
  --autocomplete-plus-text-color-cat-orange: var(--p-orange-600);
  --autocomplete-plus-text-color-cat-white: var(--p-neutral-700);
  --autocomplete-plus-text-color-disabled: var(--p-gray-400);
}

/* Dark Theme */
body.dark-theme {
  --autocomplete-plus-text-color-cat-blue: var(--p-blue-400);
  --autocomplete-plus-text-color-cat-red: var(--p-red-400);
  --autocomplete-plus-text-color-cat-purple: var(--p-purple-400);
  --autocomplete-plus-text-color-cat-green: var(--p-green-400);
  --autocomplete-plus-text-color-cat-yellow: var(--p-yellow-400);
  --autocomplete-plus-text-color-cat-gray: var(--p-gray-400);
  --autocomplete-plus-text-color-cat-sky: var(--p-sky-300);
  --autocomplete-plus-text-color-cat-orange: var(--p-orange-400);
  --autocomplete-plus-text-color-cat-white: var(--p-neutral-200);
  --autocomplete-plus-text-color-disabled: var(--p-gray-500);
}

#autocomplete-plus-root {
  position: absolute;
  z-index: 1000;
  top: 0;
  left: 0;
  display: none;
  width: fit-content;
  background-color: var(--comfy-input-bg);
  color: var(--input-text);
}

#autocomplete-plus-root .danbooru[data-tag-category="general"] {
  color: var(--autocomplete-plus-text-color-cat-blue);
}

#autocomplete-plus-root .danbooru[data-tag-category="artist"] {
  color: var(--autocomplete-plus-text-color-cat-red);
}

#autocomplete-plus-root .danbooru[data-tag-category="copyright"] {
  color: var(--autocomplete-plus-text-color-cat-purple);
}

#autocomplete-plus-root .danbooru[data-tag-category="character"] {
  color: var(--autocomplete-plus-text-color-cat-green);
}

#autocomplete-plus-root .danbooru[data-tag-category="meta"] {
  color: var(--autocomplete-plus-text-color-cat-yellow);
}

#autocomplete-plus-root .danbooru[data-tag-category="unknown"] {
  color: var(--autocomplete-plus-text-color-cat-gray);
}

#autocomplete-plus-root .e621[data-tag-category="general"] {
  color: var(--autocomplete-plus-text-color-cat-sky);
}

#autocomplete-plus-root .e621[data-tag-category="artist"] {
  color: var(--autocomplete-plus-text-color-cat-orange);
}

#autocomplete-plus-root .e621[data-tag-category="copyright"] {
  color: var(--autocomplete-plus-text-color-cat-purple);
}

#autocomplete-plus-root .e621[data-tag-category="character"] {
  color: var(--autocomplete-plus-text-color-cat-green);
}

#autocomplete-plus-root .e621[data-tag-category="species"] {
  color: var(--autocomplete-plus-text-color-cat-red);
}

#autocomplete-plus-root .e621[data-tag-category="meta"] {
  color: var(--autocomplete-plus-text-color-cat-white);
}

#autocomplete-plus-root .e621[data-tag-category="lore"] {
  color: var(--autocomplete-plus-text-color-cat-red);
}

#autocomplete-plus-root .e621[data-tag-category="unknown"] {
  color: var(--autocomplete-plus-text-color-cat-purple);
}

#autocomplete-plus-list {
  display: grid;
  box-shadow: 0 2px 8px rgb(0 0 0 / 30%);
  grid-auto-rows: auto;
  grid-template-columns: max-content 1fr auto auto;
  overflow-y: auto;
}

.autocomplete-plus-item {
  display: grid;
  cursor: pointer;
  grid-column: 1 / -1;
  grid-template-columns: subgrid;
}

.autocomplete-plus-item span {
  align-content: center;
  padding: 4px 8px;
  border-bottom: 1px solid var(--border-color);
}

/* Alternating row colors */
.autocomplete-plus-item:nth-child(even) span {
  background-color: var(--comfy-menu-bg);
}

.autocomplete-plus-item:nth-child(odd) span {
  background-color: var(--comfy-menu-secondary-bg);
}

.autocomplete-plus-item.selected span {
  background-color: var(--border-color);
}

.autocomplete-plus-item:hover span {
  background-color: var(--comfy-hover-bg);
}

.autocomplete-plus-item .autocomplete-plus-tag-name {
  white-space: nowrap;
}

.autocomplete-plus-item .autocomplete-plus-tag-icon-svg {
  width: 1em;
  height: 1em;
  vertical-align: middle;
}

.autocomplete-plus-tag-name.autocomplete-plus-already-exists  {
  color: var(--autocomplete-plus-text-color-disabled);
}

.autocomplete-plus-item .autocomplete-plus-alias {
  overflow: hidden;
  color: var(--descrip-text);
  text-overflow: ellipsis;
  white-space: nowrap;
}

.autocomplete-plus-item .autocomplete-plus-tag-count {
  text-align: right;
}

/* Related Tags UI Styles */
#related-tags-root {
  position: absolute;
  z-index: 1000;
  top: 0;
  left: 0;
  display: none;
  width: fit-content;
  background-color: var(--comfy-input-bg);
  color: var(--input-text);
}

#related-tags-root .danbooru[data-tag-category="general"] {
  color: var(--autocomplete-plus-text-color-cat-blue);
}

#related-tags-root .danbooru[data-tag-category="artist"] {
  color: var(--autocomplete-plus-text-color-cat-red);
}

#related-tags-root .danbooru[data-tag-category="copyright"] {
  color: var(--autocomplete-plus-text-color-cat-purple);
}

#related-tags-root .danbooru[data-tag-category="character"] {
  color: var(--autocomplete-plus-text-color-cat-green);
}

#related-tags-root .danbooru[data-tag-category="meta"] {
  color: var(--autocomplete-plus-text-color-cat-yellow);
}

#related-tags-root .danbooru[data-tag-category="unknown"] {
  color: var(--autocomplete-plus-text-color-cat-gray);
}

#related-tags-root .e621[data-tag-category="general"] {
  color: var(--autocomplete-plus-text-color-cat-sky);
}

#related-tags-root .e621[data-tag-category="artist"] {
  color: var(--autocomplete-plus-text-color-cat-orange);
}

#related-tags-root .e621[data-tag-category="copyright"] {
  color: var(--autocomplete-plus-text-color-cat-purple);
}

#related-tags-root .e621[data-tag-category="character"] {
  color: var(--autocomplete-plus-text-color-cat-green);
}

#related-tags-root .e621[data-tag-category="species"] {
  color: var(--autocomplete-plus-text-color-cat-red);
}

#related-tags-root .e621[data-tag-category="meta"] {
  color: var(--autocomplete-plus-text-color-cat-white);
}

#related-tags-root .e621[data-tag-category="lore"] {
  color: var(--autocomplete-plus-text-color-cat-red);
}

#related-tags-root .e621[data-tag-category="unknown"] {
  color: var(--autocomplete-plus-text-color-cat-gray);
}

#related-tags-header {
  position: sticky;
  z-index: 1;
  top: 0;
  display: flex;
  align-items: center;
  padding: 8px;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--comfy-menu-bg);
  color: var(--descrip-text);
  gap: 8px;
}

.related-tags-header-text-container {
  display:flex;
  flex: 1;
  flex-wrap: wrap;
  align-items: center;
  gap: 12px;
}

.related-tags-header-tag-text {
  padding: 2px 0;
  line-height: 1.25em;
}

.related-tags-header-tag-name {
  font-weight: bold;
  word-break: break-all;
}

.related-tags-header-tag-name .autocomplete-plus-tag-icon-svg {
  width: 1em;
  height: 1em;
  vertical-align: middle;
}

.related-tags-header-tag-alias {
    overflow: auto;
    max-height: 5em;
    padding: 2px 4px;
    border-radius: 4px;
    background-color: var(--bg-color);
    color: var(--descrip-text);
    line-height: 1.25em;
}

.related-tags-header-controls {
  margin-left: auto;
}

.related-tags-header-controls button {
  border: none;
  background: none;
  color: var(--input-text);
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.related-tags-header-controls button:hover {
  background-color: var(--border-color);
}

#related-tags-list {
  display: grid;
  box-shadow: 0 2px 8px rgb(0 0 0 / 30%);
  grid-auto-rows: auto;
  grid-template-columns: max-content 1fr auto auto;
  overflow-y: auto;
}

.related-tag-item {
  display: grid;
  cursor: pointer;
  grid-column: 1 / -1;
  grid-template-columns: subgrid;
}

/* stylelint-disable-next-line no-descending-specificity */
.related-tag-item span {
  align-content: center;
  padding: 4px 8px;
  border-bottom: 1px solid var(--border-color);
}

.related-tag-item:nth-child(even) span {
  background-color: var(--comfy-menu-bg);
}

.related-tag-item:nth-child(odd) span {
  background-color: var(--comfy-menu-secondary-bg);
}

.related-tag-item:hover span {
  background-color: var(--p-form-field-filled-hover-background);
}

.related-tag-item.selected span {
  background-color: var(--border-color);
}

.related-tag-item .related-tag-name {
  white-space: nowrap;
}

.related-tag-item .related-tag-name.related-tag-already-exists  {
  color: var(--autocomplete-plus-text-color-disabled);
}

.related-tag-item .related-tag-alias {
  overflow: hidden;
  color: var(--descrip-text);
  text-overflow: ellipsis;
  white-space: nowrap;
}

.related-tag-item .related-tag-similarity {
  color: var(--descrip-text);
  text-align: right;
  white-space: nowrap;
}

.related-tags-message {
  width: 100%;
  padding: 12px;
  font-style: italic;
  grid-column: 1 / -1;
  text-align: center;
}

.related-tags-empty {
  padding: 12px;
  color: var(--error-text);
  font-style: italic;
  text-align: center;
}