.pysssss-lds-ring {
	display: inline-block;
	position: absolute;
	width: 80px;
	height: 80px;
}
.pysssss-lds-ring div {
	box-sizing: border-box;
	display: block;
	position: absolute;
	width: 64px;
	height: 64px;
	margin: 8px;
	border: 5px solid #fff;
	border-radius: 50%;
	animation: lds-ring 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
	border-color: #fff transparent transparent transparent;
}
.pysssss-lds-ring div:nth-child(1) {
	animation-delay: -0.45s;
}
.pysssss-lds-ring div:nth-child(2) {
	animation-delay: -0.3s;
}
.pysssss-lds-ring div:nth-child(3) {
	animation-delay: -0.15s;
}
@keyframes lds-ring {
	0% {
		transform: rotate(0deg);
	}
	100% {
		transform: rotate(360deg);
	}
}
