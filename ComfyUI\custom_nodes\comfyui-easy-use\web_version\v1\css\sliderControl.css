.easyuse-slider{
    width:100%;
    height:100%;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    position: relative;
}
.easyuse-slider-item{
    height: inherit;
    min-width: 25px;
    justify-content: center;
    display: flex;
    flex-direction: column;
    align-items: center;
}
.easyuse-slider-item.positive .easyuse-slider-item-label{
    color: var(--success-color);
}
.easyuse-slider-item.negative .easyuse-slider-item-label{
    color: var(--error-color);
}
.easyuse-slider-item-input{
    height:15px;
    font-size: 10px;
    color: var(--input-text);
}
.easyuse-slider-item-label{
    height:15px;
    border: none;
    color: var(--descrip-text);
    font-size: 8px;
}
.easyuse-slider-item-scroll {
    width: 5px;
    height: calc(100% - 30px);
    background: var(--comfy-input-bg);
    border-radius: 10px;
    position: relative;
}
.easyuse-slider-item-bar{
    width: 10px;
    height: 10px;
    background: linear-gradient(to bottom, var(--input-text), var(--descrip-text));
    border-radius:100%;
    box-shadow: 0 2px 10px var(--bg-color);
    position: absolute;
    top: 0;
    left:-2.5px;
    cursor: pointer;
    z-index:1;
}
.easyuse-slider-item-area{
    width: 100%;
    border-radius:20px;
    position: absolute;
    bottom: 0;
    background: var(--input-text);
    z-index:0;
}
.easyuse-slider-item.positive .easyuse-slider-item-area{
    background: var(--success-color);
}
.easyuse-slider-item.negative .easyuse-slider-item-area{
    background: var(--error-color);
}
