## ComfyUI-Manager: installing dependencies done.
[2025-06-26 14:50:16.341] ** ComfyUI startup time: 2025-06-26 14:50:16.341
[2025-06-26 14:50:16.341] ** Platform: Windows
[2025-06-26 14:50:16.341] ** Python version: 3.12.9 (main, Feb 12 2025, 14:52:31) [MSC v.1942 64 bit (AMD64)]
[2025-06-26 14:50:16.341] ** Python executable: D:\stable_diffusion\ComfyUI\.venv\Scripts\python.exe
[2025-06-26 14:50:16.341] ** ComfyUI Path: C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI
[2025-06-26 14:50:16.341] ** ComfyUI Base Folder Path: C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI
[2025-06-26 14:50:16.341] ** User directory: D:\stable_diffusion\ComfyUI\user
[2025-06-26 14:50:16.341] ** ComfyUI-Manager config path: D:\stable_diffusion\ComfyUI\user\default\ComfyUI-Manager\config.ini
[2025-06-26 14:50:16.341] ** Log path: D:\stable_diffusion\ComfyUI\user\comfyui.log
