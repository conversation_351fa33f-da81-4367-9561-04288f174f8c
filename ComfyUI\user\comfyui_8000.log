## ComfyUI-Manager: installing dependencies done.
[2025-06-26 17:00:06.509] ** ComfyUI startup time: 2025-06-26 17:00:06.509
[2025-06-26 17:00:06.509] ** Platform: Windows
[2025-06-26 17:00:06.509] ** Python version: 3.12.9 (main, Feb 12 2025, 14:52:31) [MSC v.1942 64 bit (AMD64)]
[2025-06-26 17:00:06.509] ** Python executable: D:\stable_diffusion\ComfyUI\.venv\Scripts\python.exe
[2025-06-26 17:00:06.509] ** ComfyUI Path: C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI
[2025-06-26 17:00:06.509] ** ComfyUI Base Folder Path: C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI
[2025-06-26 17:00:06.509] ** User directory: D:\stable_diffusion\ComfyUI\user
[2025-06-26 17:00:06.554] ** ComfyUI-Manager config path: D:\stable_diffusion\ComfyUI\user\default\ComfyUI-Manager\config.ini
[2025-06-26 17:00:06.554] ** Log path: D:\stable_diffusion\ComfyUI\user\comfyui.log
[ComfyUI-Manager] Failed to restore comfyui-frontend-package
[2025-06-26 17:00:08.381] expected str, bytes or os.PathLike object, not NoneType
[2025-06-26 17:00:08.382] 
Prestartup times for custom nodes:
[2025-06-26 17:00:08.382]    5.0 seconds: C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI\custom_nodes\ComfyUI-Manager
[2025-06-26 17:00:08.382] 
