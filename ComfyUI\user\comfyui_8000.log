## ComfyUI-Manager: installing dependencies done.
[2025-06-26 19:59:45.929] ** ComfyUI startup time: 2025-06-26 19:59:45.929
[2025-06-26 19:59:45.929] ** Platform: Windows
[2025-06-26 19:59:45.929] ** Python version: 3.12.9 (main, Feb 12 2025, 14:52:31) [MSC v.1942 64 bit (AMD64)]
[2025-06-26 19:59:45.929] ** Python executable: D:\stable_diffusion\ComfyUI\.venv\Scripts\python.exe
[2025-06-26 19:59:45.929] ** ComfyUI Path: C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI
[2025-06-26 19:59:45.930] ** ComfyUI Base Folder Path: C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI
[2025-06-26 19:59:45.930] ** User directory: D:\stable_diffusion\ComfyUI\user
[2025-06-26 19:59:45.975] ** ComfyUI-Manager config path: D:\stable_diffusion\ComfyUI\user\default\ComfyUI-Manager\config.ini
[2025-06-26 19:59:45.975] ** Log path: D:\stable_diffusion\ComfyUI\user\comfyui.log
[ComfyUI-Manager] Failed to restore comfyui-frontend-package
[2025-06-26 19:59:47.721] expected str, bytes or os.PathLike object, not NoneType
[2025-06-26 19:59:47.722] 
Prestartup times for custom nodes:
[2025-06-26 19:59:47.722]    4.6 seconds: C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI\custom_nodes\ComfyUI-Manager
[2025-06-26 19:59:47.722] 
[2025-06-26 19:59:49.837] Checkpoint files will always be loaded safely.
[2025-06-26 19:59:51.138] Total VRAM 6144 MB, total RAM 28524 MB
[2025-06-26 19:59:51.138] pytorch version: 2.7.0+cu128
[2025-06-26 19:59:51.138] Set vram state to: NORMAL_VRAM
[2025-06-26 19:59:51.141] Device: cuda:0 NVIDIA GeForce RTX 3060 Laptop GPU : cudaMallocAsync
[2025-06-26 19:59:53.070] Using pytorch attention
[2025-06-26 19:59:54.898] Python version: 3.12.9 (main, Feb 12 2025, 14:52:31) [MSC v.1942 64 bit (AMD64)]
[2025-06-26 19:59:54.898] ComfyUI version: 0.3.42
[2025-06-26 19:59:55.060] [Prompt Server] web root: C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI\web_custom_versions\desktop_app
[2025-06-26 19:59:56.255] Adding D:\stable_diffusion\ComfyUI\custom_nodes to sys.path
[2025-06-26 19:59:56.255] Adding C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI\custom_nodes to sys.path
[2025-06-26 19:59:56.480] D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-art-venture\modules\utils.py:9: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  import pkg_resources
[2025-06-26 19:59:56.619] Could not find efficiency nodes
[2025-06-26 19:59:56.672] [36;20m[comfyui_controlnet_aux] | INFO -> Using ckpts path: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux\ckpts[0m
[2025-06-26 19:59:56.673] [36;20m[comfyui_controlnet_aux] | INFO -> Using symlinks: False[0m
[2025-06-26 19:59:56.673] [36;20m[comfyui_controlnet_aux] | INFO -> Using ort providers: ['CUDAExecutionProvider', 'DirectMLExecutionProvider', 'OpenVINOExecutionProvider', 'ROCMExecutionProvider', 'CPUExecutionProvider', 'CoreMLExecutionProvider'][0m
[2025-06-26 19:59:56.998] D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux\node_wrappers\dwpose.py:26: UserWarning: DWPose: Onnxruntime not found or doesn't come with acceleration providers, switch to OpenCV with CPU device. DWPose might run very slowly
  warnings.warn("DWPose: Onnxruntime not found or doesn't come with acceleration providers, switch to OpenCV with CPU device. DWPose might run very slowly")
[2025-06-26 19:59:57.027] Loaded ControlNetPreprocessors nodes from D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux
[2025-06-26 19:59:57.028] Could not find AdvancedControlNet nodes
[2025-06-26 19:59:57.031] Could not find AnimateDiff nodes
[2025-06-26 19:59:57.032] Could not find IPAdapter nodes
[2025-06-26 19:59:57.039] Could not find VideoHelperSuite nodes
[2025-06-26 19:59:57.041] Could not load ImpactPack nodes Could not find ImpactPack nodes
[2025-06-26 19:59:58.071] [34m[ComfyUI-Easy-Use] server: [0mv1.3.0 [92mLoaded[0m
[2025-06-26 19:59:58.071] [34m[ComfyUI-Easy-Use] web root: [0mD:\stable_diffusion\ComfyUI\custom_nodes\comfyui-easy-use\web_version/v2 [92mLoaded[0m
[2025-06-26 19:59:58.649] [06/26/25 19:59:58] WARNING  Your inference package version      __init__.py:41
[2025-06-26 19:59:58.649]                              0.50.4 is out of date! Please                     
[2025-06-26 19:59:58.649]                              upgrade to version 0.51.0 of                      
[2025-06-26 19:59:58.649]                              inference for the latest features                 
[2025-06-26 19:59:58.649]                              and bug fixes by running `pip                     
[2025-06-26 19:59:58.649]                              install --upgrade inference`.                     
[2025-06-26 19:59:59.059] [34mComfyUI-ImageCompare Nodes: [92mLoaded[0m
[2025-06-26 19:59:59.063] ### Loading: ComfyUI-Impact-Pack (V8.15.3)
[2025-06-26 19:59:59.119] [Impact Pack] Wildcards loading done.
[2025-06-26 19:59:59.131] ### Loading: ComfyUI-Impact-Subpack (V1.3.2)
[2025-06-26 19:59:59.132] [Impact Pack/Subpack] Using folder_paths to determine whitelist path: D:\stable_diffusion\ComfyUI\user\default\ComfyUI-Impact-Subpack\model-whitelist.txt
[2025-06-26 19:59:59.132] [Impact Pack/Subpack] Ensured whitelist directory exists: D:\stable_diffusion\ComfyUI\user\default\ComfyUI-Impact-Subpack
[2025-06-26 19:59:59.132] [Impact Pack/Subpack] Loaded 0 model(s) from whitelist: D:\stable_diffusion\ComfyUI\user\default\ComfyUI-Impact-Subpack\model-whitelist.txt
[2025-06-26 19:59:59.137] [Impact Subpack] ultralytics_bbox: D:\stable_diffusion\ComfyUI\models\ultralytics\bbox
[2025-06-26 19:59:59.137] [Impact Subpack] ultralytics_segm: D:\stable_diffusion\ComfyUI\models\ultralytics\segm
[2025-06-26 19:59:59.137] [Impact Subpack] ultralytics_bbox: D:\stable_diffusion\ComfyUI\models\ultralytics\bbox
[2025-06-26 19:59:59.137] [Impact Subpack] ultralytics_segm: D:\stable_diffusion\ComfyUI\models\ultralytics\segm
[2025-06-26 19:59:59.190] Found LoRA roots:
 - D:/stable_diffusion/1_Models/SDXL/loras
 - D:/stable_diffusion/ComfyUI/models/loras
[2025-06-26 19:59:59.190] Found checkpoint roots:
 - D:/stable_diffusion/1_Models/SDXL/checkpoints
 - D:/stable_diffusion/ComfyUI/models/checkpoints
 - D:/stable_diffusion/ComfyUI/models/diffusers
 - D:/stable_diffusion/ComfyUI/models/diffusion_models
 - D:/stable_diffusion/ComfyUI/models/unet
[2025-06-26 19:59:59.221] Saved folder paths to settings.json
[2025-06-26 19:59:59.356] Metadata collection hooks installed for runtime values
[2025-06-26 19:59:59.356] ComfyUI Metadata Collector initialized
[2025-06-26 19:59:59.356] Example images path: None
[2025-06-26 19:59:59.356] Added static route /loras_static/root1/preview -> D:/stable_diffusion/1_Models/SDXL/loras
[2025-06-26 19:59:59.360] Added static route /loras_static/root2/preview -> D:/stable_diffusion/ComfyUI/models/loras
[2025-06-26 19:59:59.360] Added static route /checkpoints_static/root1/preview -> D:/stable_diffusion/1_Models/SDXL/checkpoints
[2025-06-26 19:59:59.360] Added static route /checkpoints_static/root2/preview -> D:/stable_diffusion/ComfyUI/models/checkpoints
[2025-06-26 19:59:59.360] Added static route /checkpoints_static/root3/preview -> D:/stable_diffusion/ComfyUI/models/diffusers
[2025-06-26 19:59:59.362] Added static route /checkpoints_static/root4/preview -> D:/stable_diffusion/ComfyUI/models/diffusion_models
[2025-06-26 19:59:59.362] Added static route /checkpoints_static/root5/preview -> D:/stable_diffusion/ComfyUI/models/unet
[2025-06-26 19:59:59.814] FutureWarning: Importing from timm.models.layers is deprecated, please import via timm.layers
[2025-06-26 19:59:59.843] Imported node: DT_Flatten_Colors
[2025-06-26 19:59:59.844] Imported node: DT_FontText
[2025-06-26 19:59:59.844] Imported node: DT_GenerateNoise
[2025-06-26 19:59:59.848] Imported node: DT_Glitch_This
[2025-06-26 19:59:59.849] Imported node: DT_Hue_Rotation
[2025-06-26 19:59:59.849] Imported node: DT_Load_Picture_Index
[2025-06-26 19:59:59.888] Imported node: DT_PILGram
[2025-06-26 19:59:59.893] Imported node: DT_Pixel_Sort
[2025-06-26 20:00:00.258] Imported node: DT_Play_Sound_At_Execution
[2025-06-26 20:00:00.543] Imported node: DT_PromptGen
[2025-06-26 20:00:00.544] Imported node: DT_Solid_Color
[2025-06-26 20:00:00.544] Imported node: DT_Swap_Color_Mode
[2025-06-26 20:00:00.546] Imported node: DT_Flatten_Colors
[2025-06-26 20:00:00.546] Imported node: DT_FontText
[2025-06-26 20:00:00.546] Imported node: DT_GenerateNoise
[2025-06-26 20:00:00.546] Imported node: DT_Glitch_This
[2025-06-26 20:00:00.546] Imported node: DT_Hue_Rotation
[2025-06-26 20:00:00.546] Imported node: DT_Load_Picture_Index
[2025-06-26 20:00:00.546] Imported node: DT_PILGram
[2025-06-26 20:00:00.546] Imported node: DT_Pixel_Sort
[2025-06-26 20:00:00.546] Imported node: DT_Play_Sound_At_Execution
[2025-06-26 20:00:00.546] Imported node: DT_PromptGen
[2025-06-26 20:00:00.546] Imported node: DT_Solid_Color
[2025-06-26 20:00:00.546] Imported node: DT_Swap_Color_Mode
[2025-06-26 20:00:00.552] (pysssss:WD14Tagger) [DEBUG] Available ORT providers: AzureExecutionProvider, CPUExecutionProvider
[2025-06-26 20:00:00.552] (pysssss:WD14Tagger) [DEBUG] Using ORT providers: CUDAExecutionProvider, CPUExecutionProvider
[2025-06-26 20:00:00.562] SupervisionWarnings: BoundingBoxAnnotator is deprecated: `BoundingBoxAnnotator` is deprecated and has been renamed to `BoxAnnotator`. `BoundingBoxAnnotator` will be removed in supervision-0.26.0.
[2025-06-26 20:00:00.619] ------------------------------------------
[2025-06-26 20:00:00.619] [34mComfyroll Studio v1.76 : [92m 175 Nodes Loaded[0m
[2025-06-26 20:00:00.619] ------------------------------------------
[2025-06-26 20:00:00.619] ** For changes, please see patch notes at https://github.com/Suzie1/ComfyUI_Comfyroll_CustomNodes/blob/main/Patch_Notes.md
[2025-06-26 20:00:00.619] ** For help, please see the wiki at https://github.com/Suzie1/ComfyUI_Comfyroll_CustomNodes/wiki
[2025-06-26 20:00:00.619] ------------------------------------------
[2025-06-26 20:00:00.631] [36;20m[D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using ckpts path: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux\ckpts[0m
[2025-06-26 20:00:00.632] [36;20m[D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using symlinks: False[0m
[2025-06-26 20:00:00.633] [36;20m[D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using ort providers: ['CUDAExecutionProvider', 'DirectMLExecutionProvider', 'OpenVINOExecutionProvider', 'ROCMExecutionProvider', 'CPUExecutionProvider', 'CoreMLExecutionProvider'][0m
[2025-06-26 20:00:00.712] ------------------------------------
[2025-06-26 20:00:00.712] Loading nodes from: MY_NODES
[2025-06-26 20:00:00.713]   -> Loaded nodes from simple_slider.py
[2025-06-26 20:00:00.714]   -> Loaded nodes from slider_lora_loader.py
[2025-06-26 20:00:00.714] ------------------------------------
[2025-06-26 20:00:00.840] 
[2025-06-26 20:00:00.840] [92m[rgthree-comfy] Loaded 47 fantastic nodes. 🎉[0m
[2025-06-26 20:00:00.840] 
