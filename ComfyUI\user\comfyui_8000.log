## ComfyUI-Manager: installing dependencies done.
[2025-06-26 16:49:34.771] ** ComfyUI startup time: 2025-06-26 16:49:34.771
[2025-06-26 16:49:34.771] ** Platform: Windows
[2025-06-26 16:49:34.771] ** Python version: 3.12.9 (main, Feb 12 2025, 14:52:31) [MSC v.1942 64 bit (AMD64)]
[2025-06-26 16:49:34.771] ** Python executable: D:\stable_diffusion\ComfyUI\.venv\Scripts\python.exe
[2025-06-26 16:49:34.771] ** ComfyUI Path: C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI
[2025-06-26 16:49:34.771] ** ComfyUI Base Folder Path: C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI
[2025-06-26 16:49:34.771] ** User directory: D:\stable_diffusion\ComfyUI\user
[2025-06-26 16:49:34.817] ** ComfyUI-Manager config path: D:\stable_diffusion\ComfyUI\user\default\ComfyUI-Manager\config.ini
[2025-06-26 16:49:34.817] ** Log path: D:\stable_diffusion\ComfyUI\user\comfyui.log
[ComfyUI-Manager] Failed to restore comfyui-frontend-package
[2025-06-26 16:49:37.213] expected str, bytes or os.PathLike object, not NoneType
[2025-06-26 16:49:37.213] 
Prestartup times for custom nodes:
[2025-06-26 16:49:37.213]    8.4 seconds: C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI\custom_nodes\ComfyUI-Manager
[2025-06-26 16:49:37.213] 
[2025-06-26 16:49:42.706] Checkpoint files will always be loaded safely.
[2025-06-26 16:49:44.046] Total VRAM 6144 MB, total RAM 28524 MB
[2025-06-26 16:49:44.046] pytorch version: 2.7.0+cu128
[2025-06-26 16:49:44.053] Set vram state to: NORMAL_VRAM
[2025-06-26 16:49:44.053] Device: cuda:0 NVIDIA GeForce RTX 3060 Laptop GPU : cudaMallocAsync
[2025-06-26 16:49:47.917] Using pytorch attention
