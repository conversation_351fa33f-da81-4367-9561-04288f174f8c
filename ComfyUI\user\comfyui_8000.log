## ComfyUI-Manager: installing dependencies done.
[2025-06-26 19:00:15.740] ** ComfyUI startup time: 2025-06-26 19:00:15.740
[2025-06-26 19:00:15.740] ** Platform: Windows
[2025-06-26 19:00:15.740] ** Python version: 3.12.9 (main, Feb 12 2025, 14:52:31) [MSC v.1942 64 bit (AMD64)]
[2025-06-26 19:00:15.741] ** Python executable: D:\stable_diffusion\ComfyUI\.venv\Scripts\python.exe
[2025-06-26 19:00:15.741] ** ComfyUI Path: C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI
[2025-06-26 19:00:15.741] ** ComfyUI Base Folder Path: C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI
[2025-06-26 19:00:15.741] ** User directory: D:\stable_diffusion\ComfyUI\user
[2025-06-26 19:00:15.752] ** ComfyUI-Manager config path: D:\stable_diffusion\ComfyUI\user\default\ComfyUI-Manager\config.ini
[2025-06-26 19:00:15.752] ** Log path: D:\stable_diffusion\ComfyUI\user\comfyui.log
[ComfyUI-Manager] Failed to restore comfyui-frontend-package
[2025-06-26 19:00:17.805] expected str, bytes or os.PathLike object, not NoneType
[2025-06-26 19:00:17.805] 
Prestartup times for custom nodes:
[2025-06-26 19:00:17.805]    5.5 seconds: C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI\custom_nodes\ComfyUI-Manager
[2025-06-26 19:00:17.805] 
[2025-06-26 19:00:20.448] Checkpoint files will always be loaded safely.
[2025-06-26 19:00:21.841] Total VRAM 6144 MB, total RAM 28524 MB
[2025-06-26 19:00:21.841] pytorch version: 2.7.0+cu128
[2025-06-26 19:00:21.843] Set vram state to: NORMAL_VRAM
[2025-06-26 19:00:21.844] Device: cuda:0 NVIDIA GeForce RTX 3060 Laptop GPU : cudaMallocAsync
[2025-06-26 19:00:24.295] Using pytorch attention
[2025-06-26 19:00:28.556] Python version: 3.12.9 (main, Feb 12 2025, 14:52:31) [MSC v.1942 64 bit (AMD64)]
[2025-06-26 19:00:28.556] ComfyUI version: 0.3.42
[2025-06-26 19:00:28.724] [Prompt Server] web root: C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI\web_custom_versions\desktop_app
[2025-06-26 19:00:31.568] Adding D:\stable_diffusion\ComfyUI\custom_nodes to sys.path
[2025-06-26 19:00:31.568] Adding C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI\custom_nodes to sys.path
[2025-06-26 19:00:31.609] D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-art-venture\modules\utils.py:9: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  import pkg_resources
[2025-06-26 19:00:31.731] Could not find efficiency nodes
[2025-06-26 19:00:31.794] [36;20m[comfyui_controlnet_aux] | INFO -> Using ckpts path: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux\ckpts[0m
[2025-06-26 19:00:31.794] [36;20m[comfyui_controlnet_aux] | INFO -> Using symlinks: False[0m
[2025-06-26 19:00:31.804] [36;20m[comfyui_controlnet_aux] | INFO -> Using ort providers: ['CUDAExecutionProvider', 'DirectMLExecutionProvider', 'OpenVINOExecutionProvider', 'ROCMExecutionProvider', 'CPUExecutionProvider', 'CoreMLExecutionProvider'][0m
[2025-06-26 19:00:32.289] D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux\node_wrappers\dwpose.py:26: UserWarning: DWPose: Onnxruntime not found or doesn't come with acceleration providers, switch to OpenCV with CPU device. DWPose might run very slowly
  warnings.warn("DWPose: Onnxruntime not found or doesn't come with acceleration providers, switch to OpenCV with CPU device. DWPose might run very slowly")
[2025-06-26 19:00:32.318] Loaded ControlNetPreprocessors nodes from D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux
[2025-06-26 19:00:32.318] Could not find AdvancedControlNet nodes
[2025-06-26 19:00:32.325] Could not find AnimateDiff nodes
[2025-06-26 19:00:32.326] Could not find IPAdapter nodes
[2025-06-26 19:00:32.336] Could not find VideoHelperSuite nodes
[2025-06-26 19:00:32.339] Could not load ImpactPack nodes Could not find ImpactPack nodes
