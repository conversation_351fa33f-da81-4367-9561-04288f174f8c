## ComfyUI-Manager: installing dependencies done.
[2025-06-26 18:05:46.350] ** ComfyUI startup time: 2025-06-26 18:05:46.350
[2025-06-26 18:05:46.350] ** Platform: Windows
[2025-06-26 18:05:46.350] ** Python version: 3.12.9 (main, Feb 12 2025, 14:52:31) [MSC v.1942 64 bit (AMD64)]
[2025-06-26 18:05:46.350] ** Python executable: D:\stable_diffusion\ComfyUI\.venv\Scripts\python.exe
[2025-06-26 18:05:46.350] ** ComfyUI Path: C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI
[2025-06-26 18:05:46.350] ** ComfyUI Base Folder Path: C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI
[2025-06-26 18:05:46.350] ** User directory: D:\stable_diffusion\ComfyUI\user
[2025-06-26 18:05:46.395] ** ComfyUI-Manager config path: D:\stable_diffusion\ComfyUI\user\default\ComfyUI-Manager\config.ini
[2025-06-26 18:05:46.395] ** Log path: D:\stable_diffusion\ComfyUI\user\comfyui.log
[ComfyUI-Manager] Failed to restore comfyui-frontend-package
[2025-06-26 18:05:48.179] expected str, bytes or os.PathLike object, not NoneType
[2025-06-26 18:05:48.179] 
Prestartup times for custom nodes:
[2025-06-26 18:05:48.179]    4.5 seconds: C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI\custom_nodes\ComfyUI-Manager
[2025-06-26 18:05:48.179] 
[2025-06-26 18:05:50.276] Checkpoint files will always be loaded safely.
[2025-06-26 18:05:51.573] Total VRAM 6144 MB, total RAM 28524 MB
[2025-06-26 18:05:51.573] pytorch version: 2.7.0+cu128
[2025-06-26 18:05:51.574] Set vram state to: NORMAL_VRAM
[2025-06-26 18:05:51.575] Device: cuda:0 NVIDIA GeForce RTX 3060 Laptop GPU : cudaMallocAsync
[2025-06-26 18:05:53.504] Using pytorch attention
[2025-06-26 18:05:55.342] Python version: 3.12.9 (main, Feb 12 2025, 14:52:31) [MSC v.1942 64 bit (AMD64)]
[2025-06-26 18:05:55.344] ComfyUI version: 0.3.41
[2025-06-26 18:05:55.535] [Prompt Server] web root: C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI\web_custom_versions\desktop_app
[2025-06-26 18:05:56.763] Adding D:\stable_diffusion\ComfyUI\custom_nodes to sys.path
[2025-06-26 18:05:56.763] Adding C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI\custom_nodes to sys.path
[2025-06-26 18:05:56.805] D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-art-venture\modules\utils.py:9: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  import pkg_resources
[2025-06-26 18:05:57.065] Could not find efficiency nodes
[2025-06-26 18:05:57.114] [36;20m[comfyui_controlnet_aux] | INFO -> Using ckpts path: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux\ckpts[0m
[2025-06-26 18:05:57.115] [36;20m[comfyui_controlnet_aux] | INFO -> Using symlinks: False[0m
[2025-06-26 18:05:57.116] [36;20m[comfyui_controlnet_aux] | INFO -> Using ort providers: ['CUDAExecutionProvider', 'DirectMLExecutionProvider', 'OpenVINOExecutionProvider', 'ROCMExecutionProvider', 'CPUExecutionProvider', 'CoreMLExecutionProvider'][0m
[2025-06-26 18:05:57.447] D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux\node_wrappers\dwpose.py:26: UserWarning: DWPose: Onnxruntime not found or doesn't come with acceleration providers, switch to OpenCV with CPU device. DWPose might run very slowly
  warnings.warn("DWPose: Onnxruntime not found or doesn't come with acceleration providers, switch to OpenCV with CPU device. DWPose might run very slowly")
[2025-06-26 18:05:57.476] Loaded ControlNetPreprocessors nodes from D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux
[2025-06-26 18:05:57.478] Could not find AdvancedControlNet nodes
[2025-06-26 18:05:57.481] Could not find AnimateDiff nodes
[2025-06-26 18:05:57.484] Could not find IPAdapter nodes
[2025-06-26 18:05:57.490] Could not find VideoHelperSuite nodes
[2025-06-26 18:05:57.494] Could not load ImpactPack nodes Could not find ImpactPack nodes
[2025-06-26 18:05:58.573] [34m[ComfyUI-Easy-Use] server: [0mv1.3.0 [92mLoaded[0m
[2025-06-26 18:05:58.573] [34m[ComfyUI-Easy-Use] web root: [0mD:\stable_diffusion\ComfyUI\custom_nodes\comfyui-easy-use\web_version/v2 [92mLoaded[0m
[2025-06-26 18:05:59.227] [06/26/25 18:05:59] WARNING  Your inference package version      __init__.py:41
[2025-06-26 18:05:59.227]                              0.50.4 is out of date! Please                     
[2025-06-26 18:05:59.227]                              upgrade to version 0.51.0 of                      
[2025-06-26 18:05:59.229]                              inference for the latest features                 
[2025-06-26 18:05:59.229]                              and bug fixes by running `pip                     
[2025-06-26 18:05:59.229]                              install --upgrade inference`.                     
[2025-06-26 18:05:59.653] [34mComfyUI-ImageCompare Nodes: [92mLoaded[0m
[2025-06-26 18:05:59.657] ### Loading: ComfyUI-Impact-Pack (V8.15.3)
[2025-06-26 18:05:59.719] [Impact Pack] Wildcards loading done.
[2025-06-26 18:05:59.726] ### Loading: ComfyUI-Impact-Subpack (V1.3.2)
[2025-06-26 18:05:59.732] [Impact Pack/Subpack] Using folder_paths to determine whitelist path: D:\stable_diffusion\ComfyUI\user\default\ComfyUI-Impact-Subpack\model-whitelist.txt
[2025-06-26 18:05:59.732] [Impact Pack/Subpack] Ensured whitelist directory exists: D:\stable_diffusion\ComfyUI\user\default\ComfyUI-Impact-Subpack
[2025-06-26 18:05:59.733] [Impact Pack/Subpack] Loaded 0 model(s) from whitelist: D:\stable_diffusion\ComfyUI\user\default\ComfyUI-Impact-Subpack\model-whitelist.txt
[2025-06-26 18:05:59.734] [Impact Subpack] ultralytics_bbox: D:\stable_diffusion\ComfyUI\models\ultralytics\bbox
[2025-06-26 18:05:59.735] [Impact Subpack] ultralytics_segm: D:\stable_diffusion\ComfyUI\models\ultralytics\segm
[2025-06-26 18:05:59.735] [Impact Subpack] ultralytics_bbox: D:\stable_diffusion\ComfyUI\models\ultralytics\bbox
[2025-06-26 18:05:59.735] [Impact Subpack] ultralytics_segm: D:\stable_diffusion\ComfyUI\models\ultralytics\segm
[2025-06-26 18:05:59.780] Found LoRA roots:
 - D:/stable_diffusion/1_Models/SDXL/loras
 - D:/stable_diffusion/ComfyUI/models/loras
[2025-06-26 18:05:59.781] Found checkpoint roots:
 - D:/stable_diffusion/1_Models/SDXL/checkpoints
 - D:/stable_diffusion/ComfyUI/models/checkpoints
 - D:/stable_diffusion/ComfyUI/models/diffusers
 - D:/stable_diffusion/ComfyUI/models/diffusion_models
 - D:/stable_diffusion/ComfyUI/models/unet
[2025-06-26 18:05:59.806] Saved folder paths to settings.json
[2025-06-26 18:05:59.943] Metadata collection hooks installed for runtime values
[2025-06-26 18:05:59.943] ComfyUI Metadata Collector initialized
[2025-06-26 18:05:59.944] Example images path: None
[2025-06-26 18:05:59.945] Added static route /loras_static/root1/preview -> D:/stable_diffusion/1_Models/SDXL/loras
[2025-06-26 18:05:59.945] Added static route /loras_static/root2/preview -> D:/stable_diffusion/ComfyUI/models/loras
[2025-06-26 18:05:59.945] Added static route /checkpoints_static/root1/preview -> D:/stable_diffusion/1_Models/SDXL/checkpoints
[2025-06-26 18:05:59.945] Added static route /checkpoints_static/root2/preview -> D:/stable_diffusion/ComfyUI/models/checkpoints
[2025-06-26 18:05:59.946] Added static route /checkpoints_static/root3/preview -> D:/stable_diffusion/ComfyUI/models/diffusers
[2025-06-26 18:05:59.946] Added static route /checkpoints_static/root4/preview -> D:/stable_diffusion/ComfyUI/models/diffusion_models
[2025-06-26 18:05:59.946] Added static route /checkpoints_static/root5/preview -> D:/stable_diffusion/ComfyUI/models/unet
[2025-06-26 18:06:00.402] FutureWarning: Importing from timm.models.layers is deprecated, please import via timm.layers
[2025-06-26 18:06:00.433] Imported node: DT_Flatten_Colors
[2025-06-26 18:06:00.433] Imported node: DT_FontText
[2025-06-26 18:06:00.433] Imported node: DT_GenerateNoise
[2025-06-26 18:06:00.437] Imported node: DT_Glitch_This
[2025-06-26 18:06:00.438] Imported node: DT_Hue_Rotation
[2025-06-26 18:06:00.438] Imported node: DT_Load_Picture_Index
[2025-06-26 18:06:00.475] Imported node: DT_PILGram
[2025-06-26 18:06:00.481] Imported node: DT_Pixel_Sort
[2025-06-26 18:06:01.023] Imported node: DT_Play_Sound_At_Execution
[2025-06-26 18:06:01.303] Imported node: DT_PromptGen
[2025-06-26 18:06:01.304] Imported node: DT_Solid_Color
[2025-06-26 18:06:01.304] Imported node: DT_Swap_Color_Mode
[2025-06-26 18:06:01.304] Imported node: DT_Flatten_Colors
[2025-06-26 18:06:01.304] Imported node: DT_FontText
[2025-06-26 18:06:01.304] Imported node: DT_GenerateNoise
[2025-06-26 18:06:01.304] Imported node: DT_Glitch_This
[2025-06-26 18:06:01.304] Imported node: DT_Hue_Rotation
[2025-06-26 18:06:01.304] Imported node: DT_Load_Picture_Index
[2025-06-26 18:06:01.305] Imported node: DT_PILGram
[2025-06-26 18:06:01.305] Imported node: DT_Pixel_Sort
[2025-06-26 18:06:01.305] Imported node: DT_Play_Sound_At_Execution
[2025-06-26 18:06:01.305] Imported node: DT_PromptGen
[2025-06-26 18:06:01.305] Imported node: DT_Solid_Color
[2025-06-26 18:06:01.305] Imported node: DT_Swap_Color_Mode
[2025-06-26 18:06:01.321] (pysssss:WD14Tagger) [DEBUG] Available ORT providers: AzureExecutionProvider, CPUExecutionProvider
[2025-06-26 18:06:01.321] (pysssss:WD14Tagger) [DEBUG] Using ORT providers: CUDAExecutionProvider, CPUExecutionProvider
[2025-06-26 18:06:01.333] SupervisionWarnings: BoundingBoxAnnotator is deprecated: `BoundingBoxAnnotator` is deprecated and has been renamed to `BoxAnnotator`. `BoundingBoxAnnotator` will be removed in supervision-0.26.0.
[2025-06-26 18:06:01.382] ------------------------------------------
[2025-06-26 18:06:01.382] [34mComfyroll Studio v1.76 : [92m 175 Nodes Loaded[0m
[2025-06-26 18:06:01.382] ------------------------------------------
[2025-06-26 18:06:01.382] ** For changes, please see patch notes at https://github.com/Suzie1/ComfyUI_Comfyroll_CustomNodes/blob/main/Patch_Notes.md
[2025-06-26 18:06:01.382] ** For help, please see the wiki at https://github.com/Suzie1/ComfyUI_Comfyroll_CustomNodes/wiki
[2025-06-26 18:06:01.382] ------------------------------------------
[2025-06-26 18:06:01.393] [36;20m[D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using ckpts path: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux\ckpts[0m
[2025-06-26 18:06:01.394] [36;20m[D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using symlinks: False[0m
[2025-06-26 18:06:01.395] [36;20m[D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using ort providers: ['CUDAExecutionProvider', 'DirectMLExecutionProvider', 'OpenVINOExecutionProvider', 'ROCMExecutionProvider', 'CPUExecutionProvider', 'CoreMLExecutionProvider'][0m
[2025-06-26 18:06:01.474] ------------------------------------
[2025-06-26 18:06:01.474] Loading nodes from: MY_NODES
[2025-06-26 18:06:01.476]   -> Loaded nodes from simple_slider.py
[2025-06-26 18:06:01.476] ------------------------------------
[2025-06-26 18:06:01.608] 
[2025-06-26 18:06:01.608] [92m[rgthree-comfy] Loaded 47 magnificent nodes. 🎉[0m
[2025-06-26 18:06:01.608] 
[2025-06-26 18:06:02.587] [34mWAS Node Suite: [0mOpenCV Python FFMPEG support is enabled[0m
[2025-06-26 18:06:02.587] [34mWAS Node Suite [93mWarning: [0m`ffmpeg_bin_path` is not set in `D:\stable_diffusion\ComfyUI\custom_nodes\was-ns\was_suite_config.json` config file. Will attempt to use system ffmpeg binaries if available.[0m
[2025-06-26 18:06:03.355] [34mWAS Node Suite: [0mFinished.[0m [32mLoaded[0m [0m220[0m [32mnodes successfully.[0m
[2025-06-26 18:06:03.356] 
	[3m[93m"Don't wait. The time will never be just right."[0m[3m - Napoleon Hill[0m
[2025-06-26 18:06:03.356] 
[2025-06-26 18:06:03.376] ### Loading: ComfyUI-Manager (V3.30.4)
[2025-06-26 18:06:03.376] [ComfyUI-Manager] network_mode: public
[2025-06-26 18:06:03.377] ### ComfyUI Revision: UNKNOWN (The currently installed ComfyUI is not a Git repository)
[2025-06-26 18:06:03.422] 
Import times for custom nodes:
[2025-06-26 18:06:03.422]    0.0 seconds: C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI\custom_nodes\websocket_image_save.py
[2025-06-26 18:06:03.422]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyUI-ImageCompare
[2025-06-26 18:06:03.422]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\masquerade-nodes-comfyui
[2025-06-26 18:06:03.422]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\sdxl-recommended-res-calc
[2025-06-26 18:06:03.422]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyUI_Cutoff
[2025-06-26 18:06:03.422]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-ultralytics-yolo
[2025-06-26 18:06:03.422]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\MY_NODES
[2025-06-26 18:06:03.422]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\quick-connections
[2025-06-26 18:06:03.422]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-mxtoolkit
[2025-06-26 18:06:03.422]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\civitai_comfy_nodes
[2025-06-26 18:06:03.422]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\lora-info
[2025-06-26 18:06:03.422]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyUI-Contextual-SAM2
[2025-06-26 18:06:03.422]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-lora-auto-trigger-words
[2025-06-26 18:06:03.422]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-inpaint-cropandstitch
[2025-06-26 18:06:03.423]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-autocomplete-plus
[2025-06-26 18:06:03.423]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-inpaint-nodes
[2025-06-26 18:06:03.423]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-model-downloader
[2025-06-26 18:06:03.423]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_essentials
[2025-06-26 18:06:03.423]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyUI-YoloWorld-EfficientSAM
[2025-06-26 18:06:03.423]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_ultimatesdupscale
[2025-06-26 18:06:03.423]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-custom-scripts
[2025-06-26 18:06:03.423]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyMath
[2025-06-26 18:06:03.423]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-impact-subpack
[2025-06-26 18:06:03.423]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-wd14-tagger
[2025-06-26 18:06:03.423]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyUI-segment-anything-2
[2025-06-26 18:06:03.423]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-kjnodes
[2025-06-26 18:06:03.423]    0.1 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux
[2025-06-26 18:06:03.424]    0.1 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyUI_Comfyroll_CustomNodes
[2025-06-26 18:06:03.424]    0.1 seconds: C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI\custom_nodes\ComfyUI-Manager
[2025-06-26 18:06:03.424]    0.1 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-impact-pack
[2025-06-26 18:06:03.425]    0.1 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\rgthree-comfy
[2025-06-26 18:06:03.425]    0.2 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-lora-manager
[2025-06-26 18:06:03.425]    0.4 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-sam2
[2025-06-26 18:06:03.425]    0.7 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-art-venture
[2025-06-26 18:06:03.425]    0.9 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyUI-Vextra-Nodes
[2025-06-26 18:06:03.425]    1.1 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-easy-use
[2025-06-26 18:06:03.425]    1.1 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyUI-GroundedSAM2
[2025-06-26 18:06:03.425]    1.7 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\was-ns
[2025-06-26 18:06:03.425] 
[2025-06-26 18:06:03.731] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/alter-list.json
[2025-06-26 18:06:03.788] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/model-list.json
[2025-06-26 18:06:03.824] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/github-stats.json
[2025-06-26 18:06:03.876] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/extension-node-map.json
[2025-06-26 18:06:03.925] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json
[2025-06-26 18:06:04.115] Failed to initialize database. Please ensure you have installed the latest requirements. If the error persists, please report this as in future the database will be required: (sqlite3.OperationalError) unable to open database file
(Background on this error at: https://sqlalche.me/e/20/e3q8)
[2025-06-26 18:06:04.157] Metadata collection hooks installed for runtime values
[2025-06-26 18:06:04.157] ComfyUI Metadata Collector initialized
[2025-06-26 18:06:04.157] LoRA Manager: All services initialized and background tasks scheduled
[2025-06-26 18:06:04.157] Starting server

[2025-06-26 18:06:04.157] Cache files disabled for lora, skipping load
[2025-06-26 18:06:04.158] Cache files disabled for checkpoint, skipping load
[2025-06-26 18:06:04.162] To see the GUI go to: http://127.0.0.1:8000
[2025-06-26 18:06:04.164] Recipe cache initialized in 0.00 seconds. Found 0 recipes
[2025-06-26 18:06:04.181] Checkpoint cache initialized in 0.02 seconds. Found 12 models
[2025-06-26 18:06:04.238] Lora cache initialized in 0.05 seconds. Found 60 models
[2025-06-26 18:06:07.056] FETCH ComfyRegistry Data: 5/90
[2025-06-26 18:06:07.952] [Autocomplete-Plus] CSV file status:
  * Danbooru -> base: True, extra: 
  * E621     -> base: True, extra: 
[2025-06-26 18:06:10.663] FETCH ComfyRegistry Data: 10/90
[2025-06-26 18:06:14.165] FETCH ComfyRegistry Data: 15/90
[2025-06-26 18:06:18.182] FETCH ComfyRegistry Data: 20/90
[2025-06-26 18:06:23.207] FETCH ComfyRegistry Data: 25/90
[2025-06-26 18:06:27.297] FETCH ComfyRegistry Data: 30/90
[2025-06-26 18:06:31.140] FETCH ComfyRegistry Data: 35/90
[2025-06-26 18:06:34.670] FETCH ComfyRegistry Data: 40/90
[2025-06-26 18:06:38.490] FETCH ComfyRegistry Data: 45/90
[2025-06-26 18:06:42.197] FETCH ComfyRegistry Data: 50/90
[2025-06-26 18:06:46.299] FETCH ComfyRegistry Data: 55/90
[2025-06-26 18:06:51.095] FETCH ComfyRegistry Data: 60/90
[2025-06-26 18:06:54.650] FETCH ComfyRegistry Data: 65/90
[2025-06-26 18:06:58.201] FETCH ComfyRegistry Data: 70/90
[2025-06-26 18:07:01.802] FETCH ComfyRegistry Data: 75/90
[2025-06-26 18:07:05.454] FETCH ComfyRegistry Data: 80/90
[2025-06-26 18:07:08.574] [Autocomplete-Plus] CSV file status:
  * Danbooru -> base: True, extra: 
  * E621     -> base: True, extra: 
[2025-06-26 18:07:09.035] FETCH ComfyRegistry Data: 85/90
[2025-06-26 18:07:12.539] FETCH ComfyRegistry Data: 90/90
[2025-06-26 18:07:13.040] FETCH ComfyRegistry Data [DONE]
[2025-06-26 18:07:13.188] [ComfyUI-Manager] default cache updated: https://api.comfy.org/nodes
[2025-06-26 18:07:13.228] nightly_channel: 
[2025-06-26 18:07:13.228] https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/remote
[2025-06-26 18:07:13.229] FETCH DATA from: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json [DONE]
[2025-06-26 18:07:13.384] [ComfyUI-Manager] All startup tasks have been completed.
[2025-06-26 18:07:45.013] [Autocomplete-Plus] CSV file status:
  * Danbooru -> base: True, extra: 
  * E621     -> base: True, extra: 
[2025-06-26 18:08:42.389] [Autocomplete-Plus] CSV file status:
  * Danbooru -> base: True, extra: 
  * E621     -> base: True, extra: 
[2025-06-26 18:08:58.029] [Autocomplete-Plus] CSV file status:
  * Danbooru -> base: True, extra: 
  * E621     -> base: True, extra: 
[2025-06-26 18:11:17.842] [Autocomplete-Plus] CSV file status:
  * Danbooru -> base: True, extra: 
  * E621     -> base: True, extra: 
[2025-06-26 18:11:30.002] [Autocomplete-Plus] CSV file status:
  * Danbooru -> base: True, extra: 
  * E621     -> base: True, extra: 
[2025-06-26 18:12:06.736] [Autocomplete-Plus] CSV file status:
  * Danbooru -> base: True, extra: 
  * E621     -> base: True, extra: 
[2025-06-26 18:12:15.966] [Autocomplete-Plus] CSV file status:
  * Danbooru -> base: True, extra: 
  * E621     -> base: True, extra: 
[2025-06-26 18:12:44.632] [Autocomplete-Plus] CSV file status:
  * Danbooru -> base: True, extra: 
  * E621     -> base: True, extra: 
[2025-06-26 18:13:04.275] [Autocomplete-Plus] CSV file status:
  * Danbooru -> base: True, extra: 
  * E621     -> base: True, extra: 
[2025-06-26 18:13:16.685] [Autocomplete-Plus] CSV file status:
  * Danbooru -> base: True, extra: 
  * E621     -> base: True, extra: 
[2025-06-26 18:13:54.294] [Autocomplete-Plus] CSV file status:
  * Danbooru -> base: True, extra: 
  * E621     -> base: True, extra: 
[2025-06-26 18:14:08.282] [Autocomplete-Plus] CSV file status:
  * Danbooru -> base: True, extra: 
  * E621     -> base: True, extra: 
[2025-06-26 18:14:19.668] [Autocomplete-Plus] CSV file status:
  * Danbooru -> base: True, extra: 
  * E621     -> base: True, extra: 
