## ComfyUI-Manager: installing dependencies done.
[2025-06-26 14:58:00.806] ** ComfyUI startup time: 2025-06-26 14:58:00.806
[2025-06-26 14:58:00.806] ** Platform: Windows
[2025-06-26 14:58:00.806] ** Python version: 3.12.9 (main, Feb 12 2025, 14:52:31) [MSC v.1942 64 bit (AMD64)]
[2025-06-26 14:58:00.806] ** Python executable: D:\stable_diffusion\ComfyUI\.venv\Scripts\python.exe
[2025-06-26 14:58:00.806] ** ComfyUI Path: C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI
[2025-06-26 14:58:00.806] ** ComfyUI Base Folder Path: C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI
[2025-06-26 14:58:00.806] ** User directory: D:\stable_diffusion\ComfyUI\user
[2025-06-26 14:58:00.818] ** ComfyUI-Manager config path: D:\stable_diffusion\ComfyUI\user\default\ComfyUI-Manager\config.ini
[2025-06-26 14:58:00.818] ** Log path: D:\stable_diffusion\ComfyUI\user\comfyui.log
[ComfyUI-Manager] Failed to restore comfyui-frontend-package
[2025-06-26 14:58:02.548] expected str, bytes or os.PathLike object, not NoneType
[2025-06-26 14:58:02.549] 
Prestartup times for custom nodes:
[2025-06-26 14:58:02.549]    4.3 seconds: C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI\custom_nodes\ComfyUI-Manager
[2025-06-26 14:58:02.549] 
[2025-06-26 14:58:04.549] Checkpoint files will always be loaded safely.
[2025-06-26 14:58:05.835] Total VRAM 6144 MB, total RAM 28524 MB
[2025-06-26 14:58:05.835] pytorch version: 2.7.0+cu128
[2025-06-26 14:58:05.835] Set vram state to: NORMAL_VRAM
[2025-06-26 14:58:05.847] Device: cuda:0 NVIDIA GeForce RTX 3060 Laptop GPU : cudaMallocAsync
[2025-06-26 14:58:07.719] Using pytorch attention
[2025-06-26 14:58:09.499] Python version: 3.12.9 (main, Feb 12 2025, 14:52:31) [MSC v.1942 64 bit (AMD64)]
[2025-06-26 14:58:09.499] ComfyUI version: 0.3.41
[2025-06-26 14:58:09.666] [Prompt Server] web root: C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI\web_custom_versions\desktop_app
[2025-06-26 14:58:10.856] Adding D:\stable_diffusion\ComfyUI\custom_nodes to sys.path
[2025-06-26 14:58:10.857] Adding C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI\custom_nodes to sys.path
[2025-06-26 14:58:10.902] D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-art-venture\modules\utils.py:9: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  import pkg_resources
[2025-06-26 14:58:11.189] Could not find efficiency nodes
[2025-06-26 14:58:11.261] [36;20m[comfyui_controlnet_aux] | INFO -> Using ckpts path: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux\ckpts[0m
[2025-06-26 14:58:11.262] [36;20m[comfyui_controlnet_aux] | INFO -> Using symlinks: False[0m
[2025-06-26 14:58:11.263] [36;20m[comfyui_controlnet_aux] | INFO -> Using ort providers: ['CUDAExecutionProvider', 'DirectMLExecutionProvider', 'OpenVINOExecutionProvider', 'ROCMExecutionProvider', 'CPUExecutionProvider', 'CoreMLExecutionProvider'][0m
[2025-06-26 14:58:11.677] D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux\node_wrappers\dwpose.py:26: UserWarning: DWPose: Onnxruntime not found or doesn't come with acceleration providers, switch to OpenCV with CPU device. DWPose might run very slowly
  warnings.warn("DWPose: Onnxruntime not found or doesn't come with acceleration providers, switch to OpenCV with CPU device. DWPose might run very slowly")
[2025-06-26 14:58:11.711] Loaded ControlNetPreprocessors nodes from D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux
[2025-06-26 14:58:11.712] Could not find AdvancedControlNet nodes
[2025-06-26 14:58:11.715] Could not find AnimateDiff nodes
[2025-06-26 14:58:11.717] Could not find IPAdapter nodes
[2025-06-26 14:58:11.724] Could not find VideoHelperSuite nodes
[2025-06-26 14:58:11.727] Could not load ImpactPack nodes Could not find ImpactPack nodes
[2025-06-26 14:58:12.737] [34m[ComfyUI-Easy-Use] server: [0mv1.3.0 [92mLoaded[0m
[2025-06-26 14:58:12.737] [34m[ComfyUI-Easy-Use] web root: [0mD:\stable_diffusion\ComfyUI\custom_nodes\comfyui-easy-use\web_version/v2 [92mLoaded[0m
[2025-06-26 14:58:13.352] [06/26/25 14:58:13] WARNING  Your inference package version      __init__.py:41
[2025-06-26 14:58:13.352]                              0.50.4 is out of date! Please                     
[2025-06-26 14:58:13.352]                              upgrade to version 0.51.0 of                      
[2025-06-26 14:58:13.352]                              inference for the latest features                 
[2025-06-26 14:58:13.354]                              and bug fixes by running `pip                     
[2025-06-26 14:58:13.354]                              install --upgrade inference`.                     
[2025-06-26 14:58:13.791] [34mComfyUI-ImageCompare Nodes: [92mLoaded[0m
[2025-06-26 14:58:13.795] ### Loading: ComfyUI-Impact-Pack (V8.15.3)
[2025-06-26 14:58:13.846] [Impact Pack] Wildcards loading done.
[2025-06-26 14:58:13.859] ### Loading: ComfyUI-Impact-Subpack (V1.3.2)
[2025-06-26 14:58:13.862] [Impact Pack/Subpack] Using folder_paths to determine whitelist path: D:\stable_diffusion\ComfyUI\user\default\ComfyUI-Impact-Subpack\model-whitelist.txt
[2025-06-26 14:58:13.863] [Impact Pack/Subpack] Ensured whitelist directory exists: D:\stable_diffusion\ComfyUI\user\default\ComfyUI-Impact-Subpack
[2025-06-26 14:58:13.863] [Impact Pack/Subpack] Loaded 0 model(s) from whitelist: D:\stable_diffusion\ComfyUI\user\default\ComfyUI-Impact-Subpack\model-whitelist.txt
[2025-06-26 14:58:13.864] [Impact Subpack] ultralytics_bbox: D:\stable_diffusion\ComfyUI\models\ultralytics\bbox
[2025-06-26 14:58:13.864] [Impact Subpack] ultralytics_segm: D:\stable_diffusion\ComfyUI\models\ultralytics\segm
[2025-06-26 14:58:13.865] [Impact Subpack] ultralytics_bbox: D:\stable_diffusion\ComfyUI\models\ultralytics\bbox
[2025-06-26 14:58:13.865] [Impact Subpack] ultralytics_segm: D:\stable_diffusion\ComfyUI\models\ultralytics\segm
[2025-06-26 14:58:13.904] Found LoRA roots:
 - D:/stable_diffusion/1_Models/SDXL/loras
 - D:/stable_diffusion/ComfyUI/models/loras
[2025-06-26 14:58:13.913] Found checkpoint roots:
 - D:/stable_diffusion/1_Models/SDXL/checkpoints
 - D:/stable_diffusion/ComfyUI/models/checkpoints
 - D:/stable_diffusion/ComfyUI/models/diffusers
 - D:/stable_diffusion/ComfyUI/models/diffusion_models
 - D:/stable_diffusion/ComfyUI/models/unet
[2025-06-26 14:58:13.934] Saved folder paths to settings.json
[2025-06-26 14:58:14.062] Metadata collection hooks installed for runtime values
[2025-06-26 14:58:14.062] ComfyUI Metadata Collector initialized
[2025-06-26 14:58:14.062] Example images path: None
[2025-06-26 14:58:14.072] Added static route /loras_static/root1/preview -> D:/stable_diffusion/1_Models/SDXL/loras
[2025-06-26 14:58:14.073] Added static route /loras_static/root2/preview -> D:/stable_diffusion/ComfyUI/models/loras
[2025-06-26 14:58:14.073] Added static route /checkpoints_static/root1/preview -> D:/stable_diffusion/1_Models/SDXL/checkpoints
[2025-06-26 14:58:14.073] Added static route /checkpoints_static/root2/preview -> D:/stable_diffusion/ComfyUI/models/checkpoints
[2025-06-26 14:58:14.073] Added static route /checkpoints_static/root3/preview -> D:/stable_diffusion/ComfyUI/models/diffusers
[2025-06-26 14:58:14.074] Added static route /checkpoints_static/root4/preview -> D:/stable_diffusion/ComfyUI/models/diffusion_models
[2025-06-26 14:58:14.074] Added static route /checkpoints_static/root5/preview -> D:/stable_diffusion/ComfyUI/models/unet
[2025-06-26 14:58:14.540] FutureWarning: Importing from timm.models.layers is deprecated, please import via timm.layers
[2025-06-26 14:58:14.572] Imported node: DT_Flatten_Colors
[2025-06-26 14:58:14.572] Imported node: DT_FontText
[2025-06-26 14:58:14.574] Imported node: DT_GenerateNoise
[2025-06-26 14:58:14.577] Imported node: DT_Glitch_This
[2025-06-26 14:58:14.578] Imported node: DT_Hue_Rotation
[2025-06-26 14:58:14.579] Imported node: DT_Load_Picture_Index
[2025-06-26 14:58:14.611] Imported node: DT_PILGram
[2025-06-26 14:58:14.620] Imported node: DT_Pixel_Sort
[2025-06-26 14:58:14.999] Imported node: DT_Play_Sound_At_Execution
[2025-06-26 14:58:15.273] Imported node: DT_PromptGen
[2025-06-26 14:58:15.273] Imported node: DT_Solid_Color
[2025-06-26 14:58:15.275] Imported node: DT_Swap_Color_Mode
[2025-06-26 14:58:15.275] Imported node: DT_Flatten_Colors
[2025-06-26 14:58:15.275] Imported node: DT_FontText
[2025-06-26 14:58:15.275] Imported node: DT_GenerateNoise
[2025-06-26 14:58:15.275] Imported node: DT_Glitch_This
[2025-06-26 14:58:15.275] Imported node: DT_Hue_Rotation
[2025-06-26 14:58:15.275] Imported node: DT_Load_Picture_Index
[2025-06-26 14:58:15.276] Imported node: DT_PILGram
[2025-06-26 14:58:15.276] Imported node: DT_Pixel_Sort
[2025-06-26 14:58:15.276] Imported node: DT_Play_Sound_At_Execution
[2025-06-26 14:58:15.276] Imported node: DT_PromptGen
[2025-06-26 14:58:15.276] Imported node: DT_Solid_Color
[2025-06-26 14:58:15.276] Imported node: DT_Swap_Color_Mode
[2025-06-26 14:58:15.282] (pysssss:WD14Tagger) [DEBUG] Available ORT providers: AzureExecutionProvider, CPUExecutionProvider
[2025-06-26 14:58:15.282] (pysssss:WD14Tagger) [DEBUG] Using ORT providers: CUDAExecutionProvider, CPUExecutionProvider
[2025-06-26 14:58:15.290] SupervisionWarnings: BoundingBoxAnnotator is deprecated: `BoundingBoxAnnotator` is deprecated and has been renamed to `BoxAnnotator`. `BoundingBoxAnnotator` will be removed in supervision-0.26.0.
[2025-06-26 14:58:15.336] ------------------------------------------
[2025-06-26 14:58:15.336] [34mComfyroll Studio v1.76 : [92m 175 Nodes Loaded[0m
[2025-06-26 14:58:15.336] ------------------------------------------
[2025-06-26 14:58:15.336] ** For changes, please see patch notes at https://github.com/Suzie1/ComfyUI_Comfyroll_CustomNodes/blob/main/Patch_Notes.md
[2025-06-26 14:58:15.336] ** For help, please see the wiki at https://github.com/Suzie1/ComfyUI_Comfyroll_CustomNodes/wiki
[2025-06-26 14:58:15.336] ------------------------------------------
[2025-06-26 14:58:15.347] [36;20m[D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using ckpts path: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux\ckpts[0m
[2025-06-26 14:58:15.347] [36;20m[D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using symlinks: False[0m
[2025-06-26 14:58:15.348] [36;20m[D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using ort providers: ['CUDAExecutionProvider', 'DirectMLExecutionProvider', 'OpenVINOExecutionProvider', 'ROCMExecutionProvider', 'CPUExecutionProvider', 'CoreMLExecutionProvider'][0m
[2025-06-26 14:58:15.427] ------------------------------------
[2025-06-26 14:58:15.427] Loading nodes from: MY_NODES
[2025-06-26 14:58:15.428]   -> Loaded nodes from simple_slider.py
[2025-06-26 14:58:15.428] ------------------------------------
[2025-06-26 14:58:15.537] 
[2025-06-26 14:58:15.538] [92m[rgthree-comfy] Loaded 47 fantastic nodes. 🎉[0m
[2025-06-26 14:58:15.538] 
[2025-06-26 14:58:16.567] [34mWAS Node Suite: [0mOpenCV Python FFMPEG support is enabled[0m
[2025-06-26 14:58:16.567] [34mWAS Node Suite [93mWarning: [0m`ffmpeg_bin_path` is not set in `D:\stable_diffusion\ComfyUI\custom_nodes\was-ns\was_suite_config.json` config file. Will attempt to use system ffmpeg binaries if available.[0m
[2025-06-26 14:58:17.296] [34mWAS Node Suite: [0mFinished.[0m [32mLoaded[0m [0m220[0m [32mnodes successfully.[0m
[2025-06-26 14:58:17.296] 
	[3m[93m"The artist's world is limitless. It can be found anywhere, far from where he lives or a few feet away. It is always on his doorstep."[0m[3m - Paul Strand[0m
[2025-06-26 14:58:17.296] 
[2025-06-26 14:58:17.314] ### Loading: ComfyUI-Manager (V3.30.4)
[2025-06-26 14:58:17.314] [ComfyUI-Manager] network_mode: public
[2025-06-26 14:58:17.316] ### ComfyUI Revision: UNKNOWN (The currently installed ComfyUI is not a Git repository)
[2025-06-26 14:58:17.351] 
Import times for custom nodes:
[2025-06-26 14:58:17.351]    0.0 seconds: C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI\custom_nodes\websocket_image_save.py
[2025-06-26 14:58:17.351]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyUI-ImageCompare
[2025-06-26 14:58:17.351]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyUI_Cutoff
[2025-06-26 14:58:17.351]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\quick-connections
[2025-06-26 14:58:17.351]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\masquerade-nodes-comfyui
[2025-06-26 14:58:17.351]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-ultralytics-yolo
[2025-06-26 14:58:17.351]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\MY_NODES
[2025-06-26 14:58:17.351]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\sdxl-recommended-res-calc
[2025-06-26 14:58:17.351]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\lora-info
[2025-06-26 14:58:17.352]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-mxtoolkit
[2025-06-26 14:58:17.352]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyUI-Contextual-SAM2
[2025-06-26 14:58:17.352]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\civitai_comfy_nodes
[2025-06-26 14:58:17.352]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-inpaint-cropandstitch
[2025-06-26 14:58:17.352]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-autocomplete-plus
[2025-06-26 14:58:17.352]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-lora-auto-trigger-words
[2025-06-26 14:58:17.352]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-wd14-tagger
[2025-06-26 14:58:17.352]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyUI-YoloWorld-EfficientSAM
[2025-06-26 14:58:17.352]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-impact-subpack
[2025-06-26 14:58:17.352]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-model-downloader
[2025-06-26 14:58:17.352]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_essentials
[2025-06-26 14:58:17.352]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-inpaint-nodes
[2025-06-26 14:58:17.352]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_ultimatesdupscale
[2025-06-26 14:58:17.352]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-custom-scripts
[2025-06-26 14:58:17.352]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyMath
[2025-06-26 14:58:17.352]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyUI-segment-anything-2
[2025-06-26 14:58:17.352]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-kjnodes
[2025-06-26 14:58:17.352]    0.0 seconds: C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI\custom_nodes\ComfyUI-Manager
[2025-06-26 14:58:17.352]    0.1 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyUI_Comfyroll_CustomNodes
[2025-06-26 14:58:17.352]    0.1 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux
[2025-06-26 14:58:17.352]    0.1 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-impact-pack
[2025-06-26 14:58:17.352]    0.1 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\rgthree-comfy
[2025-06-26 14:58:17.353]    0.2 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-lora-manager
[2025-06-26 14:58:17.353]    0.5 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-sam2
[2025-06-26 14:58:17.353]    0.7 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyUI-Vextra-Nodes
[2025-06-26 14:58:17.353]    0.9 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-art-venture
[2025-06-26 14:58:17.353]    1.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-easy-use
[2025-06-26 14:58:17.353]    1.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyUI-GroundedSAM2
[2025-06-26 14:58:17.353]    1.8 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\was-ns
[2025-06-26 14:58:17.353] 
[2025-06-26 14:58:17.689] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/model-list.json
[2025-06-26 14:58:17.692] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/alter-list.json
[2025-06-26 14:58:17.723] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/github-stats.json
[2025-06-26 14:58:17.775] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/extension-node-map.json
[2025-06-26 14:58:17.823] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json
[2025-06-26 14:58:17.993] Failed to initialize database. Please ensure you have installed the latest requirements. If the error persists, please report this as in future the database will be required: (sqlite3.OperationalError) unable to open database file
(Background on this error at: https://sqlalche.me/e/20/e3q8)
[2025-06-26 14:58:18.033] Metadata collection hooks installed for runtime values
[2025-06-26 14:58:18.033] ComfyUI Metadata Collector initialized
[2025-06-26 14:58:18.033] LoRA Manager: All services initialized and background tasks scheduled
[2025-06-26 14:58:18.035] Starting server

[2025-06-26 14:58:18.035] Cache files disabled for lora, skipping load
[2025-06-26 14:58:18.035] Cache files disabled for checkpoint, skipping load
[2025-06-26 14:58:18.038] To see the GUI go to: http://127.0.0.1:8000
[2025-06-26 14:58:18.039] Recipe cache initialized in 0.00 seconds. Found 0 recipes
[2025-06-26 14:58:18.053] Checkpoint cache initialized in 0.01 seconds. Found 12 models
[2025-06-26 14:58:18.103] Lora cache initialized in 0.04 seconds. Found 60 models
[2025-06-26 14:58:20.733] FETCH ComfyRegistry Data: 5/90
[2025-06-26 14:58:20.794] Error handling request from 127.0.0.1
[2025-06-26 14:58:24.193] FETCH ComfyRegistry Data: 10/90
[2025-06-26 14:58:27.688] FETCH ComfyRegistry Data: 15/90
[2025-06-26 14:58:31.257] FETCH ComfyRegistry Data: 20/90
[2025-06-26 14:58:34.745] FETCH ComfyRegistry Data: 25/90
[2025-06-26 14:58:38.195] FETCH ComfyRegistry Data: 30/90
[2025-06-26 14:58:41.718] FETCH ComfyRegistry Data: 35/90
[2025-06-26 14:58:45.297] FETCH ComfyRegistry Data: 40/90
[2025-06-26 14:58:48.880] FETCH ComfyRegistry Data: 45/90
[2025-06-26 14:58:52.321] FETCH ComfyRegistry Data: 50/90
[2025-06-26 14:58:55.743] FETCH ComfyRegistry Data: 55/90
[2025-06-26 14:58:59.292] FETCH ComfyRegistry Data: 60/90
[2025-06-26 14:59:02.810] FETCH ComfyRegistry Data: 65/90
[2025-06-26 14:59:06.317] FETCH ComfyRegistry Data: 70/90
[2025-06-26 14:59:09.834] FETCH ComfyRegistry Data: 75/90
[2025-06-26 14:59:13.368] FETCH ComfyRegistry Data: 80/90
[2025-06-26 14:59:16.797] FETCH ComfyRegistry Data: 85/90
[2025-06-26 14:59:20.145] FETCH ComfyRegistry Data: 90/90
[2025-06-26 14:59:20.646] FETCH ComfyRegistry Data [DONE]
[2025-06-26 14:59:20.811] [ComfyUI-Manager] default cache updated: https://api.comfy.org/nodes
[2025-06-26 14:59:20.866] nightly_channel: 
[2025-06-26 14:59:20.866] https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/remote
[2025-06-26 14:59:20.866] FETCH DATA from: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json [DONE]
[2025-06-26 14:59:21.052] [ComfyUI-Manager] All startup tasks have been completed.
