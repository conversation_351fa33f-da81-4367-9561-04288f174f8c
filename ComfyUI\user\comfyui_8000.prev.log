## ComfyUI-Manager: installing dependencies done.
[2025-06-26 16:15:43.303] ** ComfyUI startup time: 2025-06-26 16:15:43.303
[2025-06-26 16:15:43.303] ** Platform: Windows
[2025-06-26 16:15:43.303] ** Python version: 3.12.9 (main, Feb 12 2025, 14:52:31) [MSC v.1942 64 bit (AMD64)]
[2025-06-26 16:15:43.303] ** Python executable: D:\stable_diffusion\ComfyUI\.venv\Scripts\python.exe
[2025-06-26 16:15:43.303] ** ComfyUI Path: C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI
[2025-06-26 16:15:43.303] ** ComfyUI Base Folder Path: C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI
[2025-06-26 16:15:43.303] ** User directory: D:\stable_diffusion\ComfyUI\user
[2025-06-26 16:15:43.349] ** ComfyUI-Manager config path: D:\stable_diffusion\ComfyUI\user\default\ComfyUI-Manager\config.ini
[2025-06-26 16:15:43.349] ** Log path: D:\stable_diffusion\ComfyUI\user\comfyui.log
[ComfyUI-Manager] Failed to restore comfyui-frontend-package
[2025-06-26 16:15:44.957] expected str, bytes or os.PathLike object, not NoneType
[2025-06-26 16:15:44.957] 
Prestartup times for custom nodes:
[2025-06-26 16:15:44.957]    4.2 seconds: C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI\custom_nodes\ComfyUI-Manager
[2025-06-26 16:15:44.957] 
[2025-06-26 16:15:46.883] Checkpoint files will always be loaded safely.
[2025-06-26 16:15:47.634] Total VRAM 6144 MB, total RAM 28524 MB
[2025-06-26 16:15:47.634] pytorch version: 2.7.0+cu128
[2025-06-26 16:15:47.635] Set vram state to: NORMAL_VRAM
[2025-06-26 16:15:47.635] Device: cuda:0 NVIDIA GeForce RTX 3060 Laptop GPU : cudaMallocAsync
[2025-06-26 16:15:49.608] Using pytorch attention
[2025-06-26 16:15:51.341] Python version: 3.12.9 (main, Feb 12 2025, 14:52:31) [MSC v.1942 64 bit (AMD64)]
[2025-06-26 16:15:51.341] ComfyUI version: 0.3.41
[2025-06-26 16:15:51.495] [Prompt Server] web root: C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI\web_custom_versions\desktop_app
[2025-06-26 16:15:52.674] Adding D:\stable_diffusion\ComfyUI\custom_nodes to sys.path
[2025-06-26 16:15:52.674] Adding C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI\custom_nodes to sys.path
[2025-06-26 16:15:52.714] D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-art-venture\modules\utils.py:9: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  import pkg_resources
[2025-06-26 16:15:52.967] Could not find efficiency nodes
[2025-06-26 16:15:53.015] [36;20m[comfyui_controlnet_aux] | INFO -> Using ckpts path: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux\ckpts[0m
[2025-06-26 16:15:53.015] [36;20m[comfyui_controlnet_aux] | INFO -> Using symlinks: False[0m
[2025-06-26 16:15:53.017] [36;20m[comfyui_controlnet_aux] | INFO -> Using ort providers: ['CUDAExecutionProvider', 'DirectMLExecutionProvider', 'OpenVINOExecutionProvider', 'ROCMExecutionProvider', 'CPUExecutionProvider', 'CoreMLExecutionProvider'][0m
[2025-06-26 16:15:53.321] D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux\node_wrappers\dwpose.py:26: UserWarning: DWPose: Onnxruntime not found or doesn't come with acceleration providers, switch to OpenCV with CPU device. DWPose might run very slowly
  warnings.warn("DWPose: Onnxruntime not found or doesn't come with acceleration providers, switch to OpenCV with CPU device. DWPose might run very slowly")
[2025-06-26 16:15:53.349] Loaded ControlNetPreprocessors nodes from D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux
[2025-06-26 16:15:53.350] Could not find AdvancedControlNet nodes
[2025-06-26 16:15:53.352] Could not find AnimateDiff nodes
[2025-06-26 16:15:53.354] Could not find IPAdapter nodes
[2025-06-26 16:15:53.360] Could not find VideoHelperSuite nodes
[2025-06-26 16:15:53.362] Could not load ImpactPack nodes Could not find ImpactPack nodes
[2025-06-26 16:15:54.283] [34m[ComfyUI-Easy-Use] server: [0mv1.3.0 [92mLoaded[0m
[2025-06-26 16:15:54.283] [34m[ComfyUI-Easy-Use] web root: [0mD:\stable_diffusion\ComfyUI\custom_nodes\comfyui-easy-use\web_version/v2 [92mLoaded[0m
[2025-06-26 16:15:54.814] [06/26/25 16:15:54] WARNING  Your inference package version      __init__.py:41
[2025-06-26 16:15:54.814]                              0.50.4 is out of date! Please                     
[2025-06-26 16:15:54.814]                              upgrade to version 0.51.0 of                      
[2025-06-26 16:15:54.815]                              inference for the latest features                 
[2025-06-26 16:15:54.815]                              and bug fixes by running `pip                     
[2025-06-26 16:15:54.815]                              install --upgrade inference`.                     
[2025-06-26 16:15:55.194] [34mComfyUI-ImageCompare Nodes: [92mLoaded[0m
[2025-06-26 16:15:55.198] ### Loading: ComfyUI-Impact-Pack (V8.15.3)
[2025-06-26 16:15:55.257] [Impact Pack] Wildcards loading done.
[2025-06-26 16:15:55.261] ### Loading: ComfyUI-Impact-Subpack (V1.3.2)
[2025-06-26 16:15:55.264] [Impact Pack/Subpack] Using folder_paths to determine whitelist path: D:\stable_diffusion\ComfyUI\user\default\ComfyUI-Impact-Subpack\model-whitelist.txt
[2025-06-26 16:15:55.264] [Impact Pack/Subpack] Ensured whitelist directory exists: D:\stable_diffusion\ComfyUI\user\default\ComfyUI-Impact-Subpack
[2025-06-26 16:15:55.265] [Impact Pack/Subpack] Loaded 0 model(s) from whitelist: D:\stable_diffusion\ComfyUI\user\default\ComfyUI-Impact-Subpack\model-whitelist.txt
[2025-06-26 16:15:55.266] [Impact Subpack] ultralytics_bbox: D:\stable_diffusion\ComfyUI\models\ultralytics\bbox
[2025-06-26 16:15:55.266] [Impact Subpack] ultralytics_segm: D:\stable_diffusion\ComfyUI\models\ultralytics\segm
[2025-06-26 16:15:55.266] [Impact Subpack] ultralytics_bbox: D:\stable_diffusion\ComfyUI\models\ultralytics\bbox
[2025-06-26 16:15:55.266] [Impact Subpack] ultralytics_segm: D:\stable_diffusion\ComfyUI\models\ultralytics\segm
[2025-06-26 16:15:55.303] Found LoRA roots:
 - D:/stable_diffusion/1_Models/SDXL/loras
 - D:/stable_diffusion/ComfyUI/models/loras
[2025-06-26 16:15:55.303] Found checkpoint roots:
 - D:/stable_diffusion/1_Models/SDXL/checkpoints
 - D:/stable_diffusion/ComfyUI/models/checkpoints
 - D:/stable_diffusion/ComfyUI/models/diffusers
 - D:/stable_diffusion/ComfyUI/models/diffusion_models
 - D:/stable_diffusion/ComfyUI/models/unet
[2025-06-26 16:15:55.329] Saved folder paths to settings.json
[2025-06-26 16:15:55.458] Metadata collection hooks installed for runtime values
[2025-06-26 16:15:55.458] ComfyUI Metadata Collector initialized
[2025-06-26 16:15:55.459] Example images path: None
[2025-06-26 16:15:55.459] Added static route /loras_static/root1/preview -> D:/stable_diffusion/1_Models/SDXL/loras
[2025-06-26 16:15:55.460] Added static route /loras_static/root2/preview -> D:/stable_diffusion/ComfyUI/models/loras
[2025-06-26 16:15:55.460] Added static route /checkpoints_static/root1/preview -> D:/stable_diffusion/1_Models/SDXL/checkpoints
[2025-06-26 16:15:55.460] Added static route /checkpoints_static/root2/preview -> D:/stable_diffusion/ComfyUI/models/checkpoints
[2025-06-26 16:15:55.460] Added static route /checkpoints_static/root3/preview -> D:/stable_diffusion/ComfyUI/models/diffusers
[2025-06-26 16:15:55.461] Added static route /checkpoints_static/root4/preview -> D:/stable_diffusion/ComfyUI/models/diffusion_models
[2025-06-26 16:15:55.461] Added static route /checkpoints_static/root5/preview -> D:/stable_diffusion/ComfyUI/models/unet
[2025-06-26 16:15:55.879] FutureWarning: Importing from timm.models.layers is deprecated, please import via timm.layers
[2025-06-26 16:15:55.907] Imported node: DT_Flatten_Colors
[2025-06-26 16:15:55.908] Imported node: DT_FontText
[2025-06-26 16:15:55.908] Imported node: DT_GenerateNoise
[2025-06-26 16:15:55.911] Imported node: DT_Glitch_This
[2025-06-26 16:15:55.912] Imported node: DT_Hue_Rotation
[2025-06-26 16:15:55.913] Imported node: DT_Load_Picture_Index
[2025-06-26 16:15:55.945] Imported node: DT_PILGram
[2025-06-26 16:15:55.950] Imported node: DT_Pixel_Sort
[2025-06-26 16:15:56.310] Imported node: DT_Play_Sound_At_Execution
[2025-06-26 16:15:56.571] Imported node: DT_PromptGen
[2025-06-26 16:15:56.573] Imported node: DT_Solid_Color
[2025-06-26 16:15:56.574] Imported node: DT_Swap_Color_Mode
[2025-06-26 16:15:56.574] Imported node: DT_Flatten_Colors
[2025-06-26 16:15:56.574] Imported node: DT_FontText
[2025-06-26 16:15:56.574] Imported node: DT_GenerateNoise
[2025-06-26 16:15:56.574] Imported node: DT_Glitch_This
[2025-06-26 16:15:56.574] Imported node: DT_Hue_Rotation
[2025-06-26 16:15:56.574] Imported node: DT_Load_Picture_Index
[2025-06-26 16:15:56.574] Imported node: DT_PILGram
[2025-06-26 16:15:56.574] Imported node: DT_Pixel_Sort
[2025-06-26 16:15:56.574] Imported node: DT_Play_Sound_At_Execution
[2025-06-26 16:15:56.574] Imported node: DT_PromptGen
[2025-06-26 16:15:56.574] Imported node: DT_Solid_Color
[2025-06-26 16:15:56.574] Imported node: DT_Swap_Color_Mode
[2025-06-26 16:15:56.579] (pysssss:WD14Tagger) [DEBUG] Available ORT providers: AzureExecutionProvider, CPUExecutionProvider
[2025-06-26 16:15:56.579] (pysssss:WD14Tagger) [DEBUG] Using ORT providers: CUDAExecutionProvider, CPUExecutionProvider
[2025-06-26 16:15:56.587] SupervisionWarnings: BoundingBoxAnnotator is deprecated: `BoundingBoxAnnotator` is deprecated and has been renamed to `BoxAnnotator`. `BoundingBoxAnnotator` will be removed in supervision-0.26.0.
[2025-06-26 16:15:56.623] ------------------------------------------
[2025-06-26 16:15:56.623] [34mComfyroll Studio v1.76 : [92m 175 Nodes Loaded[0m
[2025-06-26 16:15:56.623] ------------------------------------------
[2025-06-26 16:15:56.623] ** For changes, please see patch notes at https://github.com/Suzie1/ComfyUI_Comfyroll_CustomNodes/blob/main/Patch_Notes.md
[2025-06-26 16:15:56.623] ** For help, please see the wiki at https://github.com/Suzie1/ComfyUI_Comfyroll_CustomNodes/wiki
[2025-06-26 16:15:56.623] ------------------------------------------
[2025-06-26 16:15:56.635] [36;20m[D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using ckpts path: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux\ckpts[0m
[2025-06-26 16:15:56.636] [36;20m[D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using symlinks: False[0m
[2025-06-26 16:15:56.637] [36;20m[D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using ort providers: ['CUDAExecutionProvider', 'DirectMLExecutionProvider', 'OpenVINOExecutionProvider', 'ROCMExecutionProvider', 'CPUExecutionProvider', 'CoreMLExecutionProvider'][0m
[2025-06-26 16:15:56.710] ------------------------------------
[2025-06-26 16:15:56.710] Loading nodes from: MY_NODES
[2025-06-26 16:15:56.711]   -> Loaded nodes from simple_slider.py
[2025-06-26 16:15:56.711] ------------------------------------
[2025-06-26 16:15:56.853] 
[2025-06-26 16:15:56.853] [92m[rgthree-comfy] Loaded 47 extraordinary nodes. 🎉[0m
[2025-06-26 16:15:56.853] 
[2025-06-26 16:15:57.746] [34mWAS Node Suite: [0mOpenCV Python FFMPEG support is enabled[0m
[2025-06-26 16:15:57.746] [34mWAS Node Suite [93mWarning: [0m`ffmpeg_bin_path` is not set in `D:\stable_diffusion\ComfyUI\custom_nodes\was-ns\was_suite_config.json` config file. Will attempt to use system ffmpeg binaries if available.[0m
[2025-06-26 16:15:58.454] [34mWAS Node Suite: [0mFinished.[0m [32mLoaded[0m [0m220[0m [32mnodes successfully.[0m
[2025-06-26 16:15:58.454] 
	[3m[93m"Your time is now. Start where you are and never stop."[0m[3m - Roy T. Bennett[0m
[2025-06-26 16:15:58.454] 
[2025-06-26 16:15:58.472] ### Loading: ComfyUI-Manager (V3.30.4)
[2025-06-26 16:15:58.473] [ComfyUI-Manager] network_mode: public
[2025-06-26 16:15:58.473] ### ComfyUI Revision: UNKNOWN (The currently installed ComfyUI is not a Git repository)
[2025-06-26 16:15:58.507] 
Import times for custom nodes:
[2025-06-26 16:15:58.507]    0.0 seconds: C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI\custom_nodes\websocket_image_save.py
[2025-06-26 16:15:58.507]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyUI-ImageCompare
[2025-06-26 16:15:58.507]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\MY_NODES
[2025-06-26 16:15:58.507]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\quick-connections
[2025-06-26 16:15:58.507]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-ultralytics-yolo
[2025-06-26 16:15:58.508]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\sdxl-recommended-res-calc
[2025-06-26 16:15:58.508]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\masquerade-nodes-comfyui
[2025-06-26 16:15:58.508]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyUI_Cutoff
[2025-06-26 16:15:58.508]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\lora-info
[2025-06-26 16:15:58.508]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-mxtoolkit
[2025-06-26 16:15:58.508]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyUI-Contextual-SAM2
[2025-06-26 16:15:58.508]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\civitai_comfy_nodes
[2025-06-26 16:15:58.508]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-lora-auto-trigger-words
[2025-06-26 16:15:58.508]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-inpaint-cropandstitch
[2025-06-26 16:15:58.508]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-wd14-tagger
[2025-06-26 16:15:58.508]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-autocomplete-plus
[2025-06-26 16:15:58.508]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-impact-subpack
[2025-06-26 16:15:58.508]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyUI-YoloWorld-EfficientSAM
[2025-06-26 16:15:58.508]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-inpaint-nodes
[2025-06-26 16:15:58.509]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-model-downloader
[2025-06-26 16:15:58.509]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-custom-scripts
[2025-06-26 16:15:58.509]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_essentials
[2025-06-26 16:15:58.509]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_ultimatesdupscale
[2025-06-26 16:15:58.509]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyMath
[2025-06-26 16:15:58.509]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyUI-segment-anything-2
[2025-06-26 16:15:58.509]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-kjnodes
[2025-06-26 16:15:58.509]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyUI_Comfyroll_CustomNodes
[2025-06-26 16:15:58.509]    0.0 seconds: C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI\custom_nodes\ComfyUI-Manager
[2025-06-26 16:15:58.509]    0.1 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux
[2025-06-26 16:15:58.509]    0.1 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-impact-pack
[2025-06-26 16:15:58.509]    0.1 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\rgthree-comfy
[2025-06-26 16:15:58.509]    0.2 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-lora-manager
[2025-06-26 16:15:58.509]    0.4 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-sam2
[2025-06-26 16:15:58.509]    0.7 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyUI-Vextra-Nodes
[2025-06-26 16:15:58.509]    0.7 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-art-venture
[2025-06-26 16:15:58.509]    0.9 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-easy-use
[2025-06-26 16:15:58.509]    0.9 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyUI-GroundedSAM2
[2025-06-26 16:15:58.509]    1.6 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\was-ns
[2025-06-26 16:15:58.509] 
[2025-06-26 16:15:58.816] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/alter-list.json
[2025-06-26 16:15:58.850] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/model-list.json
[2025-06-26 16:15:58.880] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/github-stats.json
[2025-06-26 16:15:58.931] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/extension-node-map.json
[2025-06-26 16:15:58.974] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json
[2025-06-26 16:15:59.143] Failed to initialize database. Please ensure you have installed the latest requirements. If the error persists, please report this as in future the database will be required: (sqlite3.OperationalError) unable to open database file
(Background on this error at: https://sqlalche.me/e/20/e3q8)
[2025-06-26 16:15:59.182] Metadata collection hooks installed for runtime values
[2025-06-26 16:15:59.182] ComfyUI Metadata Collector initialized
[2025-06-26 16:15:59.182] LoRA Manager: All services initialized and background tasks scheduled
[2025-06-26 16:15:59.182] Starting server

[2025-06-26 16:15:59.182] Cache files disabled for lora, skipping load
[2025-06-26 16:15:59.183] Cache files disabled for checkpoint, skipping load
[2025-06-26 16:15:59.186] To see the GUI go to: http://127.0.0.1:8000
[2025-06-26 16:15:59.187] Recipe cache initialized in 0.00 seconds. Found 0 recipes
[2025-06-26 16:15:59.202] Checkpoint cache initialized in 0.01 seconds. Found 12 models
[2025-06-26 16:15:59.254] Lora cache initialized in 0.05 seconds. Found 60 models
[2025-06-26 16:16:01.999] FETCH ComfyRegistry Data: 5/90
[2025-06-26 16:16:02.856] [Autocomplete-Plus] CSV file status:
  * Danbooru -> base: True, extra: 
  * E621     -> base: True, extra: 
[2025-06-26 16:16:05.675] FETCH ComfyRegistry Data: 10/90
[2025-06-26 16:16:09.532] FETCH ComfyRegistry Data: 15/90
[2025-06-26 16:16:13.050] FETCH ComfyRegistry Data: 20/90
[2025-06-26 16:16:16.522] FETCH ComfyRegistry Data: 25/90
[2025-06-26 16:16:20.697] FETCH ComfyRegistry Data: 30/90
[2025-06-26 16:16:24.246] FETCH ComfyRegistry Data: 35/90
[2025-06-26 16:16:27.707] FETCH ComfyRegistry Data: 40/90
[2025-06-26 16:16:31.302] FETCH ComfyRegistry Data: 45/90
[2025-06-26 16:16:34.847] FETCH ComfyRegistry Data: 50/90
[2025-06-26 16:16:38.345] FETCH ComfyRegistry Data: 55/90
[2025-06-26 16:16:41.934] FETCH ComfyRegistry Data: 60/90
[2025-06-26 16:16:45.517] FETCH ComfyRegistry Data: 65/90
[2025-06-26 16:16:49.238] FETCH ComfyRegistry Data: 70/90
[2025-06-26 16:16:52.669] FETCH ComfyRegistry Data: 75/90
[2025-06-26 16:16:56.079] FETCH ComfyRegistry Data: 80/90
[2025-06-26 16:16:59.499] FETCH ComfyRegistry Data: 85/90
[2025-06-26 16:17:02.928] FETCH ComfyRegistry Data: 90/90
[2025-06-26 16:17:03.430] FETCH ComfyRegistry Data [DONE]
[2025-06-26 16:17:03.566] [ComfyUI-Manager] default cache updated: https://api.comfy.org/nodes
[2025-06-26 16:17:03.603] nightly_channel: 
[2025-06-26 16:17:03.604] https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/remote
[2025-06-26 16:17:03.604] FETCH DATA from: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json [DONE]
[2025-06-26 16:17:03.750] [ComfyUI-Manager] All startup tasks have been completed.
[2025-06-26 16:18:37.076] [Autocomplete-Plus] CSV file status:
  * Danbooru -> base: True, extra: 
  * E621     -> base: True, extra: 
