## ComfyUI-Manager: installing dependencies done.
[2025-06-26 17:07:13.622] ** ComfyUI startup time: 2025-06-26 17:07:13.622
[2025-06-26 17:07:13.622] ** Platform: Windows
[2025-06-26 17:07:13.622] ** Python version: 3.12.9 (main, Feb 12 2025, 14:52:31) [MSC v.1942 64 bit (AMD64)]
[2025-06-26 17:07:13.622] ** Python executable: D:\stable_diffusion\ComfyUI\.venv\Scripts\python.exe
[2025-06-26 17:07:13.622] ** ComfyUI Path: C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI
[2025-06-26 17:07:13.622] ** ComfyUI Base Folder Path: C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI
[2025-06-26 17:07:13.622] ** User directory: D:\stable_diffusion\ComfyUI\user
[2025-06-26 17:07:13.634] ** ComfyUI-Manager config path: D:\stable_diffusion\ComfyUI\user\default\ComfyUI-Manager\config.ini
[2025-06-26 17:07:13.634] ** Log path: D:\stable_diffusion\ComfyUI\user\comfyui.log
[ComfyUI-Manager] Failed to restore comfyui-frontend-package
[2025-06-26 17:07:15.376] expected str, bytes or os.PathLike object, not NoneType
[2025-06-26 17:07:15.377] 
Prestartup times for custom nodes:
[2025-06-26 17:07:15.377]    4.5 seconds: C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI\custom_nodes\ComfyUI-Manager
[2025-06-26 17:07:15.377] 
[2025-06-26 17:07:17.503] Checkpoint files will always be loaded safely.
[2025-06-26 17:07:18.251] Total VRAM 6144 MB, total RAM 28524 MB
[2025-06-26 17:07:18.252] pytorch version: 2.7.0+cu128
[2025-06-26 17:07:18.253] Set vram state to: NORMAL_VRAM
[2025-06-26 17:07:18.253] Device: cuda:0 NVIDIA GeForce RTX 3060 Laptop GPU : cudaMallocAsync
[2025-06-26 17:07:20.148] Using pytorch attention
[2025-06-26 17:07:21.948] Python version: 3.12.9 (main, Feb 12 2025, 14:52:31) [MSC v.1942 64 bit (AMD64)]
[2025-06-26 17:07:21.948] ComfyUI version: 0.3.41
[2025-06-26 17:07:22.120] [Prompt Server] web root: C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI\web_custom_versions\desktop_app
[2025-06-26 17:07:23.301] Adding D:\stable_diffusion\ComfyUI\custom_nodes to sys.path
[2025-06-26 17:07:23.302] Adding C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI\custom_nodes to sys.path
[2025-06-26 17:07:23.346] D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-art-venture\modules\utils.py:9: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  import pkg_resources
[2025-06-26 17:07:23.613] Could not find efficiency nodes
[2025-06-26 17:07:23.663] [36;20m[comfyui_controlnet_aux] | INFO -> Using ckpts path: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux\ckpts[0m
[2025-06-26 17:07:23.664] [36;20m[comfyui_controlnet_aux] | INFO -> Using symlinks: False[0m
[2025-06-26 17:07:23.664] [36;20m[comfyui_controlnet_aux] | INFO -> Using ort providers: ['CUDAExecutionProvider', 'DirectMLExecutionProvider', 'OpenVINOExecutionProvider', 'ROCMExecutionProvider', 'CPUExecutionProvider', 'CoreMLExecutionProvider'][0m
[2025-06-26 17:07:23.983] D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux\node_wrappers\dwpose.py:26: UserWarning: DWPose: Onnxruntime not found or doesn't come with acceleration providers, switch to OpenCV with CPU device. DWPose might run very slowly
  warnings.warn("DWPose: Onnxruntime not found or doesn't come with acceleration providers, switch to OpenCV with CPU device. DWPose might run very slowly")
[2025-06-26 17:07:24.013] Loaded ControlNetPreprocessors nodes from D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux
[2025-06-26 17:07:24.015] Could not find AdvancedControlNet nodes
[2025-06-26 17:07:24.017] Could not find AnimateDiff nodes
[2025-06-26 17:07:24.019] Could not find IPAdapter nodes
[2025-06-26 17:07:24.027] Could not find VideoHelperSuite nodes
[2025-06-26 17:07:24.030] Could not load ImpactPack nodes Could not find ImpactPack nodes
[2025-06-26 17:07:25.043] [34m[ComfyUI-Easy-Use] server: [0mv1.3.0 [92mLoaded[0m
[2025-06-26 17:07:25.043] [34m[ComfyUI-Easy-Use] web root: [0mD:\stable_diffusion\ComfyUI\custom_nodes\comfyui-easy-use\web_version/v2 [92mLoaded[0m
[2025-06-26 17:07:25.656] [06/26/25 17:07:25] WARNING  Your inference package version      __init__.py:41
[2025-06-26 17:07:25.656]                              0.50.4 is out of date! Please                     
[2025-06-26 17:07:25.656]                              upgrade to version 0.51.0 of                      
[2025-06-26 17:07:25.657]                              inference for the latest features                 
[2025-06-26 17:07:25.657]                              and bug fixes by running `pip                     
[2025-06-26 17:07:25.657]                              install --upgrade inference`.                     
[2025-06-26 17:07:26.067] [34mComfyUI-ImageCompare Nodes: [92mLoaded[0m
[2025-06-26 17:07:26.070] ### Loading: ComfyUI-Impact-Pack (V8.15.3)
[2025-06-26 17:07:26.133] [Impact Pack] Wildcards loading done.
[2025-06-26 17:07:26.137] ### Loading: ComfyUI-Impact-Subpack (V1.3.2)
[2025-06-26 17:07:26.141] [Impact Pack/Subpack] Using folder_paths to determine whitelist path: D:\stable_diffusion\ComfyUI\user\default\ComfyUI-Impact-Subpack\model-whitelist.txt
[2025-06-26 17:07:26.142] [Impact Pack/Subpack] Ensured whitelist directory exists: D:\stable_diffusion\ComfyUI\user\default\ComfyUI-Impact-Subpack
[2025-06-26 17:07:26.142] [Impact Pack/Subpack] Loaded 0 model(s) from whitelist: D:\stable_diffusion\ComfyUI\user\default\ComfyUI-Impact-Subpack\model-whitelist.txt
[2025-06-26 17:07:26.145] [Impact Subpack] ultralytics_bbox: D:\stable_diffusion\ComfyUI\models\ultralytics\bbox
[2025-06-26 17:07:26.145] [Impact Subpack] ultralytics_segm: D:\stable_diffusion\ComfyUI\models\ultralytics\segm
[2025-06-26 17:07:26.145] [Impact Subpack] ultralytics_bbox: D:\stable_diffusion\ComfyUI\models\ultralytics\bbox
[2025-06-26 17:07:26.145] [Impact Subpack] ultralytics_segm: D:\stable_diffusion\ComfyUI\models\ultralytics\segm
[2025-06-26 17:07:26.195] Found LoRA roots:
 - D:/stable_diffusion/1_Models/SDXL/loras
 - D:/stable_diffusion/ComfyUI/models/loras
[2025-06-26 17:07:26.195] Found checkpoint roots:
 - D:/stable_diffusion/1_Models/SDXL/checkpoints
 - D:/stable_diffusion/ComfyUI/models/checkpoints
 - D:/stable_diffusion/ComfyUI/models/diffusers
 - D:/stable_diffusion/ComfyUI/models/diffusion_models
 - D:/stable_diffusion/ComfyUI/models/unet
[2025-06-26 17:07:26.218] Saved folder paths to settings.json
[2025-06-26 17:07:26.364] Metadata collection hooks installed for runtime values
[2025-06-26 17:07:26.364] ComfyUI Metadata Collector initialized
[2025-06-26 17:07:26.365] Example images path: None
[2025-06-26 17:07:26.365] Added static route /loras_static/root1/preview -> D:/stable_diffusion/1_Models/SDXL/loras
[2025-06-26 17:07:26.366] Added static route /loras_static/root2/preview -> D:/stable_diffusion/ComfyUI/models/loras
[2025-06-26 17:07:26.366] Added static route /checkpoints_static/root1/preview -> D:/stable_diffusion/1_Models/SDXL/checkpoints
[2025-06-26 17:07:26.366] Added static route /checkpoints_static/root2/preview -> D:/stable_diffusion/ComfyUI/models/checkpoints
[2025-06-26 17:07:26.366] Added static route /checkpoints_static/root3/preview -> D:/stable_diffusion/ComfyUI/models/diffusers
[2025-06-26 17:07:26.367] Added static route /checkpoints_static/root4/preview -> D:/stable_diffusion/ComfyUI/models/diffusion_models
[2025-06-26 17:07:26.368] Added static route /checkpoints_static/root5/preview -> D:/stable_diffusion/ComfyUI/models/unet
[2025-06-26 17:07:26.823] FutureWarning: Importing from timm.models.layers is deprecated, please import via timm.layers
[2025-06-26 17:07:26.855] Imported node: DT_Flatten_Colors
[2025-06-26 17:07:26.855] Imported node: DT_FontText
[2025-06-26 17:07:26.856] Imported node: DT_GenerateNoise
[2025-06-26 17:07:26.859] Imported node: DT_Glitch_This
[2025-06-26 17:07:26.859] Imported node: DT_Hue_Rotation
[2025-06-26 17:07:26.861] Imported node: DT_Load_Picture_Index
[2025-06-26 17:07:26.898] Imported node: DT_PILGram
[2025-06-26 17:07:26.904] Imported node: DT_Pixel_Sort
[2025-06-26 17:07:27.279] Imported node: DT_Play_Sound_At_Execution
[2025-06-26 17:07:27.537] Imported node: DT_PromptGen
[2025-06-26 17:07:27.538] Imported node: DT_Solid_Color
[2025-06-26 17:07:27.538] Imported node: DT_Swap_Color_Mode
[2025-06-26 17:07:27.538] Imported node: DT_Flatten_Colors
[2025-06-26 17:07:27.538] Imported node: DT_FontText
[2025-06-26 17:07:27.539] Imported node: DT_GenerateNoise
[2025-06-26 17:07:27.539] Imported node: DT_Glitch_This
[2025-06-26 17:07:27.539] Imported node: DT_Hue_Rotation
[2025-06-26 17:07:27.539] Imported node: DT_Load_Picture_Index
[2025-06-26 17:07:27.539] Imported node: DT_PILGram
[2025-06-26 17:07:27.539] Imported node: DT_Pixel_Sort
[2025-06-26 17:07:27.539] Imported node: DT_Play_Sound_At_Execution
[2025-06-26 17:07:27.539] Imported node: DT_PromptGen
[2025-06-26 17:07:27.539] Imported node: DT_Solid_Color
[2025-06-26 17:07:27.539] Imported node: DT_Swap_Color_Mode
[2025-06-26 17:07:27.546] (pysssss:WD14Tagger) [DEBUG] Available ORT providers: AzureExecutionProvider, CPUExecutionProvider
[2025-06-26 17:07:27.546] (pysssss:WD14Tagger) [DEBUG] Using ORT providers: CUDAExecutionProvider, CPUExecutionProvider
[2025-06-26 17:07:27.555] SupervisionWarnings: BoundingBoxAnnotator is deprecated: `BoundingBoxAnnotator` is deprecated and has been renamed to `BoxAnnotator`. `BoundingBoxAnnotator` will be removed in supervision-0.26.0.
[2025-06-26 17:07:27.598] ------------------------------------------
[2025-06-26 17:07:27.598] [34mComfyroll Studio v1.76 : [92m 175 Nodes Loaded[0m
[2025-06-26 17:07:27.598] ------------------------------------------
[2025-06-26 17:07:27.598] ** For changes, please see patch notes at https://github.com/Suzie1/ComfyUI_Comfyroll_CustomNodes/blob/main/Patch_Notes.md
[2025-06-26 17:07:27.598] ** For help, please see the wiki at https://github.com/Suzie1/ComfyUI_Comfyroll_CustomNodes/wiki
[2025-06-26 17:07:27.598] ------------------------------------------
[2025-06-26 17:07:27.612] [36;20m[D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using ckpts path: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux\ckpts[0m
[2025-06-26 17:07:27.614] [36;20m[D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using symlinks: False[0m
[2025-06-26 17:07:27.615] [36;20m[D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using ort providers: ['CUDAExecutionProvider', 'DirectMLExecutionProvider', 'OpenVINOExecutionProvider', 'ROCMExecutionProvider', 'CPUExecutionProvider', 'CoreMLExecutionProvider'][0m
[2025-06-26 17:07:27.709] ------------------------------------
[2025-06-26 17:07:27.709] Loading nodes from: MY_NODES
[2025-06-26 17:07:27.710]   -> Loaded nodes from simple_slider.py
[2025-06-26 17:07:27.710] ------------------------------------
[2025-06-26 17:07:27.819] 
[2025-06-26 17:07:27.819] [92m[rgthree-comfy] Loaded 47 epic nodes. 🎉[0m
[2025-06-26 17:07:27.819] 
[2025-06-26 17:07:28.787] [34mWAS Node Suite: [0mOpenCV Python FFMPEG support is enabled[0m
[2025-06-26 17:07:28.787] [34mWAS Node Suite [93mWarning: [0m`ffmpeg_bin_path` is not set in `D:\stable_diffusion\ComfyUI\custom_nodes\was-ns\was_suite_config.json` config file. Will attempt to use system ffmpeg binaries if available.[0m
[2025-06-26 17:07:29.538] [34mWAS Node Suite: [0mFinished.[0m [32mLoaded[0m [0m220[0m [32mnodes successfully.[0m
[2025-06-26 17:07:29.538] 
	[3m[93m"The journey of a thousand miles begins with one step."[0m[3m - Lao Tzu[0m
[2025-06-26 17:07:29.538] 
[2025-06-26 17:07:29.562] ### Loading: ComfyUI-Manager (V3.30.4)
[2025-06-26 17:07:29.562] [ComfyUI-Manager] network_mode: public
[2025-06-26 17:07:29.563] ### ComfyUI Revision: UNKNOWN (The currently installed ComfyUI is not a Git repository)
[2025-06-26 17:07:29.601] 
Import times for custom nodes:
[2025-06-26 17:07:29.601]    0.0 seconds: C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI\custom_nodes\websocket_image_save.py
[2025-06-26 17:07:29.601]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyUI-ImageCompare
[2025-06-26 17:07:29.602]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\MY_NODES
[2025-06-26 17:07:29.602]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyUI_Cutoff
[2025-06-26 17:07:29.602]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-ultralytics-yolo
[2025-06-26 17:07:29.602]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\sdxl-recommended-res-calc
[2025-06-26 17:07:29.602]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\quick-connections
[2025-06-26 17:07:29.602]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\masquerade-nodes-comfyui
[2025-06-26 17:07:29.602]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\lora-info
[2025-06-26 17:07:29.602]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-mxtoolkit
[2025-06-26 17:07:29.602]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyUI-Contextual-SAM2
[2025-06-26 17:07:29.602]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\civitai_comfy_nodes
[2025-06-26 17:07:29.602]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-lora-auto-trigger-words
[2025-06-26 17:07:29.602]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-wd14-tagger
[2025-06-26 17:07:29.602]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-inpaint-cropandstitch
[2025-06-26 17:07:29.602]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-autocomplete-plus
[2025-06-26 17:07:29.602]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-inpaint-nodes
[2025-06-26 17:07:29.602]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-model-downloader
[2025-06-26 17:07:29.602]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyUI-YoloWorld-EfficientSAM
[2025-06-26 17:07:29.602]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-impact-subpack
[2025-06-26 17:07:29.604]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-custom-scripts
[2025-06-26 17:07:29.604]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_essentials
[2025-06-26 17:07:29.604]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyMath
[2025-06-26 17:07:29.604]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyUI-segment-anything-2
[2025-06-26 17:07:29.604]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_ultimatesdupscale
[2025-06-26 17:07:29.604]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-kjnodes
[2025-06-26 17:07:29.604]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyUI_Comfyroll_CustomNodes
[2025-06-26 17:07:29.604]    0.1 seconds: C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI\custom_nodes\ComfyUI-Manager
[2025-06-26 17:07:29.604]    0.1 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux
[2025-06-26 17:07:29.604]    0.1 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-impact-pack
[2025-06-26 17:07:29.604]    0.1 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\rgthree-comfy
[2025-06-26 17:07:29.604]    0.2 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-lora-manager
[2025-06-26 17:07:29.604]    0.4 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-sam2
[2025-06-26 17:07:29.604]    0.7 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyUI-Vextra-Nodes
[2025-06-26 17:07:29.604]    0.7 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-art-venture
[2025-06-26 17:07:29.604]    1.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-easy-use
[2025-06-26 17:07:29.604]    1.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyUI-GroundedSAM2
[2025-06-26 17:07:29.604]    1.7 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\was-ns
[2025-06-26 17:07:29.605] 
[2025-06-26 17:07:29.912] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/alter-list.json
[2025-06-26 17:07:29.962] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/model-list.json
[2025-06-26 17:07:29.988] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/github-stats.json
[2025-06-26 17:07:30.051] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json
[2025-06-26 17:07:30.092] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/extension-node-map.json
[2025-06-26 17:07:30.288] Failed to initialize database. Please ensure you have installed the latest requirements. If the error persists, please report this as in future the database will be required: (sqlite3.OperationalError) unable to open database file
(Background on this error at: https://sqlalche.me/e/20/e3q8)
[2025-06-26 17:07:30.329] Metadata collection hooks installed for runtime values
[2025-06-26 17:07:30.329] ComfyUI Metadata Collector initialized
[2025-06-26 17:07:30.330] LoRA Manager: All services initialized and background tasks scheduled
[2025-06-26 17:07:30.330] Starting server

[2025-06-26 17:07:30.330] Cache files disabled for lora, skipping load
[2025-06-26 17:07:30.331] Cache files disabled for checkpoint, skipping load
[2025-06-26 17:07:30.333] To see the GUI go to: http://127.0.0.1:8000
[2025-06-26 17:07:30.335] Recipe cache initialized in 0.00 seconds. Found 0 recipes
[2025-06-26 17:07:30.354] Checkpoint cache initialized in 0.02 seconds. Found 12 models
[2025-06-26 17:07:30.412] Lora cache initialized in 0.05 seconds. Found 60 models
[2025-06-26 17:07:33.043] FETCH ComfyRegistry Data: 5/90
[2025-06-26 17:07:34.229] [Autocomplete-Plus] CSV file status:
  * Danbooru -> base: True, extra: 
  * E621     -> base: True, extra: 
[2025-06-26 17:07:36.506] FETCH ComfyRegistry Data: 10/90
[2025-06-26 17:07:40.786] FETCH ComfyRegistry Data: 15/90
[2025-06-26 17:07:44.273] FETCH ComfyRegistry Data: 20/90
[2025-06-26 17:07:47.729] FETCH ComfyRegistry Data: 25/90
[2025-06-26 17:07:51.187] FETCH ComfyRegistry Data: 30/90
[2025-06-26 17:07:54.760] FETCH ComfyRegistry Data: 35/90
[2025-06-26 17:07:58.173] FETCH ComfyRegistry Data: 40/90
[2025-06-26 17:08:01.643] FETCH ComfyRegistry Data: 45/90
[2025-06-26 17:08:05.107] FETCH ComfyRegistry Data: 50/90
[2025-06-26 17:08:08.609] FETCH ComfyRegistry Data: 55/90
[2025-06-26 17:08:12.011] FETCH ComfyRegistry Data: 60/90
[2025-06-26 17:08:15.587] FETCH ComfyRegistry Data: 65/90
[2025-06-26 17:08:19.070] FETCH ComfyRegistry Data: 70/90
[2025-06-26 17:08:23.555] FETCH ComfyRegistry Data: 75/90
[2025-06-26 17:08:27.170] FETCH ComfyRegistry Data: 80/90
[2025-06-26 17:08:28.351] [Autocomplete-Plus] CSV file status:
  * Danbooru -> base: True, extra: 
  * E621     -> base: True, extra: 
[2025-06-26 17:08:30.647] FETCH ComfyRegistry Data: 85/90
[2025-06-26 17:08:34.297] FETCH ComfyRegistry Data: 90/90
[2025-06-26 17:08:34.798] FETCH ComfyRegistry Data [DONE]
[2025-06-26 17:08:34.959] [ComfyUI-Manager] default cache updated: https://api.comfy.org/nodes
[2025-06-26 17:08:35.005] nightly_channel: 
[2025-06-26 17:08:35.005] https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/remote
[2025-06-26 17:08:35.006] FETCH DATA from: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json [DONE]
[2025-06-26 17:08:35.160] [ComfyUI-Manager] All startup tasks have been completed.
[2025-06-26 17:08:51.512] [Autocomplete-Plus] CSV file status:
  * Danbooru -> base: True, extra: 
  * E621     -> base: True, extra: 
[2025-06-26 17:09:53.316] [Autocomplete-Plus] CSV file status:
  * Danbooru -> base: True, extra: 
  * E621     -> base: True, extra: 
[2025-06-26 17:22:27.221] [Autocomplete-Plus] CSV file status:
  * Danbooru -> base: True, extra: 
  * E621     -> base: True, extra: 
[2025-06-26 17:23:37.986] [Autocomplete-Plus] CSV file status:
  * Danbooru -> base: True, extra: 
  * E621     -> base: True, extra: 
[2025-06-26 17:24:00.286] [Autocomplete-Plus] CSV file status:
  * Danbooru -> base: True, extra: 
  * E621     -> base: True, extra: 
[2025-06-26 17:24:29.240] [Autocomplete-Plus] CSV file status:
  * Danbooru -> base: True, extra: 
  * E621     -> base: True, extra: 
[2025-06-26 17:28:01.639] [Autocomplete-Plus] CSV file status:
  * Danbooru -> base: True, extra: 
  * E621     -> base: True, extra: 
