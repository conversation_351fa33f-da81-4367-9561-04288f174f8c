## ComfyUI-Manager: installing dependencies done.
[2025-06-26 19:42:40.493] ** ComfyUI startup time: 2025-06-26 19:42:40.493
[2025-06-26 19:42:40.493] ** Platform: Windows
[2025-06-26 19:42:40.493] ** Python version: 3.12.9 (main, Feb 12 2025, 14:52:31) [MSC v.1942 64 bit (AMD64)]
[2025-06-26 19:42:40.493] ** Python executable: D:\stable_diffusion\ComfyUI\.venv\Scripts\python.exe
[2025-06-26 19:42:40.493] ** ComfyUI Path: C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI
[2025-06-26 19:42:40.494] ** ComfyUI Base Folder Path: C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI
[2025-06-26 19:42:40.494] ** User directory: D:\stable_diffusion\ComfyUI\user
[2025-06-26 19:42:40.539] ** ComfyUI-Manager config path: D:\stable_diffusion\ComfyUI\user\default\ComfyUI-Manager\config.ini
[2025-06-26 19:42:40.539] ** Log path: D:\stable_diffusion\ComfyUI\user\comfyui.log
[ComfyUI-Manager] Failed to restore comfyui-frontend-package
[2025-06-26 19:42:42.304] expected str, bytes or os.PathLike object, not NoneType
[2025-06-26 19:42:42.307] 
Prestartup times for custom nodes:
[2025-06-26 19:42:42.307]    4.8 seconds: C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI\custom_nodes\ComfyUI-Manager
[2025-06-26 19:42:42.307] 
[2025-06-26 19:42:44.396] Checkpoint files will always be loaded safely.
[2025-06-26 19:42:45.696] Total VRAM 6144 MB, total RAM 28524 MB
[2025-06-26 19:42:45.696] pytorch version: 2.7.0+cu128
[2025-06-26 19:42:45.697] Set vram state to: NORMAL_VRAM
[2025-06-26 19:42:45.697] Device: cuda:0 NVIDIA GeForce RTX 3060 Laptop GPU : cudaMallocAsync
[2025-06-26 19:42:47.643] Using pytorch attention
[2025-06-26 19:42:49.546] Python version: 3.12.9 (main, Feb 12 2025, 14:52:31) [MSC v.1942 64 bit (AMD64)]
[2025-06-26 19:42:49.546] ComfyUI version: 0.3.42
[2025-06-26 19:42:49.748] [Prompt Server] web root: C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI\web_custom_versions\desktop_app
[2025-06-26 19:42:50.986] Adding D:\stable_diffusion\ComfyUI\custom_nodes to sys.path
[2025-06-26 19:42:50.986] Adding C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI\custom_nodes to sys.path
[2025-06-26 19:42:51.185] D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-art-venture\modules\utils.py:9: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  import pkg_resources
[2025-06-26 19:42:51.292] Could not find efficiency nodes
[2025-06-26 19:42:51.342] [36;20m[comfyui_controlnet_aux] | INFO -> Using ckpts path: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux\ckpts[0m
[2025-06-26 19:42:51.343] [36;20m[comfyui_controlnet_aux] | INFO -> Using symlinks: False[0m
[2025-06-26 19:42:51.344] [36;20m[comfyui_controlnet_aux] | INFO -> Using ort providers: ['CUDAExecutionProvider', 'DirectMLExecutionProvider', 'OpenVINOExecutionProvider', 'ROCMExecutionProvider', 'CPUExecutionProvider', 'CoreMLExecutionProvider'][0m
[2025-06-26 19:42:51.683] D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux\node_wrappers\dwpose.py:26: UserWarning: DWPose: Onnxruntime not found or doesn't come with acceleration providers, switch to OpenCV with CPU device. DWPose might run very slowly
  warnings.warn("DWPose: Onnxruntime not found or doesn't come with acceleration providers, switch to OpenCV with CPU device. DWPose might run very slowly")
[2025-06-26 19:42:51.717] Loaded ControlNetPreprocessors nodes from D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux
[2025-06-26 19:42:51.719] Could not find AdvancedControlNet nodes
[2025-06-26 19:42:51.722] Could not find AnimateDiff nodes
[2025-06-26 19:42:51.723] Could not find IPAdapter nodes
[2025-06-26 19:42:51.731] Could not find VideoHelperSuite nodes
[2025-06-26 19:42:51.734] Could not load ImpactPack nodes Could not find ImpactPack nodes
[2025-06-26 19:42:52.960] [34m[ComfyUI-Easy-Use] server: [0mv1.3.0 [92mLoaded[0m
[2025-06-26 19:42:52.960] [34m[ComfyUI-Easy-Use] web root: [0mD:\stable_diffusion\ComfyUI\custom_nodes\comfyui-easy-use\web_version/v2 [92mLoaded[0m
[2025-06-26 19:42:53.665] [06/26/25 19:42:53] WARNING  Your inference package version      __init__.py:41
[2025-06-26 19:42:53.665]                              0.50.4 is out of date! Please                     
[2025-06-26 19:42:53.665]                              upgrade to version 0.51.0 of                      
[2025-06-26 19:42:53.667]                              inference for the latest features                 
[2025-06-26 19:42:53.667]                              and bug fixes by running `pip                     
[2025-06-26 19:42:53.667]                              install --upgrade inference`.                     
[2025-06-26 19:42:54.107] [34mComfyUI-ImageCompare Nodes: [92mLoaded[0m
[2025-06-26 19:42:54.116] ### Loading: ComfyUI-Impact-Pack (V8.15.3)
[2025-06-26 19:42:54.174] [Impact Pack] Wildcards loading done.
[2025-06-26 19:42:54.182] ### Loading: ComfyUI-Impact-Subpack (V1.3.2)
[2025-06-26 19:42:54.185] [Impact Pack/Subpack] Using folder_paths to determine whitelist path: D:\stable_diffusion\ComfyUI\user\default\ComfyUI-Impact-Subpack\model-whitelist.txt
[2025-06-26 19:42:54.185] [Impact Pack/Subpack] Ensured whitelist directory exists: D:\stable_diffusion\ComfyUI\user\default\ComfyUI-Impact-Subpack
[2025-06-26 19:42:54.185] [Impact Pack/Subpack] Loaded 0 model(s) from whitelist: D:\stable_diffusion\ComfyUI\user\default\ComfyUI-Impact-Subpack\model-whitelist.txt
[2025-06-26 19:42:54.188] [Impact Subpack] ultralytics_bbox: D:\stable_diffusion\ComfyUI\models\ultralytics\bbox
[2025-06-26 19:42:54.188] [Impact Subpack] ultralytics_segm: D:\stable_diffusion\ComfyUI\models\ultralytics\segm
[2025-06-26 19:42:54.188] [Impact Subpack] ultralytics_bbox: D:\stable_diffusion\ComfyUI\models\ultralytics\bbox
[2025-06-26 19:42:54.188] [Impact Subpack] ultralytics_segm: D:\stable_diffusion\ComfyUI\models\ultralytics\segm
[2025-06-26 19:42:54.231] Found LoRA roots:
 - D:/stable_diffusion/1_Models/SDXL/loras
 - D:/stable_diffusion/ComfyUI/models/loras
[2025-06-26 19:42:54.231] Found checkpoint roots:
 - D:/stable_diffusion/1_Models/SDXL/checkpoints
 - D:/stable_diffusion/ComfyUI/models/checkpoints
 - D:/stable_diffusion/ComfyUI/models/diffusers
 - D:/stable_diffusion/ComfyUI/models/diffusion_models
 - D:/stable_diffusion/ComfyUI/models/unet
[2025-06-26 19:42:54.261] Saved folder paths to settings.json
[2025-06-26 19:42:54.398] Metadata collection hooks installed for runtime values
[2025-06-26 19:42:54.402] ComfyUI Metadata Collector initialized
[2025-06-26 19:42:54.402] Example images path: None
[2025-06-26 19:42:54.402] Added static route /loras_static/root1/preview -> D:/stable_diffusion/1_Models/SDXL/loras
[2025-06-26 19:42:54.403] Added static route /loras_static/root2/preview -> D:/stable_diffusion/ComfyUI/models/loras
[2025-06-26 19:42:54.403] Added static route /checkpoints_static/root1/preview -> D:/stable_diffusion/1_Models/SDXL/checkpoints
[2025-06-26 19:42:54.403] Added static route /checkpoints_static/root2/preview -> D:/stable_diffusion/ComfyUI/models/checkpoints
[2025-06-26 19:42:54.403] Added static route /checkpoints_static/root3/preview -> D:/stable_diffusion/ComfyUI/models/diffusers
[2025-06-26 19:42:54.404] Added static route /checkpoints_static/root4/preview -> D:/stable_diffusion/ComfyUI/models/diffusion_models
[2025-06-26 19:42:54.404] Added static route /checkpoints_static/root5/preview -> D:/stable_diffusion/ComfyUI/models/unet
[2025-06-26 19:42:54.859] FutureWarning: Importing from timm.models.layers is deprecated, please import via timm.layers
[2025-06-26 19:42:54.893] Imported node: DT_Flatten_Colors
[2025-06-26 19:42:54.895] Imported node: DT_FontText
[2025-06-26 19:42:54.896] Imported node: DT_GenerateNoise
[2025-06-26 19:42:54.899] Imported node: DT_Glitch_This
[2025-06-26 19:42:54.899] Imported node: DT_Hue_Rotation
[2025-06-26 19:42:54.901] Imported node: DT_Load_Picture_Index
[2025-06-26 19:42:54.942] Imported node: DT_PILGram
[2025-06-26 19:42:54.950] Imported node: DT_Pixel_Sort
[2025-06-26 19:42:55.333] Imported node: DT_Play_Sound_At_Execution
[2025-06-26 19:42:55.603] Imported node: DT_PromptGen
[2025-06-26 19:42:55.604] Imported node: DT_Solid_Color
[2025-06-26 19:42:55.605] Imported node: DT_Swap_Color_Mode
[2025-06-26 19:42:55.605] Imported node: DT_Flatten_Colors
[2025-06-26 19:42:55.605] Imported node: DT_FontText
[2025-06-26 19:42:55.605] Imported node: DT_GenerateNoise
[2025-06-26 19:42:55.605] Imported node: DT_Glitch_This
[2025-06-26 19:42:55.605] Imported node: DT_Hue_Rotation
[2025-06-26 19:42:55.605] Imported node: DT_Load_Picture_Index
[2025-06-26 19:42:55.605] Imported node: DT_PILGram
[2025-06-26 19:42:55.605] Imported node: DT_Pixel_Sort
[2025-06-26 19:42:55.605] Imported node: DT_Play_Sound_At_Execution
[2025-06-26 19:42:55.605] Imported node: DT_PromptGen
[2025-06-26 19:42:55.605] Imported node: DT_Solid_Color
[2025-06-26 19:42:55.605] Imported node: DT_Swap_Color_Mode
[2025-06-26 19:42:55.613] (pysssss:WD14Tagger) [DEBUG] Available ORT providers: AzureExecutionProvider, CPUExecutionProvider
[2025-06-26 19:42:55.613] (pysssss:WD14Tagger) [DEBUG] Using ORT providers: CUDAExecutionProvider, CPUExecutionProvider
[2025-06-26 19:42:55.622] SupervisionWarnings: BoundingBoxAnnotator is deprecated: `BoundingBoxAnnotator` is deprecated and has been renamed to `BoxAnnotator`. `BoundingBoxAnnotator` will be removed in supervision-0.26.0.
[2025-06-26 19:42:55.679] ------------------------------------------
[2025-06-26 19:42:55.679] [34mComfyroll Studio v1.76 : [92m 175 Nodes Loaded[0m
[2025-06-26 19:42:55.679] ------------------------------------------
[2025-06-26 19:42:55.679] ** For changes, please see patch notes at https://github.com/Suzie1/ComfyUI_Comfyroll_CustomNodes/blob/main/Patch_Notes.md
[2025-06-26 19:42:55.679] ** For help, please see the wiki at https://github.com/Suzie1/ComfyUI_Comfyroll_CustomNodes/wiki
[2025-06-26 19:42:55.679] ------------------------------------------
[2025-06-26 19:42:55.692] [36;20m[D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using ckpts path: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux\ckpts[0m
[2025-06-26 19:42:55.692] [36;20m[D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using symlinks: False[0m
[2025-06-26 19:42:55.694] [36;20m[D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using ort providers: ['CUDAExecutionProvider', 'DirectMLExecutionProvider', 'OpenVINOExecutionProvider', 'ROCMExecutionProvider', 'CPUExecutionProvider', 'CoreMLExecutionProvider'][0m
[2025-06-26 19:42:55.780] ------------------------------------
[2025-06-26 19:42:55.780] Loading nodes from: MY_NODES
[2025-06-26 19:42:55.782]   -> Loaded nodes from simple_slider.py
[2025-06-26 19:42:55.784]   -> Loaded nodes from slider_lora_loader.py
[2025-06-26 19:42:55.784] ------------------------------------
[2025-06-26 19:42:55.885] 
[2025-06-26 19:42:55.885] [92m[rgthree-comfy] Loaded 47 extraordinary nodes. 🎉[0m
[2025-06-26 19:42:55.885] 
[2025-06-26 19:42:57.101] [34mWAS Node Suite: [0mOpenCV Python FFMPEG support is enabled[0m
[2025-06-26 19:42:57.101] [34mWAS Node Suite [93mWarning: [0m`ffmpeg_bin_path` is not set in `D:\stable_diffusion\ComfyUI\custom_nodes\was-ns\was_suite_config.json` config file. Will attempt to use system ffmpeg binaries if available.[0m
[2025-06-26 19:42:57.874] [34mWAS Node Suite: [0mFinished.[0m [32mLoaded[0m [0m220[0m [32mnodes successfully.[0m
[2025-06-26 19:42:57.874] 
	[3m[93m"The best way to predict the future is to create it."[0m[3m - Peter Drucker[0m
[2025-06-26 19:42:57.874] 
[2025-06-26 19:42:57.896] ### Loading: ComfyUI-Manager (V3.30.4)
[2025-06-26 19:42:57.897] [ComfyUI-Manager] network_mode: public
[2025-06-26 19:42:57.898] ### ComfyUI Revision: UNKNOWN (The currently installed ComfyUI is not a Git repository)
[2025-06-26 19:42:57.945] 
Import times for custom nodes:
[2025-06-26 19:42:57.945]    0.0 seconds: C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI\custom_nodes\websocket_image_save.py
[2025-06-26 19:42:57.945]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyUI-ImageCompare
[2025-06-26 19:42:57.945]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-ultralytics-yolo
[2025-06-26 19:42:57.946]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\sdxl-recommended-res-calc
[2025-06-26 19:42:57.946]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyUI_Cutoff
[2025-06-26 19:42:57.946]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\masquerade-nodes-comfyui
[2025-06-26 19:42:57.946]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyUI-Contextual-SAM2
[2025-06-26 19:42:57.946]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\quick-connections
[2025-06-26 19:42:57.947]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-mxtoolkit
[2025-06-26 19:42:57.947]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\civitai_comfy_nodes
[2025-06-26 19:42:57.947]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\lora-info
[2025-06-26 19:42:57.947]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-lora-auto-trigger-words
[2025-06-26 19:42:57.947]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\MY_NODES
[2025-06-26 19:42:57.947]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-inpaint-cropandstitch
[2025-06-26 19:42:57.947]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-autocomplete-plus
[2025-06-26 19:42:57.948]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-wd14-tagger
[2025-06-26 19:42:57.948]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-inpaint-nodes
[2025-06-26 19:42:57.948]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-impact-subpack
[2025-06-26 19:42:57.948]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-model-downloader
[2025-06-26 19:42:57.948]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyUI-YoloWorld-EfficientSAM
[2025-06-26 19:42:57.948]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-custom-scripts
[2025-06-26 19:42:57.949]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_essentials
[2025-06-26 19:42:57.949]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_ultimatesdupscale
[2025-06-26 19:42:57.949]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyMath
[2025-06-26 19:42:57.949]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyUI-segment-anything-2
[2025-06-26 19:42:57.949]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-kjnodes
[2025-06-26 19:42:57.949]    0.1 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux
[2025-06-26 19:42:57.949]    0.1 seconds: C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI\custom_nodes\ComfyUI-Manager
[2025-06-26 19:42:57.949]    0.1 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyUI_Comfyroll_CustomNodes
[2025-06-26 19:42:57.949]    0.1 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-impact-pack
[2025-06-26 19:42:57.949]    0.1 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\rgthree-comfy
[2025-06-26 19:42:57.949]    0.2 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-lora-manager
[2025-06-26 19:42:57.949]    0.4 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-sam2
[2025-06-26 19:42:57.949]    0.7 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyUI-Vextra-Nodes
[2025-06-26 19:42:57.951]    0.8 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-art-venture
[2025-06-26 19:42:57.951]    1.1 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyUI-GroundedSAM2
[2025-06-26 19:42:57.951]    1.2 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-easy-use
[2025-06-26 19:42:57.951]    2.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\was-ns
[2025-06-26 19:42:57.951] 
[2025-06-26 19:42:58.023] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/alter-list.json
[2025-06-26 19:42:58.037] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/model-list.json
[2025-06-26 19:42:58.104] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/github-stats.json
[2025-06-26 19:42:58.175] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/extension-node-map.json
[2025-06-26 19:42:58.225] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json
[2025-06-26 19:42:58.423] Failed to initialize database. Please ensure you have installed the latest requirements. If the error persists, please report this as in future the database will be required: (sqlite3.OperationalError) unable to open database file
(Background on this error at: https://sqlalche.me/e/20/e3q8)
[2025-06-26 19:42:58.469] Metadata collection hooks installed for runtime values
[2025-06-26 19:42:58.469] ComfyUI Metadata Collector initialized
[2025-06-26 19:42:58.469] LoRA Manager: All services initialized and background tasks scheduled
[2025-06-26 19:42:58.469] Starting server

[2025-06-26 19:42:58.470] Cache files disabled for lora, skipping load
[2025-06-26 19:42:58.470] Cache files disabled for checkpoint, skipping load
[2025-06-26 19:42:58.473] To see the GUI go to: http://127.0.0.1:8000
[2025-06-26 19:42:58.476] Recipe cache initialized in 0.00 seconds. Found 0 recipes
[2025-06-26 19:42:58.493] Checkpoint cache initialized in 0.02 seconds. Found 12 models
[2025-06-26 19:42:58.545] Lora cache initialized in 0.05 seconds. Found 60 models
[2025-06-26 19:43:01.227] FETCH ComfyRegistry Data: 5/90
[2025-06-26 19:43:02.927] [Autocomplete-Plus] CSV file status:
  * Danbooru -> base: True, extra: 
  * E621     -> base: True, extra: 
[2025-06-26 19:43:05.655] FETCH ComfyRegistry Data: 10/90
[2025-06-26 19:43:09.228] FETCH ComfyRegistry Data: 15/90
[2025-06-26 19:43:12.815] FETCH ComfyRegistry Data: 20/90
[2025-06-26 19:43:16.257] FETCH ComfyRegistry Data: 25/90
[2025-06-26 19:43:19.712] FETCH ComfyRegistry Data: 30/90
[2025-06-26 19:43:23.127] FETCH ComfyRegistry Data: 35/90
[2025-06-26 19:43:26.571] FETCH ComfyRegistry Data: 40/90
[2025-06-26 19:43:30.167] FETCH ComfyRegistry Data: 45/90
[2025-06-26 19:43:33.564] FETCH ComfyRegistry Data: 50/90
[2025-06-26 19:43:37.220] FETCH ComfyRegistry Data: 55/90
[2025-06-26 19:43:41.571] FETCH ComfyRegistry Data: 60/90
[2025-06-26 19:43:45.213] FETCH ComfyRegistry Data: 65/90
[2025-06-26 19:43:48.905] FETCH ComfyRegistry Data: 70/90
[2025-06-26 19:43:52.485] FETCH ComfyRegistry Data: 75/90
[2025-06-26 19:44:00.139] FETCH ComfyRegistry Data: 80/90
[2025-06-26 19:44:03.757] FETCH ComfyRegistry Data: 85/90
[2025-06-26 19:44:07.185] FETCH ComfyRegistry Data: 90/90
[2025-06-26 19:44:07.686] FETCH ComfyRegistry Data [DONE]
[2025-06-26 19:44:07.828] [ComfyUI-Manager] default cache updated: https://api.comfy.org/nodes
[2025-06-26 19:44:07.867] nightly_channel: 
[2025-06-26 19:44:07.867] https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/remote
[2025-06-26 19:44:07.867] FETCH DATA from: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json [DONE]
[2025-06-26 19:44:08.034] [ComfyUI-Manager] All startup tasks have been completed.
[2025-06-26 19:44:56.422] [Autocomplete-Plus] CSV file status:
  * Danbooru -> base: True, extra: 
  * E621     -> base: True, extra: 
