## ComfyUI-Manager: installing dependencies done.
[2025-06-26 14:50:16.341] ** ComfyUI startup time: 2025-06-26 14:50:16.341
[2025-06-26 14:50:16.341] ** Platform: Windows
[2025-06-26 14:50:16.341] ** Python version: 3.12.9 (main, Feb 12 2025, 14:52:31) [MSC v.1942 64 bit (AMD64)]
[2025-06-26 14:50:16.341] ** Python executable: D:\stable_diffusion\ComfyUI\.venv\Scripts\python.exe
[2025-06-26 14:50:16.341] ** ComfyUI Path: C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI
[2025-06-26 14:50:16.341] ** ComfyUI Base Folder Path: C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI
[2025-06-26 14:50:16.341] ** User directory: D:\stable_diffusion\ComfyUI\user
[2025-06-26 14:50:16.341] ** ComfyUI-Manager config path: D:\stable_diffusion\ComfyUI\user\default\ComfyUI-Manager\config.ini
[2025-06-26 14:50:16.341] ** Log path: D:\stable_diffusion\ComfyUI\user\comfyui.log
[ComfyUI-Manager] Failed to restore comfyui-frontend-package
[2025-06-26 14:50:18.546] expected str, bytes or os.PathLike object, not NoneType
[2025-06-26 14:50:18.547] 
Prestartup times for custom nodes:
[2025-06-26 14:50:18.547]    8.3 seconds: C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI\custom_nodes\ComfyUI-Manager
[2025-06-26 14:50:18.547] 
[2025-06-26 14:50:24.602] Checkpoint files will always be loaded safely.
[2025-06-26 14:50:26.070] Total VRAM 6144 MB, total RAM 28524 MB
[2025-06-26 14:50:26.070] pytorch version: 2.7.0+cu128
[2025-06-26 14:50:26.071] Set vram state to: NORMAL_VRAM
[2025-06-26 14:50:26.072] Device: cuda:0 NVIDIA GeForce RTX 3060 Laptop GPU : cudaMallocAsync
[2025-06-26 14:50:30.084] Using pytorch attention
[2025-06-26 14:50:44.646] Python version: 3.12.9 (main, Feb 12 2025, 14:52:31) [MSC v.1942 64 bit (AMD64)]
[2025-06-26 14:50:44.646] ComfyUI version: 0.3.41
[2025-06-26 14:50:44.798] [Prompt Server] web root: C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI\web_custom_versions\desktop_app
[2025-06-26 14:50:46.760] Adding D:\stable_diffusion\ComfyUI\custom_nodes to sys.path
[2025-06-26 14:50:46.760] Adding C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI\custom_nodes to sys.path
[2025-06-26 14:50:46.907] D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-art-venture\modules\utils.py:9: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  import pkg_resources
[2025-06-26 14:50:47.173] Could not find efficiency nodes
[2025-06-26 14:50:47.394] [36;20m[comfyui_controlnet_aux] | INFO -> Using ckpts path: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux\ckpts[0m
[2025-06-26 14:50:47.394] [36;20m[comfyui_controlnet_aux] | INFO -> Using symlinks: False[0m
[2025-06-26 14:50:47.410] [36;20m[comfyui_controlnet_aux] | INFO -> Using ort providers: ['CUDAExecutionProvider', 'DirectMLExecutionProvider', 'OpenVINOExecutionProvider', 'ROCMExecutionProvider', 'CPUExecutionProvider', 'CoreMLExecutionProvider'][0m
[2025-06-26 14:50:48.090] D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux\node_wrappers\dwpose.py:26: UserWarning: DWPose: Onnxruntime not found or doesn't come with acceleration providers, switch to OpenCV with CPU device. DWPose might run very slowly
  warnings.warn("DWPose: Onnxruntime not found or doesn't come with acceleration providers, switch to OpenCV with CPU device. DWPose might run very slowly")
[2025-06-26 14:50:48.118] Loaded ControlNetPreprocessors nodes from D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux
[2025-06-26 14:50:48.120] Could not find AdvancedControlNet nodes
[2025-06-26 14:50:48.122] Could not find AnimateDiff nodes
[2025-06-26 14:50:48.123] Could not find IPAdapter nodes
[2025-06-26 14:50:48.129] Could not find VideoHelperSuite nodes
[2025-06-26 14:50:48.130] Could not load ImpactPack nodes Could not find ImpactPack nodes
[2025-06-26 14:50:50.739] [34m[ComfyUI-Easy-Use] server: [0mv1.3.0 [92mLoaded[0m
[2025-06-26 14:50:50.739] [34m[ComfyUI-Easy-Use] web root: [0mD:\stable_diffusion\ComfyUI\custom_nodes\comfyui-easy-use\web_version/v2 [92mLoaded[0m
[2025-06-26 14:50:51.398] [06/26/25 14:50:51] WARNING  Your inference package version      __init__.py:41
[2025-06-26 14:50:51.398]                              0.50.4 is out of date! Please                     
[2025-06-26 14:50:51.398]                              upgrade to version 0.51.0 of                      
[2025-06-26 14:50:51.398]                              inference for the latest features                 
[2025-06-26 14:50:51.398]                              and bug fixes by running `pip                     
[2025-06-26 14:50:51.398]                              install --upgrade inference`.                     
[2025-06-26 14:50:51.994] [34mComfyUI-ImageCompare Nodes: [92mLoaded[0m
[2025-06-26 14:50:52.007] ### Loading: ComfyUI-Impact-Pack (V8.15.3)
[2025-06-26 14:50:52.158] ### Loading: ComfyUI-Impact-Subpack (V1.3.2)
[2025-06-26 14:50:52.163] [Impact Pack] Wildcards loading done.
[2025-06-26 14:50:52.163] [Impact Pack/Subpack] Using folder_paths to determine whitelist path: D:\stable_diffusion\ComfyUI\user\default\ComfyUI-Impact-Subpack\model-whitelist.txt
[2025-06-26 14:50:52.163] [Impact Pack/Subpack] Ensured whitelist directory exists: D:\stable_diffusion\ComfyUI\user\default\ComfyUI-Impact-Subpack
[2025-06-26 14:50:52.172] [Impact Pack/Subpack] Loaded 0 model(s) from whitelist: D:\stable_diffusion\ComfyUI\user\default\ComfyUI-Impact-Subpack\model-whitelist.txt
[2025-06-26 14:50:52.175] [Impact Subpack] ultralytics_bbox: D:\stable_diffusion\ComfyUI\models\ultralytics\bbox
[2025-06-26 14:50:52.175] [Impact Subpack] ultralytics_segm: D:\stable_diffusion\ComfyUI\models\ultralytics\segm
[2025-06-26 14:50:52.175] [Impact Subpack] ultralytics_bbox: D:\stable_diffusion\ComfyUI\models\ultralytics\bbox
[2025-06-26 14:50:52.175] [Impact Subpack] ultralytics_segm: D:\stable_diffusion\ComfyUI\models\ultralytics\segm
[2025-06-26 14:50:52.257] Found LoRA roots:
 - D:/stable_diffusion/1_Models/SDXL/loras
 - D:/stable_diffusion/ComfyUI/models/loras
[2025-06-26 14:50:52.257] Found checkpoint roots:
 - D:/stable_diffusion/1_Models/SDXL/checkpoints
 - D:/stable_diffusion/ComfyUI/models/checkpoints
 - D:/stable_diffusion/ComfyUI/models/diffusers
 - D:/stable_diffusion/ComfyUI/models/diffusion_models
 - D:/stable_diffusion/ComfyUI/models/unet
[2025-06-26 14:50:52.290] Saved folder paths to settings.json
[2025-06-26 14:50:52.501] Metadata collection hooks installed for runtime values
[2025-06-26 14:50:52.501] ComfyUI Metadata Collector initialized
[2025-06-26 14:50:52.501] Example images path: None
[2025-06-26 14:50:52.501] Added static route /loras_static/root1/preview -> D:/stable_diffusion/1_Models/SDXL/loras
[2025-06-26 14:50:52.501] Added static route /loras_static/root2/preview -> D:/stable_diffusion/ComfyUI/models/loras
[2025-06-26 14:50:52.501] Added static route /checkpoints_static/root1/preview -> D:/stable_diffusion/1_Models/SDXL/checkpoints
[2025-06-26 14:50:52.501] Added static route /checkpoints_static/root2/preview -> D:/stable_diffusion/ComfyUI/models/checkpoints
[2025-06-26 14:50:52.501] Added static route /checkpoints_static/root3/preview -> D:/stable_diffusion/ComfyUI/models/diffusers
[2025-06-26 14:50:52.501] Added static route /checkpoints_static/root4/preview -> D:/stable_diffusion/ComfyUI/models/diffusion_models
[2025-06-26 14:50:52.504] Added static route /checkpoints_static/root5/preview -> D:/stable_diffusion/ComfyUI/models/unet
[2025-06-26 14:50:53.172] FutureWarning: Importing from timm.models.layers is deprecated, please import via timm.layers
[2025-06-26 14:50:53.226] Imported node: DT_Flatten_Colors
[2025-06-26 14:50:53.226] Imported node: DT_FontText
[2025-06-26 14:50:53.226] Imported node: DT_GenerateNoise
[2025-06-26 14:50:53.230] Imported node: DT_Glitch_This
[2025-06-26 14:50:53.231] Imported node: DT_Hue_Rotation
[2025-06-26 14:50:53.231] Imported node: DT_Load_Picture_Index
[2025-06-26 14:50:53.300] Imported node: DT_PILGram
[2025-06-26 14:50:53.306] Imported node: DT_Pixel_Sort
[2025-06-26 14:50:54.382] Imported node: DT_Play_Sound_At_Execution
[2025-06-26 14:50:54.763] Imported node: DT_PromptGen
[2025-06-26 14:50:54.764] Imported node: DT_Solid_Color
[2025-06-26 14:50:54.765] Imported node: DT_Swap_Color_Mode
[2025-06-26 14:50:54.765] Imported node: DT_Flatten_Colors
[2025-06-26 14:50:54.765] Imported node: DT_FontText
[2025-06-26 14:50:54.765] Imported node: DT_GenerateNoise
[2025-06-26 14:50:54.765] Imported node: DT_Glitch_This
[2025-06-26 14:50:54.765] Imported node: DT_Hue_Rotation
[2025-06-26 14:50:54.765] Imported node: DT_Load_Picture_Index
[2025-06-26 14:50:54.765] Imported node: DT_PILGram
[2025-06-26 14:50:54.765] Imported node: DT_Pixel_Sort
[2025-06-26 14:50:54.765] Imported node: DT_Play_Sound_At_Execution
[2025-06-26 14:50:54.765] Imported node: DT_PromptGen
[2025-06-26 14:50:54.765] Imported node: DT_Solid_Color
[2025-06-26 14:50:54.765] Imported node: DT_Swap_Color_Mode
[2025-06-26 14:50:54.786] (pysssss:WD14Tagger) [DEBUG] Available ORT providers: AzureExecutionProvider, CPUExecutionProvider
[2025-06-26 14:50:54.786] (pysssss:WD14Tagger) [DEBUG] Using ORT providers: CUDAExecutionProvider, CPUExecutionProvider
[2025-06-26 14:50:54.824] SupervisionWarnings: BoundingBoxAnnotator is deprecated: `BoundingBoxAnnotator` is deprecated and has been renamed to `BoxAnnotator`. `BoundingBoxAnnotator` will be removed in supervision-0.26.0.
[2025-06-26 14:50:54.859] ------------------------------------------
[2025-06-26 14:50:54.859] [34mComfyroll Studio v1.76 : [92m 175 Nodes Loaded[0m
[2025-06-26 14:50:54.859] ------------------------------------------
[2025-06-26 14:50:54.859] ** For changes, please see patch notes at https://github.com/Suzie1/ComfyUI_Comfyroll_CustomNodes/blob/main/Patch_Notes.md
[2025-06-26 14:50:54.859] ** For help, please see the wiki at https://github.com/Suzie1/ComfyUI_Comfyroll_CustomNodes/wiki
[2025-06-26 14:50:54.859] ------------------------------------------
[2025-06-26 14:50:54.869] [36;20m[D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using ckpts path: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux\ckpts[0m
[2025-06-26 14:50:54.870] [36;20m[D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using symlinks: False[0m
[2025-06-26 14:50:54.871] [36;20m[D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using ort providers: ['CUDAExecutionProvider', 'DirectMLExecutionProvider', 'OpenVINOExecutionProvider', 'ROCMExecutionProvider', 'CPUExecutionProvider', 'CoreMLExecutionProvider'][0m
[2025-06-26 14:50:54.971] ------------------------------------
[2025-06-26 14:50:54.971] Loading nodes from: MY_NODES
[2025-06-26 14:50:54.974]   -> Loaded nodes from simple_slider.py
[2025-06-26 14:50:54.974] ------------------------------------
[2025-06-26 14:50:55.208] 
[2025-06-26 14:50:55.208] [92m[rgthree-comfy] Loaded 47 epic nodes. 🎉[0m
[2025-06-26 14:50:55.208] 
[2025-06-26 14:50:56.604] [34mWAS Node Suite: [0mOpenCV Python FFMPEG support is enabled[0m
[2025-06-26 14:50:56.604] [34mWAS Node Suite [93mWarning: [0m`ffmpeg_bin_path` is not set in `D:\stable_diffusion\ComfyUI\custom_nodes\was-ns\was_suite_config.json` config file. Will attempt to use system ffmpeg binaries if available.[0m
[2025-06-26 14:50:57.301] [34mWAS Node Suite: [0mFinished.[0m [32mLoaded[0m [0m220[0m [32mnodes successfully.[0m
[2025-06-26 14:50:57.301] 
	[3m[93m"Art enables us to find ourselves and lose ourselves at the same time."[0m[3m - Thomas Merton[0m
[2025-06-26 14:50:57.301] 
[2025-06-26 14:50:57.335] ### Loading: ComfyUI-Manager (V3.30.4)
[2025-06-26 14:50:57.335] [ComfyUI-Manager] network_mode: public
[2025-06-26 14:50:57.336] ### ComfyUI Revision: UNKNOWN (The currently installed ComfyUI is not a Git repository)
[2025-06-26 14:50:57.722] 
Import times for custom nodes:
[2025-06-26 14:50:57.722]    0.0 seconds: C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI\custom_nodes\websocket_image_save.py
[2025-06-26 14:50:57.722]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyUI-ImageCompare
[2025-06-26 14:50:57.722]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyUI_Cutoff
[2025-06-26 14:50:57.723]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\MY_NODES
[2025-06-26 14:50:57.723]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-ultralytics-yolo
[2025-06-26 14:50:57.723]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\masquerade-nodes-comfyui
[2025-06-26 14:50:57.723]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\civitai_comfy_nodes
[2025-06-26 14:50:57.723]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\quick-connections
[2025-06-26 14:50:57.723]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\sdxl-recommended-res-calc
[2025-06-26 14:50:57.723]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-model-downloader
[2025-06-26 14:50:57.723]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyUI-Contextual-SAM2
[2025-06-26 14:50:57.723]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-mxtoolkit
[2025-06-26 14:50:57.723]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\lora-info
[2025-06-26 14:50:57.723]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-lora-auto-trigger-words
[2025-06-26 14:50:57.723]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-inpaint-cropandstitch
[2025-06-26 14:50:57.723]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-inpaint-nodes
[2025-06-26 14:50:57.723]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-autocomplete-plus
[2025-06-26 14:50:57.723]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_ultimatesdupscale
[2025-06-26 14:50:57.723]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-wd14-tagger
[2025-06-26 14:50:57.723]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_essentials
[2025-06-26 14:50:57.724]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyMath
[2025-06-26 14:50:57.724]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-custom-scripts
[2025-06-26 14:50:57.724]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyUI-segment-anything-2
[2025-06-26 14:50:57.724]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-impact-subpack
[2025-06-26 14:50:57.724]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-kjnodes
[2025-06-26 14:50:57.724]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyUI-YoloWorld-EfficientSAM
[2025-06-26 14:50:57.724]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyUI_Comfyroll_CustomNodes
[2025-06-26 14:50:57.724]    0.1 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux
[2025-06-26 14:50:57.724]    0.2 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-impact-pack
[2025-06-26 14:50:57.724]    0.2 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\rgthree-comfy
[2025-06-26 14:50:57.724]    0.3 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-lora-manager
[2025-06-26 14:50:57.724]    0.4 seconds: C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI\custom_nodes\ComfyUI-Manager
[2025-06-26 14:50:57.724]    0.7 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-sam2
[2025-06-26 14:50:57.724]    1.2 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyUI-GroundedSAM2
[2025-06-26 14:50:57.724]    1.4 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-art-venture
[2025-06-26 14:50:57.724]    1.6 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyUI-Vextra-Nodes
[2025-06-26 14:50:57.724]    2.1 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\was-ns
[2025-06-26 14:50:57.724]    2.6 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-easy-use
[2025-06-26 14:50:57.724] 
[2025-06-26 14:50:57.783] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/model-list.json
[2025-06-26 14:50:57.787] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/alter-list.json
[2025-06-26 14:50:57.814] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/github-stats.json
[2025-06-26 14:50:57.867] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json
[2025-06-26 14:50:57.911] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/extension-node-map.json
[2025-06-26 14:50:58.509] Failed to initialize database. Please ensure you have installed the latest requirements. If the error persists, please report this as in future the database will be required: (sqlite3.OperationalError) unable to open database file
(Background on this error at: https://sqlalche.me/e/20/e3q8)
[2025-06-26 14:50:58.549] Metadata collection hooks installed for runtime values
[2025-06-26 14:50:58.549] ComfyUI Metadata Collector initialized
[2025-06-26 14:50:58.549] LoRA Manager: All services initialized and background tasks scheduled
[2025-06-26 14:50:58.549] Starting server

[2025-06-26 14:50:58.549] Cache files disabled for lora, skipping load
[2025-06-26 14:50:58.551] Cache files disabled for checkpoint, skipping load
[2025-06-26 14:50:58.553] To see the GUI go to: http://127.0.0.1:8000
[2025-06-26 14:50:58.555] Recipe cache initialized in 0.00 seconds. Found 0 recipes
[2025-06-26 14:50:58.669] Checkpoint cache initialized in 0.11 seconds. Found 12 models
[2025-06-26 14:50:59.128] Lora cache initialized in 0.56 seconds. Found 60 models
[2025-06-26 14:51:01.097] FETCH ComfyRegistry Data: 5/90
[2025-06-26 14:51:03.895] Error handling request from 127.0.0.1
[2025-06-26 14:51:04.617] FETCH ComfyRegistry Data: 10/90
[2025-06-26 14:51:08.103] FETCH ComfyRegistry Data: 15/90
[2025-06-26 14:51:11.513] FETCH ComfyRegistry Data: 20/90
[2025-06-26 14:51:15.142] FETCH ComfyRegistry Data: 25/90
[2025-06-26 14:51:18.601] FETCH ComfyRegistry Data: 30/90
[2025-06-26 14:51:22.961] FETCH ComfyRegistry Data: 35/90
[2025-06-26 14:51:26.511] FETCH ComfyRegistry Data: 40/90
[2025-06-26 14:51:30.125] FETCH ComfyRegistry Data: 45/90
[2025-06-26 14:51:33.486] FETCH ComfyRegistry Data: 50/90
[2025-06-26 14:51:36.857] FETCH ComfyRegistry Data: 55/90
[2025-06-26 14:51:40.316] FETCH ComfyRegistry Data: 60/90
[2025-06-26 14:51:43.817] FETCH ComfyRegistry Data: 65/90
[2025-06-26 14:51:47.268] FETCH ComfyRegistry Data: 70/90
[2025-06-26 14:51:50.839] FETCH ComfyRegistry Data: 75/90
[2025-06-26 14:51:54.283] FETCH ComfyRegistry Data: 80/90
[2025-06-26 14:51:57.714] FETCH ComfyRegistry Data: 85/90
[2025-06-26 14:52:01.190] FETCH ComfyRegistry Data: 90/90
[2025-06-26 14:52:01.691] FETCH ComfyRegistry Data [DONE]
[2025-06-26 14:52:01.831] [ComfyUI-Manager] default cache updated: https://api.comfy.org/nodes
[2025-06-26 14:52:01.873] nightly_channel: 
[2025-06-26 14:52:01.873] https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/remote
[2025-06-26 14:52:01.882] FETCH DATA from: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json [DONE]
[2025-06-26 14:52:02.023] [ComfyUI-Manager] All startup tasks have been completed.
