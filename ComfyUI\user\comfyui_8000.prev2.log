## ComfyUI-Manager: installing dependencies done.
[2025-06-26 17:05:16.155] ** ComfyUI startup time: 2025-06-26 17:05:16.155
[2025-06-26 17:05:16.155] ** Platform: Windows
[2025-06-26 17:05:16.155] ** Python version: 3.12.9 (main, Feb 12 2025, 14:52:31) [MSC v.1942 64 bit (AMD64)]
[2025-06-26 17:05:16.155] ** Python executable: D:\stable_diffusion\ComfyUI\.venv\Scripts\python.exe
[2025-06-26 17:05:16.155] ** ComfyUI Path: C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI
[2025-06-26 17:05:16.155] ** ComfyUI Base Folder Path: C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI
[2025-06-26 17:05:16.156] ** User directory: D:\stable_diffusion\ComfyUI\user
[2025-06-26 17:05:16.167] ** ComfyUI-Manager config path: D:\stable_diffusion\ComfyUI\user\default\ComfyUI-Manager\config.ini
[2025-06-26 17:05:16.167] ** Log path: D:\stable_diffusion\ComfyUI\user\comfyui.log
[ComfyUI-Manager] Failed to restore comfyui-frontend-package
[2025-06-26 17:05:17.931] expected str, bytes or os.PathLike object, not NoneType
[2025-06-26 17:05:17.933] 
Prestartup times for custom nodes:
[2025-06-26 17:05:17.933]    4.6 seconds: C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI\custom_nodes\ComfyUI-Manager
[2025-06-26 17:05:17.933] 
[2025-06-26 17:05:20.024] Checkpoint files will always be loaded safely.
[2025-06-26 17:05:21.322] Total VRAM 6144 MB, total RAM 28524 MB
[2025-06-26 17:05:21.322] pytorch version: 2.7.0+cu128
[2025-06-26 17:05:21.323] Set vram state to: NORMAL_VRAM
[2025-06-26 17:05:21.324] Device: cuda:0 NVIDIA GeForce RTX 3060 Laptop GPU : cudaMallocAsync
[2025-06-26 17:05:23.218] Using pytorch attention
[2025-06-26 17:05:25.001] Python version: 3.12.9 (main, Feb 12 2025, 14:52:31) [MSC v.1942 64 bit (AMD64)]
[2025-06-26 17:05:25.001] ComfyUI version: 0.3.41
[2025-06-26 17:05:25.173] [Prompt Server] web root: C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI\web_custom_versions\desktop_app
[2025-06-26 17:05:26.360] Adding D:\stable_diffusion\ComfyUI\custom_nodes to sys.path
[2025-06-26 17:05:26.360] Adding C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI\custom_nodes to sys.path
[2025-06-26 17:05:26.404] D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-art-venture\modules\utils.py:9: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  import pkg_resources
[2025-06-26 17:05:26.666] Could not find efficiency nodes
[2025-06-26 17:05:26.716] [36;20m[comfyui_controlnet_aux] | INFO -> Using ckpts path: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux\ckpts[0m
[2025-06-26 17:05:26.717] [36;20m[comfyui_controlnet_aux] | INFO -> Using symlinks: False[0m
[2025-06-26 17:05:26.718] [36;20m[comfyui_controlnet_aux] | INFO -> Using ort providers: ['CUDAExecutionProvider', 'DirectMLExecutionProvider', 'OpenVINOExecutionProvider', 'ROCMExecutionProvider', 'CPUExecutionProvider', 'CoreMLExecutionProvider'][0m
[2025-06-26 17:05:27.046] D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux\node_wrappers\dwpose.py:26: UserWarning: DWPose: Onnxruntime not found or doesn't come with acceleration providers, switch to OpenCV with CPU device. DWPose might run very slowly
  warnings.warn("DWPose: Onnxruntime not found or doesn't come with acceleration providers, switch to OpenCV with CPU device. DWPose might run very slowly")
[2025-06-26 17:05:27.075] Loaded ControlNetPreprocessors nodes from D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux
[2025-06-26 17:05:27.076] Could not find AdvancedControlNet nodes
[2025-06-26 17:05:27.079] Could not find AnimateDiff nodes
[2025-06-26 17:05:27.080] Could not find IPAdapter nodes
[2025-06-26 17:05:27.088] Could not find VideoHelperSuite nodes
[2025-06-26 17:05:27.090] Could not load ImpactPack nodes Could not find ImpactPack nodes
[2025-06-26 17:05:28.082] [34m[ComfyUI-Easy-Use] server: [0mv1.3.0 [92mLoaded[0m
[2025-06-26 17:05:28.082] [34m[ComfyUI-Easy-Use] web root: [0mD:\stable_diffusion\ComfyUI\custom_nodes\comfyui-easy-use\web_version/v2 [92mLoaded[0m
[2025-06-26 17:05:28.658] [06/26/25 17:05:28] WARNING  Your inference package version      __init__.py:41
[2025-06-26 17:05:28.658]                              0.50.4 is out of date! Please                     
[2025-06-26 17:05:28.658]                              upgrade to version 0.51.0 of                      
[2025-06-26 17:05:28.658]                              inference for the latest features                 
[2025-06-26 17:05:28.658]                              and bug fixes by running `pip                     
[2025-06-26 17:05:28.658]                              install --upgrade inference`.                     
[2025-06-26 17:05:29.062] [34mComfyUI-ImageCompare Nodes: [92mLoaded[0m
[2025-06-26 17:05:29.065] ### Loading: ComfyUI-Impact-Pack (V8.15.3)
[2025-06-26 17:05:29.124] [Impact Pack] Wildcards loading done.
[2025-06-26 17:05:29.132] ### Loading: ComfyUI-Impact-Subpack (V1.3.2)
[2025-06-26 17:05:29.136] [Impact Pack/Subpack] Using folder_paths to determine whitelist path: D:\stable_diffusion\ComfyUI\user\default\ComfyUI-Impact-Subpack\model-whitelist.txt
[2025-06-26 17:05:29.137] [Impact Pack/Subpack] Ensured whitelist directory exists: D:\stable_diffusion\ComfyUI\user\default\ComfyUI-Impact-Subpack
[2025-06-26 17:05:29.137] [Impact Pack/Subpack] Loaded 0 model(s) from whitelist: D:\stable_diffusion\ComfyUI\user\default\ComfyUI-Impact-Subpack\model-whitelist.txt
[2025-06-26 17:05:29.137] [Impact Subpack] ultralytics_bbox: D:\stable_diffusion\ComfyUI\models\ultralytics\bbox
[2025-06-26 17:05:29.137] [Impact Subpack] ultralytics_segm: D:\stable_diffusion\ComfyUI\models\ultralytics\segm
[2025-06-26 17:05:29.137] [Impact Subpack] ultralytics_bbox: D:\stable_diffusion\ComfyUI\models\ultralytics\bbox
[2025-06-26 17:05:29.137] [Impact Subpack] ultralytics_segm: D:\stable_diffusion\ComfyUI\models\ultralytics\segm
[2025-06-26 17:05:29.189] Found LoRA roots:
 - D:/stable_diffusion/1_Models/SDXL/loras
 - D:/stable_diffusion/ComfyUI/models/loras
[2025-06-26 17:05:29.189] Found checkpoint roots:
 - D:/stable_diffusion/1_Models/SDXL/checkpoints
 - D:/stable_diffusion/ComfyUI/models/checkpoints
 - D:/stable_diffusion/ComfyUI/models/diffusers
 - D:/stable_diffusion/ComfyUI/models/diffusion_models
 - D:/stable_diffusion/ComfyUI/models/unet
[2025-06-26 17:05:29.210] Saved folder paths to settings.json
[2025-06-26 17:05:29.351] Metadata collection hooks installed for runtime values
[2025-06-26 17:05:29.351] ComfyUI Metadata Collector initialized
[2025-06-26 17:05:29.352] Example images path: None
[2025-06-26 17:05:29.354] Added static route /loras_static/root1/preview -> D:/stable_diffusion/1_Models/SDXL/loras
[2025-06-26 17:05:29.354] Added static route /loras_static/root2/preview -> D:/stable_diffusion/ComfyUI/models/loras
[2025-06-26 17:05:29.354] Added static route /checkpoints_static/root1/preview -> D:/stable_diffusion/1_Models/SDXL/checkpoints
[2025-06-26 17:05:29.354] Added static route /checkpoints_static/root2/preview -> D:/stable_diffusion/ComfyUI/models/checkpoints
[2025-06-26 17:05:29.355] Added static route /checkpoints_static/root3/preview -> D:/stable_diffusion/ComfyUI/models/diffusers
[2025-06-26 17:05:29.355] Added static route /checkpoints_static/root4/preview -> D:/stable_diffusion/ComfyUI/models/diffusion_models
[2025-06-26 17:05:29.355] Added static route /checkpoints_static/root5/preview -> D:/stable_diffusion/ComfyUI/models/unet
[2025-06-26 17:05:29.793] FutureWarning: Importing from timm.models.layers is deprecated, please import via timm.layers
[2025-06-26 17:05:29.824] Imported node: DT_Flatten_Colors
[2025-06-26 17:05:29.825] Imported node: DT_FontText
[2025-06-26 17:05:29.825] Imported node: DT_GenerateNoise
[2025-06-26 17:05:29.829] Imported node: DT_Glitch_This
[2025-06-26 17:05:29.829] Imported node: DT_Hue_Rotation
[2025-06-26 17:05:29.830] Imported node: DT_Load_Picture_Index
[2025-06-26 17:05:29.865] Imported node: DT_PILGram
[2025-06-26 17:05:29.869] Imported node: DT_Pixel_Sort
[2025-06-26 17:05:30.236] Imported node: DT_Play_Sound_At_Execution
[2025-06-26 17:05:30.510] Imported node: DT_PromptGen
[2025-06-26 17:05:30.511] Imported node: DT_Solid_Color
[2025-06-26 17:05:30.511] Imported node: DT_Swap_Color_Mode
[2025-06-26 17:05:30.511] Imported node: DT_Flatten_Colors
[2025-06-26 17:05:30.512] Imported node: DT_FontText
[2025-06-26 17:05:30.512] Imported node: DT_GenerateNoise
[2025-06-26 17:05:30.512] Imported node: DT_Glitch_This
[2025-06-26 17:05:30.512] Imported node: DT_Hue_Rotation
[2025-06-26 17:05:30.512] Imported node: DT_Load_Picture_Index
[2025-06-26 17:05:30.512] Imported node: DT_PILGram
[2025-06-26 17:05:30.512] Imported node: DT_Pixel_Sort
[2025-06-26 17:05:30.512] Imported node: DT_Play_Sound_At_Execution
[2025-06-26 17:05:30.512] Imported node: DT_PromptGen
[2025-06-26 17:05:30.512] Imported node: DT_Solid_Color
[2025-06-26 17:05:30.512] Imported node: DT_Swap_Color_Mode
[2025-06-26 17:05:30.518] (pysssss:WD14Tagger) [DEBUG] Available ORT providers: AzureExecutionProvider, CPUExecutionProvider
[2025-06-26 17:05:30.518] (pysssss:WD14Tagger) [DEBUG] Using ORT providers: CUDAExecutionProvider, CPUExecutionProvider
[2025-06-26 17:05:30.527] SupervisionWarnings: BoundingBoxAnnotator is deprecated: `BoundingBoxAnnotator` is deprecated and has been renamed to `BoxAnnotator`. `BoundingBoxAnnotator` will be removed in supervision-0.26.0.
[2025-06-26 17:05:30.574] ------------------------------------------
[2025-06-26 17:05:30.574] [34mComfyroll Studio v1.76 : [92m 175 Nodes Loaded[0m
[2025-06-26 17:05:30.574] ------------------------------------------
[2025-06-26 17:05:30.574] ** For changes, please see patch notes at https://github.com/Suzie1/ComfyUI_Comfyroll_CustomNodes/blob/main/Patch_Notes.md
[2025-06-26 17:05:30.574] ** For help, please see the wiki at https://github.com/Suzie1/ComfyUI_Comfyroll_CustomNodes/wiki
[2025-06-26 17:05:30.574] ------------------------------------------
[2025-06-26 17:05:30.585] [36;20m[D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using ckpts path: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux\ckpts[0m
[2025-06-26 17:05:30.586] [36;20m[D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using symlinks: False[0m
[2025-06-26 17:05:30.587] [36;20m[D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using ort providers: ['CUDAExecutionProvider', 'DirectMLExecutionProvider', 'OpenVINOExecutionProvider', 'ROCMExecutionProvider', 'CPUExecutionProvider', 'CoreMLExecutionProvider'][0m
[2025-06-26 17:05:30.664] ------------------------------------
[2025-06-26 17:05:30.665] Loading nodes from: MY_NODES
[2025-06-26 17:05:30.666]   -> Loaded nodes from simple_slider.py
[2025-06-26 17:05:30.666] ------------------------------------
[2025-06-26 17:05:30.785] 
[2025-06-26 17:05:30.785] [92m[rgthree-comfy] Loaded 47 magnificent nodes. 🎉[0m
[2025-06-26 17:05:30.785] 
[2025-06-26 17:05:31.791] [34mWAS Node Suite: [0mOpenCV Python FFMPEG support is enabled[0m
[2025-06-26 17:05:31.791] [34mWAS Node Suite [93mWarning: [0m`ffmpeg_bin_path` is not set in `D:\stable_diffusion\ComfyUI\custom_nodes\was-ns\was_suite_config.json` config file. Will attempt to use system ffmpeg binaries if available.[0m
[2025-06-26 17:05:32.574] [34mWAS Node Suite: [0mFinished.[0m [32mLoaded[0m [0m220[0m [32mnodes successfully.[0m
[2025-06-26 17:05:32.575] 
	[3m[93m"Every great dream begins with a dreamer."[0m[3m - Harriet Tubman[0m
[2025-06-26 17:05:32.575] 
[2025-06-26 17:05:32.595] ### Loading: ComfyUI-Manager (V3.30.4)
[2025-06-26 17:05:32.596] [ComfyUI-Manager] network_mode: public
[2025-06-26 17:05:32.597] ### ComfyUI Revision: UNKNOWN (The currently installed ComfyUI is not a Git repository)
[2025-06-26 17:05:32.640] 
Import times for custom nodes:
[2025-06-26 17:05:32.640]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyUI-ImageCompare
[2025-06-26 17:05:32.641]    0.0 seconds: C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI\custom_nodes\websocket_image_save.py
[2025-06-26 17:05:32.641]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyUI_Cutoff
[2025-06-26 17:05:32.641]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\MY_NODES
[2025-06-26 17:05:32.641]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\sdxl-recommended-res-calc
[2025-06-26 17:05:32.641]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-ultralytics-yolo
[2025-06-26 17:05:32.641]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\quick-connections
[2025-06-26 17:05:32.641]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\masquerade-nodes-comfyui
[2025-06-26 17:05:32.641]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\lora-info
[2025-06-26 17:05:32.641]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-mxtoolkit
[2025-06-26 17:05:32.641]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyUI-Contextual-SAM2
[2025-06-26 17:05:32.641]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\civitai_comfy_nodes
[2025-06-26 17:05:32.641]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-inpaint-cropandstitch
[2025-06-26 17:05:32.641]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-lora-auto-trigger-words
[2025-06-26 17:05:32.642]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-wd14-tagger
[2025-06-26 17:05:32.642]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-autocomplete-plus
[2025-06-26 17:05:32.642]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-model-downloader
[2025-06-26 17:05:32.642]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyUI-YoloWorld-EfficientSAM
[2025-06-26 17:05:32.642]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-impact-subpack
[2025-06-26 17:05:32.642]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_essentials
[2025-06-26 17:05:32.642]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-custom-scripts
[2025-06-26 17:05:32.643]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_ultimatesdupscale
[2025-06-26 17:05:32.643]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-inpaint-nodes
[2025-06-26 17:05:32.643]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyMath
[2025-06-26 17:05:32.643]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyUI-segment-anything-2
[2025-06-26 17:05:32.643]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-kjnodes
[2025-06-26 17:05:32.643]    0.1 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyUI_Comfyroll_CustomNodes
[2025-06-26 17:05:32.643]    0.1 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux
[2025-06-26 17:05:32.643]    0.1 seconds: C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI\custom_nodes\ComfyUI-Manager
[2025-06-26 17:05:32.643]    0.1 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-impact-pack
[2025-06-26 17:05:32.643]    0.1 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\rgthree-comfy
[2025-06-26 17:05:32.643]    0.2 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-lora-manager
[2025-06-26 17:05:32.643]    0.4 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-sam2
[2025-06-26 17:05:32.643]    0.7 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyUI-Vextra-Nodes
[2025-06-26 17:05:32.643]    0.8 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-art-venture
[2025-06-26 17:05:32.643]    1.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-easy-use
[2025-06-26 17:05:32.643]    1.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyUI-GroundedSAM2
[2025-06-26 17:05:32.643]    1.8 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\was-ns
[2025-06-26 17:05:32.644] 
[2025-06-26 17:05:32.707] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/model-list.json
[2025-06-26 17:05:32.984] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/alter-list.json
[2025-06-26 17:05:33.027] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/github-stats.json
[2025-06-26 17:05:33.092] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/extension-node-map.json
[2025-06-26 17:05:33.146] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json
[2025-06-26 17:05:33.331] Failed to initialize database. Please ensure you have installed the latest requirements. If the error persists, please report this as in future the database will be required: (sqlite3.OperationalError) unable to open database file
(Background on this error at: https://sqlalche.me/e/20/e3q8)
[2025-06-26 17:05:33.374] Metadata collection hooks installed for runtime values
[2025-06-26 17:05:33.374] ComfyUI Metadata Collector initialized
[2025-06-26 17:05:33.374] LoRA Manager: All services initialized and background tasks scheduled
[2025-06-26 17:05:33.374] Starting server

[2025-06-26 17:05:33.374] Cache files disabled for lora, skipping load
[2025-06-26 17:05:33.375] Cache files disabled for checkpoint, skipping load
[2025-06-26 17:05:33.378] To see the GUI go to: http://127.0.0.1:8000
[2025-06-26 17:05:33.379] Recipe cache initialized in 0.00 seconds. Found 0 recipes
[2025-06-26 17:05:33.396] Checkpoint cache initialized in 0.02 seconds. Found 12 models
[2025-06-26 17:05:33.449] Lora cache initialized in 0.05 seconds. Found 60 models
[2025-06-26 17:05:36.201] FETCH ComfyRegistry Data: 5/90
[2025-06-26 17:05:37.633] [Autocomplete-Plus] CSV file status:
  * Danbooru -> base: True, extra: 
  * E621     -> base: True, extra: 
[2025-06-26 17:05:39.742] FETCH ComfyRegistry Data: 10/90
[2025-06-26 17:05:43.250] FETCH ComfyRegistry Data: 15/90
[2025-06-26 17:05:47.256] FETCH ComfyRegistry Data: 20/90
[2025-06-26 17:05:50.811] FETCH ComfyRegistry Data: 25/90
[2025-06-26 17:05:54.258] FETCH ComfyRegistry Data: 30/90
[2025-06-26 17:05:57.712] FETCH ComfyRegistry Data: 35/90
[2025-06-26 17:06:01.138] FETCH ComfyRegistry Data: 40/90
[2025-06-26 17:06:04.759] FETCH ComfyRegistry Data: 45/90
[2025-06-26 17:06:08.302] FETCH ComfyRegistry Data: 50/90
[2025-06-26 17:06:10.926] [Autocomplete-Plus] CSV file status:
  * Danbooru -> base: True, extra: 
  * E621     -> base: True, extra: 
[2025-06-26 17:06:11.942] FETCH ComfyRegistry Data: 55/90
