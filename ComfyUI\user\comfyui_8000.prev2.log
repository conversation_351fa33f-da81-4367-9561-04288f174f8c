## ComfyUI-Manager: installing dependencies done.
[2025-06-26 16:06:54.185] ** ComfyUI startup time: 2025-06-26 16:06:54.185
[2025-06-26 16:06:54.185] ** Platform: Windows
[2025-06-26 16:06:54.185] ** Python version: 3.12.9 (main, Feb 12 2025, 14:52:31) [MSC v.1942 64 bit (AMD64)]
[2025-06-26 16:06:54.185] ** Python executable: D:\stable_diffusion\ComfyUI\.venv\Scripts\python.exe
[2025-06-26 16:06:54.185] ** ComfyUI Path: C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI
[2025-06-26 16:06:54.186] ** ComfyUI Base Folder Path: C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI
[2025-06-26 16:06:54.186] ** User directory: D:\stable_diffusion\ComfyUI\user
[2025-06-26 16:06:54.197] ** ComfyUI-Manager config path: D:\stable_diffusion\ComfyUI\user\default\ComfyUI-Manager\config.ini
[2025-06-26 16:06:54.197] ** Log path: D:\stable_diffusion\ComfyUI\user\comfyui.log
[ComfyUI-Manager] Failed to restore comfyui-frontend-package
[2025-06-26 16:06:55.815] expected str, bytes or os.PathLike object, not NoneType
[2025-06-26 16:06:55.815] 
Prestartup times for custom nodes:
[2025-06-26 16:06:55.816]    4.3 seconds: C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI\custom_nodes\ComfyUI-Manager
[2025-06-26 16:06:55.816] 
[2025-06-26 16:06:57.795] Checkpoint files will always be loaded safely.
[2025-06-26 16:06:59.077] Total VRAM 6144 MB, total RAM 28524 MB
[2025-06-26 16:06:59.077] pytorch version: 2.7.0+cu128
[2025-06-26 16:06:59.077] Set vram state to: NORMAL_VRAM
[2025-06-26 16:06:59.085] Device: cuda:0 NVIDIA GeForce RTX 3060 Laptop GPU : cudaMallocAsync
[2025-06-26 16:07:00.860] Using pytorch attention
[2025-06-26 16:07:02.587] Python version: 3.12.9 (main, Feb 12 2025, 14:52:31) [MSC v.1942 64 bit (AMD64)]
[2025-06-26 16:07:02.587] ComfyUI version: 0.3.41
[2025-06-26 16:07:02.736] [Prompt Server] web root: C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI\web_custom_versions\desktop_app
[2025-06-26 16:07:03.881] Adding D:\stable_diffusion\ComfyUI\custom_nodes to sys.path
[2025-06-26 16:07:03.881] Adding C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI\custom_nodes to sys.path
[2025-06-26 16:07:03.922] D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-art-venture\modules\utils.py:9: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  import pkg_resources
[2025-06-26 16:07:04.171] Could not find efficiency nodes
[2025-06-26 16:07:04.220] [36;20m[comfyui_controlnet_aux] | INFO -> Using ckpts path: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux\ckpts[0m
[2025-06-26 16:07:04.221] [36;20m[comfyui_controlnet_aux] | INFO -> Using symlinks: False[0m
[2025-06-26 16:07:04.221] [36;20m[comfyui_controlnet_aux] | INFO -> Using ort providers: ['CUDAExecutionProvider', 'DirectMLExecutionProvider', 'OpenVINOExecutionProvider', 'ROCMExecutionProvider', 'CPUExecutionProvider', 'CoreMLExecutionProvider'][0m
[2025-06-26 16:07:04.529] D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux\node_wrappers\dwpose.py:26: UserWarning: DWPose: Onnxruntime not found or doesn't come with acceleration providers, switch to OpenCV with CPU device. DWPose might run very slowly
  warnings.warn("DWPose: Onnxruntime not found or doesn't come with acceleration providers, switch to OpenCV with CPU device. DWPose might run very slowly")
[2025-06-26 16:07:04.556] Loaded ControlNetPreprocessors nodes from D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux
[2025-06-26 16:07:04.557] Could not find AdvancedControlNet nodes
[2025-06-26 16:07:04.560] Could not find AnimateDiff nodes
[2025-06-26 16:07:04.561] Could not find IPAdapter nodes
[2025-06-26 16:07:04.567] Could not find VideoHelperSuite nodes
[2025-06-26 16:07:04.569] Could not load ImpactPack nodes Could not find ImpactPack nodes
[2025-06-26 16:07:05.512] [34m[ComfyUI-Easy-Use] server: [0mv1.3.0 [92mLoaded[0m
[2025-06-26 16:07:05.512] [34m[ComfyUI-Easy-Use] web root: [0mD:\stable_diffusion\ComfyUI\custom_nodes\comfyui-easy-use\web_version/v2 [92mLoaded[0m
[2025-06-26 16:07:06.090] [06/26/25 16:07:06] WARNING  Your inference package version      __init__.py:41
[2025-06-26 16:07:06.090]                              0.50.4 is out of date! Please                     
[2025-06-26 16:07:06.090]                              upgrade to version 0.51.0 of                      
[2025-06-26 16:07:06.091]                              inference for the latest features                 
[2025-06-26 16:07:06.091]                              and bug fixes by running `pip                     
[2025-06-26 16:07:06.091]                              install --upgrade inference`.                     
[2025-06-26 16:07:06.492] [34mComfyUI-ImageCompare Nodes: [92mLoaded[0m
[2025-06-26 16:07:06.496] ### Loading: ComfyUI-Impact-Pack (V8.15.3)
[2025-06-26 16:07:06.552] [Impact Pack] Wildcards loading done.
[2025-06-26 16:07:06.556] ### Loading: ComfyUI-Impact-Subpack (V1.3.2)
[2025-06-26 16:07:06.559] [Impact Pack/Subpack] Using folder_paths to determine whitelist path: D:\stable_diffusion\ComfyUI\user\default\ComfyUI-Impact-Subpack\model-whitelist.txt
[2025-06-26 16:07:06.559] [Impact Pack/Subpack] Ensured whitelist directory exists: D:\stable_diffusion\ComfyUI\user\default\ComfyUI-Impact-Subpack
[2025-06-26 16:07:06.559] [Impact Pack/Subpack] Loaded 0 model(s) from whitelist: D:\stable_diffusion\ComfyUI\user\default\ComfyUI-Impact-Subpack\model-whitelist.txt
[2025-06-26 16:07:06.561] [Impact Subpack] ultralytics_bbox: D:\stable_diffusion\ComfyUI\models\ultralytics\bbox
[2025-06-26 16:07:06.561] [Impact Subpack] ultralytics_segm: D:\stable_diffusion\ComfyUI\models\ultralytics\segm
[2025-06-26 16:07:06.561] [Impact Subpack] ultralytics_bbox: D:\stable_diffusion\ComfyUI\models\ultralytics\bbox
[2025-06-26 16:07:06.561] [Impact Subpack] ultralytics_segm: D:\stable_diffusion\ComfyUI\models\ultralytics\segm
[2025-06-26 16:07:06.606] Found LoRA roots:
 - D:/stable_diffusion/1_Models/SDXL/loras
 - D:/stable_diffusion/ComfyUI/models/loras
[2025-06-26 16:07:06.607] Found checkpoint roots:
 - D:/stable_diffusion/1_Models/SDXL/checkpoints
 - D:/stable_diffusion/ComfyUI/models/checkpoints
 - D:/stable_diffusion/ComfyUI/models/diffusers
 - D:/stable_diffusion/ComfyUI/models/diffusion_models
 - D:/stable_diffusion/ComfyUI/models/unet
[2025-06-26 16:07:06.627] Saved folder paths to settings.json
[2025-06-26 16:07:06.756] Metadata collection hooks installed for runtime values
[2025-06-26 16:07:06.756] ComfyUI Metadata Collector initialized
[2025-06-26 16:07:06.757] Example images path: None
[2025-06-26 16:07:06.757] Added static route /loras_static/root1/preview -> D:/stable_diffusion/1_Models/SDXL/loras
[2025-06-26 16:07:06.757] Added static route /loras_static/root2/preview -> D:/stable_diffusion/ComfyUI/models/loras
[2025-06-26 16:07:06.757] Added static route /checkpoints_static/root1/preview -> D:/stable_diffusion/1_Models/SDXL/checkpoints
[2025-06-26 16:07:06.759] Added static route /checkpoints_static/root2/preview -> D:/stable_diffusion/ComfyUI/models/checkpoints
[2025-06-26 16:07:06.759] Added static route /checkpoints_static/root3/preview -> D:/stable_diffusion/ComfyUI/models/diffusers
[2025-06-26 16:07:06.759] Added static route /checkpoints_static/root4/preview -> D:/stable_diffusion/ComfyUI/models/diffusion_models
[2025-06-26 16:07:06.759] Added static route /checkpoints_static/root5/preview -> D:/stable_diffusion/ComfyUI/models/unet
[2025-06-26 16:07:07.195] FutureWarning: Importing from timm.models.layers is deprecated, please import via timm.layers
[2025-06-26 16:07:07.223] Imported node: DT_Flatten_Colors
[2025-06-26 16:07:07.223] Imported node: DT_FontText
[2025-06-26 16:07:07.224] Imported node: DT_GenerateNoise
[2025-06-26 16:07:07.228] Imported node: DT_Glitch_This
[2025-06-26 16:07:07.229] Imported node: DT_Hue_Rotation
[2025-06-26 16:07:07.229] Imported node: DT_Load_Picture_Index
[2025-06-26 16:07:07.266] Imported node: DT_PILGram
[2025-06-26 16:07:07.271] Imported node: DT_Pixel_Sort
[2025-06-26 16:07:07.649] Imported node: DT_Play_Sound_At_Execution
[2025-06-26 16:07:07.927] Imported node: DT_PromptGen
[2025-06-26 16:07:07.928] Imported node: DT_Solid_Color
[2025-06-26 16:07:07.929] Imported node: DT_Swap_Color_Mode
[2025-06-26 16:07:07.929] Imported node: DT_Flatten_Colors
[2025-06-26 16:07:07.929] Imported node: DT_FontText
[2025-06-26 16:07:07.929] Imported node: DT_GenerateNoise
[2025-06-26 16:07:07.929] Imported node: DT_Glitch_This
[2025-06-26 16:07:07.929] Imported node: DT_Hue_Rotation
[2025-06-26 16:07:07.929] Imported node: DT_Load_Picture_Index
[2025-06-26 16:07:07.929] Imported node: DT_PILGram
[2025-06-26 16:07:07.930] Imported node: DT_Pixel_Sort
[2025-06-26 16:07:07.930] Imported node: DT_Play_Sound_At_Execution
[2025-06-26 16:07:07.930] Imported node: DT_PromptGen
[2025-06-26 16:07:07.930] Imported node: DT_Solid_Color
[2025-06-26 16:07:07.930] Imported node: DT_Swap_Color_Mode
[2025-06-26 16:07:07.935] (pysssss:WD14Tagger) [DEBUG] Available ORT providers: AzureExecutionProvider, CPUExecutionProvider
[2025-06-26 16:07:07.935] (pysssss:WD14Tagger) [DEBUG] Using ORT providers: CUDAExecutionProvider, CPUExecutionProvider
[2025-06-26 16:07:07.944] SupervisionWarnings: BoundingBoxAnnotator is deprecated: `BoundingBoxAnnotator` is deprecated and has been renamed to `BoxAnnotator`. `BoundingBoxAnnotator` will be removed in supervision-0.26.0.
[2025-06-26 16:07:07.989] ------------------------------------------
[2025-06-26 16:07:07.989] [34mComfyroll Studio v1.76 : [92m 175 Nodes Loaded[0m
[2025-06-26 16:07:07.989] ------------------------------------------
[2025-06-26 16:07:07.989] ** For changes, please see patch notes at https://github.com/Suzie1/ComfyUI_Comfyroll_CustomNodes/blob/main/Patch_Notes.md
[2025-06-26 16:07:07.989] ** For help, please see the wiki at https://github.com/Suzie1/ComfyUI_Comfyroll_CustomNodes/wiki
[2025-06-26 16:07:07.989] ------------------------------------------
[2025-06-26 16:07:07.999] [36;20m[D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using ckpts path: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux\ckpts[0m
[2025-06-26 16:07:08.001] [36;20m[D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using symlinks: False[0m
[2025-06-26 16:07:08.002] [36;20m[D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using ort providers: ['CUDAExecutionProvider', 'DirectMLExecutionProvider', 'OpenVINOExecutionProvider', 'ROCMExecutionProvider', 'CPUExecutionProvider', 'CoreMLExecutionProvider'][0m
[2025-06-26 16:07:08.075] ------------------------------------
[2025-06-26 16:07:08.075] Loading nodes from: MY_NODES
[2025-06-26 16:07:08.076]   -> Loaded nodes from simple_slider.py
[2025-06-26 16:07:08.076] ------------------------------------
[2025-06-26 16:07:08.209] 
[2025-06-26 16:07:08.209] [92m[rgthree-comfy] Loaded 47 magnificent nodes. 🎉[0m
[2025-06-26 16:07:08.209] 
[2025-06-26 16:07:09.127] [34mWAS Node Suite: [0mOpenCV Python FFMPEG support is enabled[0m
[2025-06-26 16:07:09.127] [34mWAS Node Suite [93mWarning: [0m`ffmpeg_bin_path` is not set in `D:\stable_diffusion\ComfyUI\custom_nodes\was-ns\was_suite_config.json` config file. Will attempt to use system ffmpeg binaries if available.[0m
[2025-06-26 16:07:09.840] [34mWAS Node Suite: [0mFinished.[0m [32mLoaded[0m [0m220[0m [32mnodes successfully.[0m
[2025-06-26 16:07:09.840] 
	[3m[93m"Don't let yesterday take up too much of today."[0m[3m - Will Rogers[0m
[2025-06-26 16:07:09.840] 
[2025-06-26 16:07:09.858] ### Loading: ComfyUI-Manager (V3.30.4)
[2025-06-26 16:07:09.859] [ComfyUI-Manager] network_mode: public
[2025-06-26 16:07:09.860] ### ComfyUI Revision: UNKNOWN (The currently installed ComfyUI is not a Git repository)
[2025-06-26 16:07:09.900] 
Import times for custom nodes:
[2025-06-26 16:07:09.900]    0.0 seconds: C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI\custom_nodes\websocket_image_save.py
[2025-06-26 16:07:09.900]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyUI-ImageCompare
[2025-06-26 16:07:09.900]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyUI_Cutoff
[2025-06-26 16:07:09.900]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\MY_NODES
[2025-06-26 16:07:09.900]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\masquerade-nodes-comfyui
[2025-06-26 16:07:09.900]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-ultralytics-yolo
[2025-06-26 16:07:09.900]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\quick-connections
[2025-06-26 16:07:09.900]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\sdxl-recommended-res-calc
[2025-06-26 16:07:09.900]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-mxtoolkit
[2025-06-26 16:07:09.900]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\lora-info
[2025-06-26 16:07:09.900]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyUI-Contextual-SAM2
[2025-06-26 16:07:09.901]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\civitai_comfy_nodes
[2025-06-26 16:07:09.901]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-lora-auto-trigger-words
[2025-06-26 16:07:09.901]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-inpaint-cropandstitch
[2025-06-26 16:07:09.901]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-autocomplete-plus
[2025-06-26 16:07:09.901]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-wd14-tagger
[2025-06-26 16:07:09.901]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-impact-subpack
[2025-06-26 16:07:09.901]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-model-downloader
[2025-06-26 16:07:09.901]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyUI-YoloWorld-EfficientSAM
[2025-06-26 16:07:09.901]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-custom-scripts
[2025-06-26 16:07:09.901]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-inpaint-nodes
[2025-06-26 16:07:09.901]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_ultimatesdupscale
[2025-06-26 16:07:09.901]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_essentials
[2025-06-26 16:07:09.901]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyMath
[2025-06-26 16:07:09.901]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyUI-segment-anything-2
[2025-06-26 16:07:09.901]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-kjnodes
[2025-06-26 16:07:09.901]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyUI_Comfyroll_CustomNodes
[2025-06-26 16:07:09.901]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux
[2025-06-26 16:07:09.901]    0.1 seconds: C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI\custom_nodes\ComfyUI-Manager
[2025-06-26 16:07:09.901]    0.1 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-impact-pack
[2025-06-26 16:07:09.901]    0.1 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\rgthree-comfy
[2025-06-26 16:07:09.902]    0.2 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-lora-manager
[2025-06-26 16:07:09.902]    0.4 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-sam2
[2025-06-26 16:07:09.902]    0.7 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-art-venture
[2025-06-26 16:07:09.902]    0.7 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyUI-Vextra-Nodes
[2025-06-26 16:07:09.902]    0.9 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-easy-use
[2025-06-26 16:07:09.902]    1.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyUI-GroundedSAM2
[2025-06-26 16:07:09.902]    1.6 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\was-ns
[2025-06-26 16:07:09.902] 
[2025-06-26 16:07:10.246] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/model-list.json
[2025-06-26 16:07:10.248] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/alter-list.json
[2025-06-26 16:07:10.281] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/github-stats.json
[2025-06-26 16:07:10.334] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/extension-node-map.json
[2025-06-26 16:07:10.382] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json
[2025-06-26 16:07:10.563] Failed to initialize database. Please ensure you have installed the latest requirements. If the error persists, please report this as in future the database will be required: (sqlite3.OperationalError) unable to open database file
(Background on this error at: https://sqlalche.me/e/20/e3q8)
[2025-06-26 16:07:10.602] Metadata collection hooks installed for runtime values
[2025-06-26 16:07:10.602] ComfyUI Metadata Collector initialized
[2025-06-26 16:07:10.602] LoRA Manager: All services initialized and background tasks scheduled
[2025-06-26 16:07:10.602] Starting server

[2025-06-26 16:07:10.602] Cache files disabled for lora, skipping load
[2025-06-26 16:07:10.603] Cache files disabled for checkpoint, skipping load
[2025-06-26 16:07:10.606] To see the GUI go to: http://127.0.0.1:8000
[2025-06-26 16:07:10.607] Recipe cache initialized in 0.00 seconds. Found 0 recipes
[2025-06-26 16:07:10.622] Checkpoint cache initialized in 0.01 seconds. Found 12 models
[2025-06-26 16:07:10.676] Lora cache initialized in 0.05 seconds. Found 60 models
[2025-06-26 16:07:13.531] FETCH ComfyRegistry Data: 5/90
[2025-06-26 16:07:14.671] [Autocomplete-Plus] CSV file status:
  * Danbooru -> base: True, extra: 
  * E621     -> base: True, extra: 
[2025-06-26 16:07:16.973] FETCH ComfyRegistry Data: 10/90
[2025-06-26 16:07:20.480] FETCH ComfyRegistry Data: 15/90
[2025-06-26 16:07:23.907] FETCH ComfyRegistry Data: 20/90
[2025-06-26 16:07:27.404] FETCH ComfyRegistry Data: 25/90
[2025-06-26 16:07:30.880] FETCH ComfyRegistry Data: 30/90
[2025-06-26 16:07:34.452] FETCH ComfyRegistry Data: 35/90
[2025-06-26 16:07:37.847] FETCH ComfyRegistry Data: 40/90
[2025-06-26 16:07:41.227] FETCH ComfyRegistry Data: 45/90
[2025-06-26 16:07:44.778] FETCH ComfyRegistry Data: 50/90
[2025-06-26 16:07:48.267] FETCH ComfyRegistry Data: 55/90
[2025-06-26 16:07:51.867] FETCH ComfyRegistry Data: 60/90
[2025-06-26 16:07:55.398] FETCH ComfyRegistry Data: 65/90
[2025-06-26 16:07:58.956] FETCH ComfyRegistry Data: 70/90
[2025-06-26 16:08:02.542] FETCH ComfyRegistry Data: 75/90
[2025-06-26 16:08:06.112] FETCH ComfyRegistry Data: 80/90
[2025-06-26 16:08:09.577] FETCH ComfyRegistry Data: 85/90
[2025-06-26 16:08:13.258] FETCH ComfyRegistry Data: 90/90
[2025-06-26 16:08:13.758] FETCH ComfyRegistry Data [DONE]
[2025-06-26 16:08:13.893] [ComfyUI-Manager] default cache updated: https://api.comfy.org/nodes
[2025-06-26 16:08:13.932] nightly_channel: 
[2025-06-26 16:08:13.933] https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/remote
[2025-06-26 16:08:13.933] FETCH DATA from: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json [DONE]
[2025-06-26 16:08:14.086] [ComfyUI-Manager] All startup tasks have been completed.
