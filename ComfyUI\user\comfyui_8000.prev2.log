## ComfyUI-Manager: installing dependencies done.
[2025-06-26 14:56:26.141] ** ComfyUI startup time: 2025-06-26 14:56:26.141
[2025-06-26 14:56:26.141] ** Platform: Windows
[2025-06-26 14:56:26.141] ** Python version: 3.12.9 (main, Feb 12 2025, 14:52:31) [MSC v.1942 64 bit (AMD64)]
[2025-06-26 14:56:26.141] ** Python executable: D:\stable_diffusion\ComfyUI\.venv\Scripts\python.exe
[2025-06-26 14:56:26.141] ** ComfyUI Path: C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI
[2025-06-26 14:56:26.141] ** ComfyUI Base Folder Path: C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI
[2025-06-26 14:56:26.141] ** User directory: D:\stable_diffusion\ComfyUI\user
[2025-06-26 14:56:26.186] ** ComfyUI-Manager config path: D:\stable_diffusion\ComfyUI\user\default\ComfyUI-Manager\config.ini
[2025-06-26 14:56:26.186] ** Log path: D:\stable_diffusion\ComfyUI\user\comfyui.log
[ComfyUI-Manager] Failed to restore comfyui-frontend-package
[2025-06-26 14:56:27.800] expected str, bytes or os.PathLike object, not NoneType
[2025-06-26 14:56:27.801] 
Prestartup times for custom nodes:
[2025-06-26 14:56:27.801]    4.3 seconds: C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI\custom_nodes\ComfyUI-Manager
[2025-06-26 14:56:27.801] 
[2025-06-26 14:56:29.791] Checkpoint files will always be loaded safely.
[2025-06-26 14:56:31.089] Total VRAM 6144 MB, total RAM 28524 MB
[2025-06-26 14:56:31.089] pytorch version: 2.7.0+cu128
[2025-06-26 14:56:31.089] Set vram state to: NORMAL_VRAM
[2025-06-26 14:56:31.101] Device: cuda:0 NVIDIA GeForce RTX 3060 Laptop GPU : cudaMallocAsync
[2025-06-26 14:56:32.956] Using pytorch attention
[2025-06-26 14:56:34.740] Python version: 3.12.9 (main, Feb 12 2025, 14:52:31) [MSC v.1942 64 bit (AMD64)]
[2025-06-26 14:56:34.740] ComfyUI version: 0.3.41
[2025-06-26 14:56:34.921] [Prompt Server] web root: C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI\web_custom_versions\desktop_app
[2025-06-26 14:56:36.093] Adding D:\stable_diffusion\ComfyUI\custom_nodes to sys.path
[2025-06-26 14:56:36.093] Adding C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI\custom_nodes to sys.path
[2025-06-26 14:56:36.138] D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-art-venture\modules\utils.py:9: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  import pkg_resources
[2025-06-26 14:56:36.398] Could not find efficiency nodes
[2025-06-26 14:56:36.451] [36;20m[comfyui_controlnet_aux] | INFO -> Using ckpts path: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux\ckpts[0m
[2025-06-26 14:56:36.452] [36;20m[comfyui_controlnet_aux] | INFO -> Using symlinks: False[0m
[2025-06-26 14:56:36.453] [36;20m[comfyui_controlnet_aux] | INFO -> Using ort providers: ['CUDAExecutionProvider', 'DirectMLExecutionProvider', 'OpenVINOExecutionProvider', 'ROCMExecutionProvider', 'CPUExecutionProvider', 'CoreMLExecutionProvider'][0m
[2025-06-26 14:56:36.783] D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux\node_wrappers\dwpose.py:26: UserWarning: DWPose: Onnxruntime not found or doesn't come with acceleration providers, switch to OpenCV with CPU device. DWPose might run very slowly
  warnings.warn("DWPose: Onnxruntime not found or doesn't come with acceleration providers, switch to OpenCV with CPU device. DWPose might run very slowly")
[2025-06-26 14:56:36.814] Loaded ControlNetPreprocessors nodes from D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux
[2025-06-26 14:56:36.815] Could not find AdvancedControlNet nodes
[2025-06-26 14:56:36.818] Could not find AnimateDiff nodes
[2025-06-26 14:56:36.819] Could not find IPAdapter nodes
[2025-06-26 14:56:36.827] Could not find VideoHelperSuite nodes
[2025-06-26 14:56:36.830] Could not load ImpactPack nodes Could not find ImpactPack nodes
[2025-06-26 14:56:37.842] [34m[ComfyUI-Easy-Use] server: [0mv1.3.0 [92mLoaded[0m
[2025-06-26 14:56:37.842] [34m[ComfyUI-Easy-Use] web root: [0mD:\stable_diffusion\ComfyUI\custom_nodes\comfyui-easy-use\web_version/v2 [92mLoaded[0m
[2025-06-26 14:56:38.384] [06/26/25 14:56:38] WARNING  Your inference package version      __init__.py:41
[2025-06-26 14:56:38.384]                              0.50.4 is out of date! Please                     
[2025-06-26 14:56:38.384]                              upgrade to version 0.51.0 of                      
[2025-06-26 14:56:38.384]                              inference for the latest features                 
[2025-06-26 14:56:38.384]                              and bug fixes by running `pip                     
[2025-06-26 14:56:38.384]                              install --upgrade inference`.                     
[2025-06-26 14:56:38.794] [34mComfyUI-ImageCompare Nodes: [92mLoaded[0m
[2025-06-26 14:56:38.800] ### Loading: ComfyUI-Impact-Pack (V8.15.3)
[2025-06-26 14:56:38.857] [Impact Pack] Wildcards loading done.
[2025-06-26 14:56:38.863] ### Loading: ComfyUI-Impact-Subpack (V1.3.2)
[2025-06-26 14:56:38.866] [Impact Pack/Subpack] Using folder_paths to determine whitelist path: D:\stable_diffusion\ComfyUI\user\default\ComfyUI-Impact-Subpack\model-whitelist.txt
[2025-06-26 14:56:38.866] [Impact Pack/Subpack] Ensured whitelist directory exists: D:\stable_diffusion\ComfyUI\user\default\ComfyUI-Impact-Subpack
[2025-06-26 14:56:38.866] [Impact Pack/Subpack] Loaded 0 model(s) from whitelist: D:\stable_diffusion\ComfyUI\user\default\ComfyUI-Impact-Subpack\model-whitelist.txt
[2025-06-26 14:56:38.869] [Impact Subpack] ultralytics_bbox: D:\stable_diffusion\ComfyUI\models\ultralytics\bbox
[2025-06-26 14:56:38.869] [Impact Subpack] ultralytics_segm: D:\stable_diffusion\ComfyUI\models\ultralytics\segm
[2025-06-26 14:56:38.869] [Impact Subpack] ultralytics_bbox: D:\stable_diffusion\ComfyUI\models\ultralytics\bbox
[2025-06-26 14:56:38.869] [Impact Subpack] ultralytics_segm: D:\stable_diffusion\ComfyUI\models\ultralytics\segm
[2025-06-26 14:56:38.905] Found LoRA roots:
 - D:/stable_diffusion/1_Models/SDXL/loras
 - D:/stable_diffusion/ComfyUI/models/loras
[2025-06-26 14:56:38.905] Found checkpoint roots:
 - D:/stable_diffusion/1_Models/SDXL/checkpoints
 - D:/stable_diffusion/ComfyUI/models/checkpoints
 - D:/stable_diffusion/ComfyUI/models/diffusers
 - D:/stable_diffusion/ComfyUI/models/diffusion_models
 - D:/stable_diffusion/ComfyUI/models/unet
[2025-06-26 14:56:38.937] Saved folder paths to settings.json
[2025-06-26 14:56:39.070] Metadata collection hooks installed for runtime values
[2025-06-26 14:56:39.070] ComfyUI Metadata Collector initialized
[2025-06-26 14:56:39.070] Example images path: None
[2025-06-26 14:56:39.072] Added static route /loras_static/root1/preview -> D:/stable_diffusion/1_Models/SDXL/loras
[2025-06-26 14:56:39.072] Added static route /loras_static/root2/preview -> D:/stable_diffusion/ComfyUI/models/loras
[2025-06-26 14:56:39.073] Added static route /checkpoints_static/root1/preview -> D:/stable_diffusion/1_Models/SDXL/checkpoints
[2025-06-26 14:56:39.073] Added static route /checkpoints_static/root2/preview -> D:/stable_diffusion/ComfyUI/models/checkpoints
[2025-06-26 14:56:39.073] Added static route /checkpoints_static/root3/preview -> D:/stable_diffusion/ComfyUI/models/diffusers
[2025-06-26 14:56:39.074] Added static route /checkpoints_static/root4/preview -> D:/stable_diffusion/ComfyUI/models/diffusion_models
[2025-06-26 14:56:39.075] Added static route /checkpoints_static/root5/preview -> D:/stable_diffusion/ComfyUI/models/unet
[2025-06-26 14:56:39.502] FutureWarning: Importing from timm.models.layers is deprecated, please import via timm.layers
[2025-06-26 14:56:39.536] Imported node: DT_Flatten_Colors
[2025-06-26 14:56:39.536] Imported node: DT_FontText
[2025-06-26 14:56:39.542] Imported node: DT_GenerateNoise
[2025-06-26 14:56:39.545] Imported node: DT_Glitch_This
[2025-06-26 14:56:39.545] Imported node: DT_Hue_Rotation
[2025-06-26 14:56:39.547] Imported node: DT_Load_Picture_Index
[2025-06-26 14:56:39.579] Imported node: DT_PILGram
[2025-06-26 14:56:39.588] Imported node: DT_Pixel_Sort
[2025-06-26 14:56:39.966] Imported node: DT_Play_Sound_At_Execution
[2025-06-26 14:56:40.235] Imported node: DT_PromptGen
[2025-06-26 14:56:40.236] Imported node: DT_Solid_Color
[2025-06-26 14:56:40.236] Imported node: DT_Swap_Color_Mode
[2025-06-26 14:56:40.236] Imported node: DT_Flatten_Colors
[2025-06-26 14:56:40.236] Imported node: DT_FontText
[2025-06-26 14:56:40.236] Imported node: DT_GenerateNoise
[2025-06-26 14:56:40.237] Imported node: DT_Glitch_This
[2025-06-26 14:56:40.237] Imported node: DT_Hue_Rotation
[2025-06-26 14:56:40.237] Imported node: DT_Load_Picture_Index
[2025-06-26 14:56:40.237] Imported node: DT_PILGram
[2025-06-26 14:56:40.237] Imported node: DT_Pixel_Sort
[2025-06-26 14:56:40.237] Imported node: DT_Play_Sound_At_Execution
[2025-06-26 14:56:40.237] Imported node: DT_PromptGen
[2025-06-26 14:56:40.237] Imported node: DT_Solid_Color
[2025-06-26 14:56:40.237] Imported node: DT_Swap_Color_Mode
[2025-06-26 14:56:40.242] (pysssss:WD14Tagger) [DEBUG] Available ORT providers: AzureExecutionProvider, CPUExecutionProvider
[2025-06-26 14:56:40.243] (pysssss:WD14Tagger) [DEBUG] Using ORT providers: CUDAExecutionProvider, CPUExecutionProvider
[2025-06-26 14:56:40.251] SupervisionWarnings: BoundingBoxAnnotator is deprecated: `BoundingBoxAnnotator` is deprecated and has been renamed to `BoxAnnotator`. `BoundingBoxAnnotator` will be removed in supervision-0.26.0.
[2025-06-26 14:56:40.290] ------------------------------------------
[2025-06-26 14:56:40.290] [34mComfyroll Studio v1.76 : [92m 175 Nodes Loaded[0m
[2025-06-26 14:56:40.290] ------------------------------------------
[2025-06-26 14:56:40.290] ** For changes, please see patch notes at https://github.com/Suzie1/ComfyUI_Comfyroll_CustomNodes/blob/main/Patch_Notes.md
[2025-06-26 14:56:40.290] ** For help, please see the wiki at https://github.com/Suzie1/ComfyUI_Comfyroll_CustomNodes/wiki
[2025-06-26 14:56:40.290] ------------------------------------------
[2025-06-26 14:56:40.307] [36;20m[D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using ckpts path: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux\ckpts[0m
[2025-06-26 14:56:40.307] [36;20m[D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using symlinks: False[0m
[2025-06-26 14:56:40.308] [36;20m[D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using ort providers: ['CUDAExecutionProvider', 'DirectMLExecutionProvider', 'OpenVINOExecutionProvider', 'ROCMExecutionProvider', 'CPUExecutionProvider', 'CoreMLExecutionProvider'][0m
[2025-06-26 14:56:40.381] ------------------------------------
[2025-06-26 14:56:40.381] Loading nodes from: MY_NODES
[2025-06-26 14:56:40.382]   -> Loaded nodes from simple_slider.py
[2025-06-26 14:56:40.382] ------------------------------------
[2025-06-26 14:56:40.510] 
[2025-06-26 14:56:40.511] [92m[rgthree-comfy] Loaded 47 exciting nodes. 🎉[0m
[2025-06-26 14:56:40.511] 
[2025-06-26 14:56:41.485] [34mWAS Node Suite: [0mOpenCV Python FFMPEG support is enabled[0m
[2025-06-26 14:56:41.485] [34mWAS Node Suite [93mWarning: [0m`ffmpeg_bin_path` is not set in `D:\stable_diffusion\ComfyUI\custom_nodes\was-ns\was_suite_config.json` config file. Will attempt to use system ffmpeg binaries if available.[0m
[2025-06-26 14:56:42.213] [34mWAS Node Suite: [0mFinished.[0m [32mLoaded[0m [0m220[0m [32mnodes successfully.[0m
[2025-06-26 14:56:42.213] 
	[3m[93m"Everything you've ever wanted is on the other side of fear."[0m[3m - George Addair[0m
[2025-06-26 14:56:42.213] 
[2025-06-26 14:56:42.232] ### Loading: ComfyUI-Manager (V3.30.4)
[2025-06-26 14:56:42.233] [ComfyUI-Manager] network_mode: public
[2025-06-26 14:56:42.234] ### ComfyUI Revision: UNKNOWN (The currently installed ComfyUI is not a Git repository)
[2025-06-26 14:56:42.271] 
Import times for custom nodes:
[2025-06-26 14:56:42.271]    0.0 seconds: C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI\custom_nodes\websocket_image_save.py
[2025-06-26 14:56:42.271]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyUI-ImageCompare
[2025-06-26 14:56:42.271]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\quick-connections
[2025-06-26 14:56:42.272]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyUI_Cutoff
[2025-06-26 14:56:42.272]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\MY_NODES
[2025-06-26 14:56:42.272]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-ultralytics-yolo
[2025-06-26 14:56:42.272]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\masquerade-nodes-comfyui
[2025-06-26 14:56:42.272]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyUI-Contextual-SAM2
[2025-06-26 14:56:42.272]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-mxtoolkit
[2025-06-26 14:56:42.272]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\lora-info
[2025-06-26 14:56:42.272]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\civitai_comfy_nodes
[2025-06-26 14:56:42.272]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-lora-auto-trigger-words
[2025-06-26 14:56:42.272]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\sdxl-recommended-res-calc
[2025-06-26 14:56:42.272]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-inpaint-cropandstitch
[2025-06-26 14:56:42.272]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-autocomplete-plus
[2025-06-26 14:56:42.272]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-wd14-tagger
[2025-06-26 14:56:42.272]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyUI-YoloWorld-EfficientSAM
[2025-06-26 14:56:42.272]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-model-downloader
[2025-06-26 14:56:42.272]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-impact-subpack
[2025-06-26 14:56:42.272]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-inpaint-nodes
[2025-06-26 14:56:42.273]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_essentials
[2025-06-26 14:56:42.273]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-custom-scripts
[2025-06-26 14:56:42.273]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_ultimatesdupscale
[2025-06-26 14:56:42.273]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyMath
[2025-06-26 14:56:42.273]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyUI-segment-anything-2
[2025-06-26 14:56:42.273]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-kjnodes
[2025-06-26 14:56:42.273]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui_controlnet_aux
[2025-06-26 14:56:42.273]    0.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyUI_Comfyroll_CustomNodes
[2025-06-26 14:56:42.273]    0.0 seconds: C:\Users\<USER>\AppData\Local\Programs\@comfyorgcomfyui-electron\resources\ComfyUI\custom_nodes\ComfyUI-Manager
[2025-06-26 14:56:42.273]    0.1 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-impact-pack
[2025-06-26 14:56:42.273]    0.1 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\rgthree-comfy
[2025-06-26 14:56:42.273]    0.2 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-lora-manager
[2025-06-26 14:56:42.273]    0.4 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-sam2
[2025-06-26 14:56:42.273]    0.7 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyUI-Vextra-Nodes
[2025-06-26 14:56:42.273]    0.7 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-art-venture
[2025-06-26 14:56:42.273]    0.9 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\ComfyUI-GroundedSAM2
[2025-06-26 14:56:42.273]    1.0 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\comfyui-easy-use
[2025-06-26 14:56:42.273]    1.7 seconds: D:\stable_diffusion\ComfyUI\custom_nodes\was-ns
[2025-06-26 14:56:42.273] 
[2025-06-26 14:56:42.609] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/alter-list.json
[2025-06-26 14:56:42.622] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/model-list.json
[2025-06-26 14:56:42.648] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/github-stats.json
[2025-06-26 14:56:42.697] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/extension-node-map.json
[2025-06-26 14:56:42.759] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json
[2025-06-26 14:56:42.918] Failed to initialize database. Please ensure you have installed the latest requirements. If the error persists, please report this as in future the database will be required: (sqlite3.OperationalError) unable to open database file
(Background on this error at: https://sqlalche.me/e/20/e3q8)
[2025-06-26 14:56:42.960] Metadata collection hooks installed for runtime values
[2025-06-26 14:56:42.960] ComfyUI Metadata Collector initialized
[2025-06-26 14:56:42.960] LoRA Manager: All services initialized and background tasks scheduled
[2025-06-26 14:56:42.961] Starting server

[2025-06-26 14:56:42.961] Cache files disabled for lora, skipping load
[2025-06-26 14:56:42.962] Cache files disabled for checkpoint, skipping load
[2025-06-26 14:56:42.964] To see the GUI go to: http://127.0.0.1:8000
[2025-06-26 14:56:42.965] Recipe cache initialized in 0.00 seconds. Found 0 recipes
[2025-06-26 14:56:42.980] Checkpoint cache initialized in 0.01 seconds. Found 12 models
[2025-06-26 14:56:43.031] Lora cache initialized in 0.05 seconds. Found 60 models
[2025-06-26 14:56:45.757] FETCH ComfyRegistry Data: 5/90
[2025-06-26 14:56:46.158] Error handling request from 127.0.0.1
[2025-06-26 14:56:49.149] FETCH ComfyRegistry Data: 10/90
[2025-06-26 14:56:52.663] FETCH ComfyRegistry Data: 15/90
[2025-06-26 14:56:56.133] FETCH ComfyRegistry Data: 20/90
[2025-06-26 14:57:00.391] FETCH ComfyRegistry Data: 25/90
[2025-06-26 14:57:03.886] FETCH ComfyRegistry Data: 30/90
[2025-06-26 14:57:07.340] FETCH ComfyRegistry Data: 35/90
[2025-06-26 14:57:10.904] FETCH ComfyRegistry Data: 40/90
[2025-06-26 14:57:14.411] FETCH ComfyRegistry Data: 45/90
[2025-06-26 14:57:17.881] FETCH ComfyRegistry Data: 50/90
[2025-06-26 14:57:21.386] FETCH ComfyRegistry Data: 55/90
[2025-06-26 14:57:24.890] FETCH ComfyRegistry Data: 60/90
[2025-06-26 14:57:30.029] FETCH ComfyRegistry Data: 65/90
[2025-06-26 14:57:33.680] FETCH ComfyRegistry Data: 70/90
[2025-06-26 14:57:37.274] FETCH ComfyRegistry Data: 75/90
[2025-06-26 14:57:41.106] FETCH ComfyRegistry Data: 80/90
[2025-06-26 14:57:44.592] FETCH ComfyRegistry Data: 85/90
[2025-06-26 14:57:48.026] FETCH ComfyRegistry Data: 90/90
[2025-06-26 14:57:48.527] FETCH ComfyRegistry Data [DONE]
[2025-06-26 14:57:48.655] [ComfyUI-Manager] default cache updated: https://api.comfy.org/nodes
[2025-06-26 14:57:48.692] nightly_channel: 
[2025-06-26 14:57:48.692] https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/remote
[2025-06-26 14:57:48.692] FETCH DATA from: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json [DONE]
[2025-06-26 14:57:48.831] [ComfyUI-Manager] All startup tasks have been completed.
