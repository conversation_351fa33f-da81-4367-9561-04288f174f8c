{"Comfy-Desktop.AutoUpdate": true, "Comfy-Desktop.SendStatistics": true, "Comfy.ColorPalette": "dark", "Comfy.UseNewMenu": "Top", "Comfy.Workflow.WorkflowTabsPosition": "Topbar", "Comfy.Workflow.ShowMissingModelsWarning": true, "Comfy.Server.LaunchArgs": {}, "Comfy-Desktop.UV.PythonInstallMirror": "https://github.com/astral-sh/python-build-standalone/releases/download", "Comfy-Desktop.UV.PypiInstallMirror": "https://pypi.org/simple/", "Comfy-Desktop.UV.TorchInstallMirror": "https://download.pytorch.org/whl/cu128", "Comfy.TutorialCompleted": true, "Comfy.CustomColorPalettes": {"obsidian": {"version": 112, "id": "obsidian", "name": "Obsidian", "colors": {"node_slot": {"CLIP": "#FFD500", "CLIP_VISION": "#A8DADC", "CLIP_VISION_OUTPUT": "#ad7452", "CONDITIONING": "#FFA931", "CONTROL_NET": "#6EE7B7", "IMAGE": "#64B5F6", "LATENT": "#FF9CF9", "MASK": "#81C784", "MODEL": "#B39DDB", "STYLE_MODEL": "#C2FFAE", "VAE": "#FF6E6E", "TAESD": "#DCC274", "PIPE_LINE": "#7737AA", "PIPE_LINE_SDXL": "#7737AA", "INT": "#29699C", "X_Y": "#38291f", "XYPLOT": "#74DA5D", "LORA_STACK": "#94dccd", "CONTROL_NET_STACK": "#94dccd", "FAST_MODEL_LOADER": "#ffd399", "SAMPLING": "#60a5fa"}, "litegraph_base": {"BACKGROUND_IMAGE": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAIAAAD/gAIDAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAQBJREFUeNrs1rEKwjAUhlETUkj3vP9rdmr1Ysammk2w5wdxuLgcMHyptfawuZX4pJSWZTnfnu/lnIe/jNNxHHGNn//HNbbv+4dr6V+11uF527arU7+u63qfa/bnmh8sWLBgwYJlqRf8MEptXPBXJXa37BSl3ixYsGDBMliwFLyCV/DeLIMFCxYsWLBMwSt4Be/NggXLYMGCBUvBK3iNruC9WbBgwYJlsGApeAWv4L1ZBgsWLFiwYJmCV/AK3psFC5bBggULloJX8BpdwXuzYMGCBctgwVLwCl7Be7MMFixYsGDBsu8FH1FaSmExVfAxBa/gvVmwYMGCZbBg/W4vAQYA5tRF9QYlv/QAAAAASUVORK5CYII=", "CLEAR_BACKGROUND_COLOR": "#222222", "NODE_TITLE_COLOR": "#d4d4d8", "NODE_SELECTED_TITLE_COLOR": "#ffffff", "NODE_TEXT_SIZE": 14, "NODE_TEXT_COLOR": "#ffffff", "NODE_SUBTEXT_SIZE": 12, "NODE_DEFAULT_COLOR": "#09090b", "NODE_DEFAULT_BGCOLOR": "rgba(24,24,27,.9)", "NODE_DEFAULT_BOXCOLOR": "rgba(255,255,255,.75)", "NODE_DEFAULT_SHAPE": 2, "NODE_BOX_OUTLINE_COLOR": "#60a5fa", "NODE_BYPASS_BGCOLOR": "#FF00FF", "NODE_ERROR_COLOUR": "#E00", "DEFAULT_SHADOW_COLOR": "rgba(0,0,0,0)", "DEFAULT_GROUP_FONT": 24, "WIDGET_BGCOLOR": "#242427", "WIDGET_OUTLINE_COLOR": "#3f3f46", "WIDGET_TEXT_COLOR": "#d4d4d8", "WIDGET_SECONDARY_TEXT_COLOR": "#d4d4d8", "LINK_COLOR": "#9A9", "EVENT_LINK_COLOR": "#A86", "CONNECTING_LINK_COLOR": "#AFA"}, "comfy_base": {"fg-color": "#fff", "bg-color": "#09090b", "comfy-menu-bg": "rgba(24,24,24,.9)", "comfy-input-bg": "#262626", "input-text": "#ddd", "descrip-text": "#999", "drag-text": "#ccc", "error-text": "#ff4444", "border-color": "#29292c", "tr-even-bg-color": "rgba(28,28,28,.9)", "tr-odd-bg-color": "rgba(19,19,19,.9)"}}}, "obsidian_dark": {"version": 112, "id": "obsidian_dark", "name": "Obsidian Dark", "colors": {"node_slot": {"CLIP": "#FFD500", "CLIP_VISION": "#A8DADC", "CLIP_VISION_OUTPUT": "#ad7452", "CONDITIONING": "#FFA931", "CONTROL_NET": "#6EE7B7", "IMAGE": "#64B5F6", "LATENT": "#FF9CF9", "MASK": "#81C784", "MODEL": "#B39DDB", "STYLE_MODEL": "#C2FFAE", "VAE": "#FF6E6E", "TAESD": "#DCC274", "PIPE_LINE": "#7737AA", "PIPE_LINE_SDXL": "#7737AA", "INT": "#29699C", "X_Y": "#38291f", "XYPLOT": "#74DA5D", "LORA_STACK": "#94dccd", "CONTROL_NET_STACK": "#94dccd", "FAST_MODEL_LOADER": "#ffd399", "SAMPLING": "#60a5fa"}, "litegraph_base": {"BACKGROUND_IMAGE": "data:image/png;base64,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", "CLEAR_BACKGROUND_COLOR": "#09090b", "NODE_TITLE_COLOR": "#d4d4d8", "NODE_SELECTED_TITLE_COLOR": "#ffffff", "NODE_TEXT_SIZE": 14, "NODE_TEXT_COLOR": "#ffffff", "NODE_SUBTEXT_SIZE": 12, "NODE_DEFAULT_COLOR": "#09090b", "NODE_DEFAULT_BGCOLOR": "rgba(24,24,27,.9)", "NODE_DEFAULT_BOXCOLOR": "rgba(255,255,255,.75)", "NODE_DEFAULT_SHAPE": 2, "NODE_BOX_OUTLINE_COLOR": "#60a5fa", "NODE_BYPASS_BGCOLOR": "#FF00FF", "NODE_ERROR_COLOUR": "#E00", "DEFAULT_SHADOW_COLOR": "rgba(0,0,0,0)", "DEFAULT_GROUP_FONT": 24, "WIDGET_BGCOLOR": "#242427", "WIDGET_OUTLINE_COLOR": "#3f3f46", "WIDGET_TEXT_COLOR": "#d4d4d8", "WIDGET_SECONDARY_TEXT_COLOR": "#d4d4d8", "LINK_COLOR": "#9A9", "EVENT_LINK_COLOR": "#A86", "CONNECTING_LINK_COLOR": "#AFA"}, "comfy_base": {"fg-color": "#fff", "bg-color": "#09090b", "comfy-menu-bg": "rgba(24,24,24,.9)", "comfy-input-bg": "#262626", "input-text": "#ddd", "descrip-text": "#999", "drag-text": "#ccc", "error-text": "#ff4444", "border-color": "#29292c", "tr-even-bg-color": "rgba(28,28,28,.9)", "tr-odd-bg-color": "rgba(19,19,19,.9)"}}}, "milk_white": {"id": "milk_white", "name": "<PERSON> White", "colors": {"node_slot": {"CLIP": "#FFA726", "CLIP_VISION": "#5C6BC0", "CLIP_VISION_OUTPUT": "#8D6E63", "CONDITIONING": "#EF5350", "CONTROL_NET": "#66BB6A", "IMAGE": "#42A5F5", "LATENT": "#AB47BC", "MASK": "#9CCC65", "MODEL": "#7E57C2", "STYLE_MODEL": "#D4E157", "VAE": "#FF7043", "PIPE_LINE": "#7737AA", "PIPE_LINE_SDXL": "#7737AA", "INT": "#29699C", "X_Y": "#38291f", "XYPLOT": "#74DA5D", "LORA_STACK": "#94dccd", "CONTROL_NET_STACK": "#94dccd", "FAST_MODEL_LOADER": "#ffd399", "SAMPLING": "#60a5fa"}, "litegraph_base": {"BACKGROUND_IMAGE": "data:image/gif;base64,R0lGODlhZABkALMAAAAAAP///+vr6+rq6ujo6Ofn5+bm5uXl5d3d3f///wAAAAAAAAAAAAAAAAAAAAAAACH5BAEAAAkALAAAAABkAGQAAAT/UMhJq7046827HkcoHkYxjgZhnGG6si5LqnIM0/fL4qwwIMAg0CAsEovBIxKhRDaNy2GUOX0KfVFrssrNdpdaqTeKBX+dZ+jYvEaTf+y4W66mC8PUdrE879f9d2mBeoNLfH+IhYBbhIx2jkiHiomQlGKPl4uZe3CaeZifnnijgkESBqipqqusra6vsLGys62SlZO4t7qbuby7CLa+wqGWxL3Gv3jByMOkjc2lw8vOoNSi0czAncXW3Njdx9Pf48/Z4Kbbx+fQ5evZ4u3k1fKR6cn03vHlp7T9/v8A/8Gbp4+gwXoFryXMB2qgwoMMHyKEqA5fxX322FG8tzBcRnMW/zlulPbRncmQGidKjMjyYsOSKEF2FBlJQMCbOHP6c9iSZs+UnGYCdbnSo1CZI5F64kn0p1KnTH02nSoV3dGTV7FFHVqVq1dtWcMmVQZTbNGu72zqXMuW7danVL+6e4t1bEy6MeueBYLXrNO5Ze36jQtWsOG97wIj1vt3St/DjTEORss4nNq2mDP3e7w4r1bFkSET5hy6s2TRlD2/mSxXtSHQhCunXo26NevCpmvD/UU6tuullzULH76q92zdZG/Ltv1a+W+osI/nRmyc+fRi1Xdbh+68+0vv10dH3+77KD/i6IdnX669/frn5Zsjh4/2PXju8+8bzc9/6fj27LFnX11/+IUnXWl7BJfegm79FyB9JOl3oHgSklefgxAC+FmFGpqHIYcCfkhgfCohSKKJVo044YUMttggiBkmp6KFXw1oII24oYhjiDByaKOOHcp3Y5BD/njikSkO+eBREQAAOw==", "CLEAR_BACKGROUND_COLOR": "lightgray", "NODE_TITLE_COLOR": "#222", "NODE_SELECTED_TITLE_COLOR": "#000", "NODE_TEXT_SIZE": 14, "NODE_TEXT_COLOR": "#444", "NODE_SUBTEXT_SIZE": 12, "NODE_DEFAULT_COLOR": "#F7F7F7", "NODE_DEFAULT_BGCOLOR": "#F5F5F5", "NODE_DEFAULT_BOXCOLOR": "#555", "NODE_DEFAULT_SHAPE": 2, "NODE_BOX_OUTLINE_COLOR": "#000", "DEFAULT_SHADOW_COLOR": "rgba(0,0,0,0.1)", "DEFAULT_GROUP_FONT": 24, "WIDGET_BGCOLOR": "#D4D4D4", "WIDGET_OUTLINE_COLOR": "#999", "WIDGET_TEXT_COLOR": "#222", "WIDGET_SECONDARY_TEXT_COLOR": "#555", "LINK_COLOR": "#9A9", "EVENT_LINK_COLOR": "#FF9800", "CONNECTING_LINK_COLOR": "#222"}, "comfy_base": {"fg-color": "#222", "bg-color": "#DDD", "comfy-menu-bg": "#F5F5F5", "comfy-input-bg": "#C9C9C9", "input-text": "#222", "descrip-text": "#444", "drag-text": "#555", "error-text": "#F44336", "border-color": "#bbb", "tr-even-bg-color": "#f9f9f9", "tr-odd-bg-color": "#fff", "content-bg": "#e0e0e0", "content-fg": "#222", "content-hover-bg": "#adadad", "content-hover-fg": "#222"}}}}, "pysssss.SnapToGrid": true, "Comfy-Desktop.WindowStyle": "default", "pysssss.AutoCompleter": false, "Comfy.LinkRenderMode": 0, "Comfy.Sidebar.Size": "normal", "Comfy.TreeExplorer.ItemPadding": 2, "Comfy.Workflow.AutoSave": "after delay", "circuit-board-lines.enable": 1, "circuit-board-lines.only90or45": true, "quick-connections.enable": true}