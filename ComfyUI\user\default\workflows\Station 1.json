{"id": "ebe90e0e-7aaf-495f-b225-693d0e659fc2", "revision": 0, "last_node_id": 241, "last_link_id": 233, "nodes": [{"id": 15, "type": "SaveImage", "pos": [1300, -970], "size": [800, 770], "flags": {}, "order": 14, "mode": 0, "inputs": [{"localized_name": "images", "name": "images", "type": "IMAGE", "link": 104}, {"localized_name": "filename_prefix", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}, "link": null}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.41", "Node name for S&R": "SaveImage"}, "widgets_values": ["%date:yyyy-MM-dd%/NSFW/%date:hhmmss%"]}, {"id": 50, "type": "workflow>Load ControlNet Model & Image", "pos": [-200, -1470], "size": [300, 370], "flags": {}, "order": 0, "mode": 0, "inputs": [{"localized_name": "control_net_name", "name": "control_net_name", "type": "COMBO", "widget": {"name": "control_net_name"}, "link": null}, {"localized_name": "image", "name": "image", "type": "COMBO", "widget": {"name": "image"}, "link": null}, {"localized_name": "upload", "name": "upload", "type": "IMAGEUPLOAD", "widget": {"name": "upload"}, "link": null}], "outputs": [{"localized_name": "CONTROL_NET", "name": "CONTROL_NET", "type": "CONTROL_NET", "links": [97]}, {"localized_name": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [98]}, {"localized_name": "MASK", "name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "workflow>Load ControlNet Model & Image"}, "widgets_values": ["SDXL\\OpenPoseXL2.safetensors", "standing over.png", "image"]}, {"id": 40, "type": "workflow>SDXL Compact Checkpoint Loader", "pos": [-180, -970], "size": [460, 270], "flags": {}, "order": 1, "mode": 0, "inputs": [{"localized_name": "ckpt_name", "name": "ckpt_name", "type": "COMBO", "widget": {"name": "ckpt_name"}, "link": null}, {"localized_name": "stop_at_clip_layer", "name": "stop_at_clip_layer", "type": "INT", "widget": {"name": "stop_at_clip_layer"}, "link": null}, {"localized_name": "aspect_ratio", "name": "aspect_ratio", "type": "COMBO", "widget": {"name": "aspect_ratio"}, "link": null}, {"localized_name": "batch_size", "name": "batch_size", "type": "INT", "widget": {"name": "batch_size"}, "link": null}], "outputs": [{"localized_name": "MODEL", "name": "MODEL", "type": "MODEL", "links": [72]}, {"localized_name": "VAE", "name": "VAE", "type": "VAE", "links": [103, 107]}, {"localized_name": "CLIP", "name": "CLIP", "type": "CLIP", "links": [74]}, {"localized_name": "LATENT", "name": "LATENT", "type": "LATENT", "links": [102]}], "properties": {"Node name for S&R": "workflow>SDXL Compact Checkpoint Loader"}, "widgets_values": ["Cartoon\\WAI-NSFW-illustrious_v140.safetensors", -2, "2:3", 1]}, {"id": 51, "type": "ControlNetApplyAdvanced", "pos": [480, -840], "size": [270, 186], "flags": {}, "order": 12, "mode": 0, "inputs": [{"localized_name": "positive", "name": "positive", "type": "CONDITIONING", "link": 227}, {"localized_name": "negative", "name": "negative", "type": "CONDITIONING", "link": 106}, {"localized_name": "control_net", "name": "control_net", "type": "CONTROL_NET", "link": 97}, {"localized_name": "image", "name": "image", "type": "IMAGE", "link": 98}, {"localized_name": "vae", "name": "vae", "shape": 7, "type": "VAE", "link": 107}, {"localized_name": "strength", "name": "strength", "type": "FLOAT", "widget": {"name": "strength"}, "link": null}, {"localized_name": "start_percent", "name": "start_percent", "type": "FLOAT", "widget": {"name": "start_percent"}, "link": null}, {"localized_name": "end_percent", "name": "end_percent", "type": "FLOAT", "widget": {"name": "end_percent"}, "link": null}], "outputs": [{"localized_name": "positive", "name": "positive", "type": "CONDITIONING", "links": [108]}, {"localized_name": "negative", "name": "negative", "type": "CONDITIONING", "links": [109]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.41", "Node name for S&R": "ControlNetApplyAdvanced"}, "widgets_values": [0, 0, 1]}, {"id": 18, "type": "CLIPTextEncode", "pos": [400, 150], "size": [400, 250], "flags": {}, "order": 10, "mode": 0, "inputs": [{"localized_name": "clip", "name": "clip", "type": "CLIP", "link": 225}, {"localized_name": "text", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"localized_name": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "links": [106]}], "title": "Negative Prompt (-)", "properties": {"cnr_id": "comfy-core", "ver": "0.3.41", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["bad quality, worst quality, worst detail, sketch, censor, multiple views\n\nmodern, recent, old, oldest, graphic, cartoon, text, painting, crayon, graphite, abstract, glitch, deformed, mutated, ugly, disfigured, long body, lowres, bad anatomy, bad hands, missing fingers, extra digits, fewer digits, cropped, very displeasing, (worst quality, bad quality:1.2), sketch, jpeg artifacts, signature, watermark, username, simple background, conjoined, bad ai-generated,\npuckered lips, eyes, 2 tails, two tails, multiple views, chibi, chibi inset, mini person, muscular, shiny skin, "]}, {"id": 52, "type": "workflow>Ksampler & VAE Decode", "pos": [900, -970], "size": [300, 282], "flags": {}, "order": 13, "mode": 0, "inputs": [{"localized_name": "model", "name": "model", "type": "MODEL", "link": 223}, {"localized_name": "positive", "name": "positive", "type": "CONDITIONING", "link": 108}, {"localized_name": "negative", "name": "negative", "type": "CONDITIONING", "link": 109}, {"localized_name": "latent_image", "name": "latent_image", "type": "LATENT", "link": 102}, {"localized_name": "vae", "name": "vae", "type": "VAE", "link": 103}, {"localized_name": "seed", "name": "seed", "type": "INT", "widget": {"name": "seed"}, "link": null}, {"localized_name": "steps", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": null}, {"localized_name": "cfg", "name": "cfg", "type": "FLOAT", "widget": {"name": "cfg"}, "link": null}, {"localized_name": "sampler_name", "name": "sampler_name", "type": "COMBO", "widget": {"name": "sampler_name"}, "link": null}, {"localized_name": "scheduler", "name": "scheduler", "type": "COMBO", "widget": {"name": "scheduler"}, "link": null}, {"localized_name": "denoise", "name": "denoise", "type": "FLOAT", "widget": {"name": "denoise"}, "link": null}], "outputs": [{"localized_name": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [104]}], "properties": {"Node name for S&R": "workflow>Ksampler & VAE Decode"}, "widgets_values": [720649827108152, "fixed", 20, 5, "euler_ancestral", "karras", 1]}, {"id": 186, "type": "CLIPTextEncode", "pos": [-200, 40], "size": [400, 200], "flags": {}, "order": 11, "mode": 0, "inputs": [{"localized_name": "clip", "name": "clip", "type": "CLIP", "link": 226}, {"localized_name": "text", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"localized_name": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "links": [227]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.41", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["masterpiece, best quality, amazing quality, very aesthetic, high resolution, ultra-detailed, absurdres, newest,\nfellatrix, 1girl, solo, hyenakamalaILL, female, anthro, hyena, body fur, brown hair, makeup, sharp teeth, 2_tone_fur, brown_body, brown_spots, brown eyes, black bra, black booty shorts, jewelry, pearl necklace, earrings, facing viewer, looking at viewer, closed mouth, hands on own hips,, white background"]}, {"id": 192, "type": "ShowText|pysssss", "pos": [-200, -250], "size": [300, 90], "flags": {}, "order": 2, "mode": 0, "inputs": [{"localized_name": "text", "name": "text", "type": "STRING", "link": null}], "outputs": [{"localized_name": "STRING", "name": "STRING", "shape": 6, "type": "STRING", "links": null}], "properties": {"cnr_id": "comfyui-custom-scripts", "ver": "1.2.5", "Node name for S&R": "ShowText|pysssss"}, "widgets_values": ["This is a blank Babynode."]}, {"id": 19, "type": "<PERSON> Lora <PERSON> (rgthree)", "pos": [-200, -640], "size": [500, 190], "flags": {}, "order": 9, "mode": 0, "inputs": [{"dir": 3, "name": "model", "type": "MODEL", "link": 72}, {"dir": 3, "name": "clip", "type": "CLIP", "link": 74}], "outputs": [{"dir": 4, "name": "MODEL", "shape": 3, "type": "MODEL", "links": [223]}, {"dir": 4, "name": "CLIP", "shape": 3, "type": "CLIP", "links": [225, 226]}], "properties": {"cnr_id": "rgthree-comfy", "ver": "1.0.2506081210", "Show Strengths": "Single Strength"}, "widgets_values": [{}, {"type": "PowerLoraLoaderHeaderWidget"}, {"on": true, "lora": "NSFW\\Artist\\Digital\\TIGERACE_fellatrix_ArtStyle_Illustrious.safetensors", "strength": 0.5, "strengthTwo": null}, {"on": true, "lora": "Cartoon\\Character\\Anime\\yeetyah145_<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_Char_Illustrious.safetensors", "strength": 1, "strengthTwo": null}, {"on": true, "lora": "NSFW\\Artist\\Digital\\IWillRemember_Fellatrix_ArtStyle_Illustrious.safetensors", "strength": 1, "strengthTwo": null}, {}, ""]}, {"id": 226, "type": "YOLOWorldModelLoader", "pos": [-1190, -570], "size": [292.3863220214844, 58], "flags": {}, "order": 3, "mode": 0, "inputs": [{"localized_name": "yolo_model", "name": "yolo_model", "type": "COMBO", "widget": {"name": "yolo_model"}, "link": null}], "outputs": [{"localized_name": "yolo_world_model", "name": "yolo_world_model", "type": "YOLO_WORLD_MODEL", "links": null}], "properties": {"Node name for S&R": "YOLOWorldModelLoader"}, "widgets_values": ["yolo_world/s"]}, {"id": 225, "type": "Seed (rgthree)", "pos": [-1200, -370], "size": [320, 130], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"dir": 4, "name": "SEED", "shape": 3, "type": "INT", "links": null}], "properties": {"cnr_id": "rgthree-comfy", "ver": "1.0.2506081210"}, "widgets_values": [-1, "", "", ""]}, {"id": 221, "type": "mxSlider", "pos": [-1500, -370], "size": [200, 30], "flags": {}, "order": 5, "mode": 0, "inputs": [{"localized_name": "Xi", "name": "Xi", "type": "INT", "widget": {"name": "Xi"}, "link": null}, {"localized_name": "Xf", "name": "Xf", "type": "FLOAT", "widget": {"name": "Xf"}, "link": null}, {"localized_name": "isfloatX", "name": "isfloatX", "type": "INT", "widget": {"name": "isfloatX"}, "link": null}], "outputs": [{"localized_name": "", "name": "", "type": "INT", "links": null}], "properties": {"cnr_id": "comfyui-mxtoolkit", "ver": "7f7a0e584f12078a1c589645d866ae96bad0cc35", "Node name for S&R": "mxSlider", "value": 0, "min": 0, "max": 1000, "step": 1, "decimals": 0, "snap": true}, "widgets_values": [0, 0, 0]}, {"id": 239, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [-1860, -500], "size": [270, 126], "flags": {}, "order": 6, "mode": 0, "inputs": [{"localized_name": "model", "name": "model", "type": "MODEL", "link": null}, {"localized_name": "clip", "name": "clip", "type": "CLIP", "link": null}, {"localized_name": "lora_name", "name": "lora_name", "type": "COMBO", "widget": {"name": "lora_name"}, "link": null}, {"localized_name": "strength_model", "name": "strength_model", "type": "FLOAT", "widget": {"name": "strength_model"}, "link": null}, {"localized_name": "strength_clip", "name": "strength_clip", "type": "FLOAT", "widget": {"name": "strength_clip"}, "link": null}], "outputs": [{"localized_name": "MODEL", "name": "MODEL", "type": "MODEL", "links": null}, {"localized_name": "CLIP", "name": "CLIP", "type": "CLIP", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.41", "Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "widgets_values": ["Cartoon\\Artist\\Contemporary\\redtvpe_Range-Murata-Style_ArtStyle_Illustrious.safetensors", 1, 1]}, {"id": 235, "type": "SimpleSliderNode", "pos": [-1500, -270], "size": [200, 30], "flags": {}, "order": 7, "mode": 0, "inputs": [{"localized_name": "int_val", "name": "int_val", "type": "INT", "widget": {"name": "int_val"}, "link": null}, {"localized_name": "float_val", "name": "float_val", "type": "FLOAT", "widget": {"name": "float_val"}, "link": null}, {"localized_name": "use_float", "name": "use_float", "type": "INT", "widget": {"name": "use_float"}, "link": null}], "outputs": [{"localized_name": "", "name": "", "type": "INT", "links": []}], "properties": {"value": 0, "min": 0, "max": 1000, "step": 50, "decimals": 0, "snap": true}, "widgets_values": [0, 0, 0]}, {"id": 241, "type": "SimpleSliderNode", "pos": [-1500, -170], "size": [210, 30], "flags": {}, "order": 8, "mode": 0, "inputs": [{"localized_name": "int_val", "name": "int_val", "type": "INT", "widget": {"name": "int_val"}, "link": null}, {"localized_name": "float_val", "name": "float_val", "type": "FLOAT", "widget": {"name": "float_val"}, "link": null}, {"localized_name": "use_float", "name": "use_float", "type": "INT", "widget": {"name": "use_float"}, "link": null}], "outputs": [{"localized_name": "", "name": "", "type": "INT", "links": null}], "properties": {"value": 20, "min": 0, "max": 100, "step": 1, "decimals": 0, "snap": true}, "widgets_values": [20, 20, 0]}], "links": [[72, 40, 0, 19, 0, "MODEL"], [74, 40, 2, 19, 1, "CLIP"], [97, 50, 0, 51, 2, "CONTROL_NET"], [98, 50, 1, 51, 3, "IMAGE"], [102, 40, 3, 52, 3, "LATENT"], [103, 40, 1, 52, 4, "VAE"], [104, 52, 0, 15, 0, "IMAGE"], [106, 18, 0, 51, 1, "CONDITIONING"], [107, 40, 1, 51, 4, "VAE"], [108, 51, 0, 52, 1, "CONDITIONING"], [109, 51, 1, 52, 2, "CONDITIONING"], [223, 19, 0, 52, 0, "MODEL"], [225, 19, 1, 18, 0, "CLIP"], [226, 19, 1, 186, 0, "CLIP"], [227, 186, 0, 51, 0, "CONDITIONING"]], "groups": [{"id": 2, "title": "Generation", "bounding": [-700, -1300, 2900, 2200], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 2.6333125430608013, "offset": [1638.3869838822536, 291.4765724016814]}, "frontendVersion": "1.22.2", "groupNodes": {"Ksampler & VAE Decode": {"nodes": [{"id": -1, "type": "K<PERSON><PERSON><PERSON>", "pos": [1100, -70], "size": [300, 270], "flags": {}, "order": 3, "mode": 0, "inputs": [{"localized_name": "model", "name": "model", "type": "MODEL", "link": null}, {"localized_name": "positive", "name": "positive", "type": "CONDITIONING", "link": null}, {"localized_name": "negative", "name": "negative", "type": "CONDITIONING", "link": null}, {"localized_name": "latent_image", "name": "latent_image", "type": "LATENT", "link": null}, {"localized_name": "seed", "name": "seed", "type": "INT", "widget": {"name": "seed"}, "link": null}, {"localized_name": "steps", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": null}, {"localized_name": "cfg", "name": "cfg", "type": "FLOAT", "widget": {"name": "cfg"}, "link": null}, {"localized_name": "sampler_name", "name": "sampler_name", "type": "COMBO", "widget": {"name": "sampler_name"}, "link": null}, {"localized_name": "scheduler", "name": "scheduler", "type": "COMBO", "widget": {"name": "scheduler"}, "link": null}, {"localized_name": "denoise", "name": "denoise", "type": "FLOAT", "widget": {"name": "denoise"}, "link": null}], "outputs": [{"localized_name": "LATENT", "name": "LATENT", "type": "LATENT", "links": []}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [0, "randomize", 20, 8, "euler", "normal", 1], "index": 0}, {"id": -1, "type": "VAEDecode", "pos": [1260, -170], "size": [140, 46], "flags": {}, "order": 4, "mode": 0, "inputs": [{"localized_name": "samples", "name": "samples", "type": "LATENT", "link": null}, {"localized_name": "vae", "name": "vae", "type": "VAE", "link": null}], "outputs": [{"localized_name": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": []}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "VAEDecode"}, "index": 1, "widgets_values": []}], "links": [[null, 0, 0, 0, 15, "MODEL"], [null, 0, 0, 1, 21, "CONDITIONING"], [null, 1, 0, 2, 21, "CONDITIONING"], [null, 3, 0, 3, 22, "LATENT"], [0, 0, 1, 0, 11, "LATENT"], [null, 2, 1, 1, 22, "VAE"]], "external": [[1, 0, "IMAGE"]]}, "SDXL Compact Checkpoint Loader": {"nodes": [{"id": -1, "type": "CheckpointLoaderSimple", "pos": [-300, -670], "size": [270, 98], "flags": {}, "order": 0, "mode": 0, "inputs": [{"localized_name": "ckpt_name", "name": "ckpt_name", "type": "COMBO", "widget": {"name": "ckpt_name"}, "link": null}], "outputs": [{"localized_name": "MODEL", "name": "MODEL", "type": "MODEL", "links": []}, {"localized_name": "CLIP", "name": "CLIP", "type": "CLIP", "links": []}, {"localized_name": "VAE", "name": "VAE", "type": "VAE", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.41", "Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["Cartoon\\WAI-NSFW-illustrious_v140.safetensors"], "index": 0}, {"id": -1, "type": "CLIPSetLastLayer", "pos": [-300, -530], "size": [270, 58], "flags": {}, "order": 2, "mode": 0, "inputs": [{"localized_name": "clip", "name": "clip", "type": "CLIP", "link": null}, {"localized_name": "stop_at_clip_layer", "name": "stop_at_clip_layer", "type": "INT", "widget": {"name": "stop_at_clip_layer"}, "link": null}], "outputs": [{"localized_name": "CLIP", "name": "CLIP", "type": "CLIP", "links": []}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.41", "Node name for S&R": "CLIPSetLastLayer"}, "widgets_values": [-2], "index": 1}, {"id": -1, "type": "SDXLAspectRatioSelector", "pos": [-300, -430], "size": [270, 98], "flags": {}, "order": 1, "mode": 0, "inputs": [{"localized_name": "aspect_ratio", "name": "aspect_ratio", "type": "COMBO", "widget": {"name": "aspect_ratio"}, "link": null}], "outputs": [{"localized_name": "ratio", "name": "ratio", "type": "STRING", "links": null}, {"localized_name": "width", "name": "width", "type": "INT", "links": []}, {"localized_name": "height", "name": "height", "type": "INT", "links": []}], "properties": {"cnr_id": "comfyui-art-venture", "ver": "1.0.7", "Node name for S&R": "SDXLAspectRatioSelector"}, "widgets_values": ["1:1"], "index": 2}, {"id": -1, "type": "EmptyLatentImage", "pos": [-300, -270], "size": [270, 106], "flags": {}, "order": 3, "mode": 0, "inputs": [{"localized_name": "width", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": null}, {"localized_name": "height", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": null}, {"localized_name": "batch_size", "name": "batch_size", "type": "INT", "widget": {"name": "batch_size"}, "link": null}], "outputs": [{"localized_name": "LATENT", "name": "LATENT", "type": "LATENT", "links": []}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.41", "Node name for S&R": "EmptyLatentImage"}, "widgets_values": [512, 512, 1], "index": 3}], "links": [[0, 1, 1, 0, 26, "CLIP"], [2, 1, 3, 0, 30, "INT"], [2, 2, 3, 1, 30, "INT"]], "external": [[{"id": -1, "type": "CheckpointLoaderSimple", "pos": [-300, -670], "size": [270, 98], "flags": {}, "order": 0, "mode": 0, "inputs": [{"localized_name": "ckpt_name", "name": "ckpt_name", "type": "COMBO", "widget": {"name": "ckpt_name"}, "link": null}], "outputs": [{"localized_name": "MODEL", "name": "MODEL", "type": "MODEL", "links": []}, {"localized_name": "CLIP", "name": "CLIP", "type": "CLIP", "links": []}, {"localized_name": "VAE", "name": "VAE", "type": "VAE", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.41", "Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["Cartoon\\WAI-NSFW-illustrious_v140.safetensors"], "index": 0}, 0, "MODEL"], [{"id": -1, "type": "CLIPSetLastLayer", "pos": [-300, -530], "size": [270, 58], "flags": {}, "order": 2, "mode": 0, "inputs": [{"localized_name": "clip", "name": "clip", "type": "CLIP", "link": null}, {"localized_name": "stop_at_clip_layer", "name": "stop_at_clip_layer", "type": "INT", "widget": {"name": "stop_at_clip_layer"}, "link": null}], "outputs": [{"localized_name": "CLIP", "name": "CLIP", "type": "CLIP", "links": []}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.41", "Node name for S&R": "CLIPSetLastLayer"}, "widgets_values": [-2], "index": 1}, 0, "CLIP"], [{"id": -1, "type": "EmptyLatentImage", "pos": [-300, -270], "size": [270, 106], "flags": {}, "order": 3, "mode": 0, "inputs": [{"localized_name": "width", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": null}, {"localized_name": "height", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": null}, {"localized_name": "batch_size", "name": "batch_size", "type": "INT", "widget": {"name": "batch_size"}, "link": null}], "outputs": [{"localized_name": "LATENT", "name": "LATENT", "type": "LATENT", "links": []}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.41", "Node name for S&R": "EmptyLatentImage"}, "widgets_values": [512, 512, 1], "index": 3}, 0, "LATENT"]], "config": {"0": {}, "1": {}, "2": {"output": {"0": {"visible": false}}}, "3": {}}}, "Load ControlNet Model & Image": {"nodes": [{"id": -1, "type": "ControlNetLoader", "pos": [470, -670], "size": [270, 58], "flags": {}, "order": 11, "mode": 0, "inputs": [{"localized_name": "control_net_name", "name": "control_net_name", "type": "COMBO", "widget": {"name": "control_net_name"}, "link": null}], "outputs": [{"localized_name": "CONTROL_NET", "name": "CONTROL_NET", "type": "CONTROL_NET", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.41", "Node name for S&R": "ControlNetLoader"}, "widgets_values": ["SDXL\\controlnet-depth-sdxl-1.0\\diffusion_pytorch_model.safetensors"], "index": 0}, {"id": -1, "type": "LoadImage", "pos": [470, -570], "size": [274.080078125, 314], "flags": {}, "order": 12, "mode": 0, "inputs": [{"localized_name": "image", "name": "image", "type": "COMBO", "widget": {"name": "image"}, "link": null}, {"localized_name": "choose file to upload", "name": "upload", "type": "IMAGEUPLOAD", "widget": {"name": "upload"}, "link": null}], "outputs": [{"localized_name": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": null}, {"localized_name": "MASK", "name": "MASK", "type": "MASK", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.41", "Node name for S&R": "LoadImage"}, "widgets_values": ["003122_00001_.png", "image"], "index": 1}], "links": [], "external": []}}}, "version": 0.4}